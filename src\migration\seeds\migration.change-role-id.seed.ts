import { Command } from "nestjs-command";
import { Injectable } from "@nestjs/common";
import { Model, Types } from "mongoose";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { RoleService } from "src/role/services/role.service";
import { RoleCreateRequestDto } from "src/role/dtos/request/role.create.request.dto";
import { UserService } from "src/users/services/user.service";
import { RoleDocument } from "src/role/repository/entities/role.entity";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { User } from "src/users/schemas/user.schema";
import { InjectModel } from "@nestjs/mongoose";

@Injectable()
export class MigrationChangeRoleId {
    constructor(
        @InjectModel(StaffProfileDetails.name) private readonly StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        @InjectModel(User.name) private UserModel: Model<User>,
        private readonly roleService: RoleService,
        private readonly userService: UserService,
    ) {}

    @Command({
        command: "user:role",
        describe: "Change user's role id",
    })
    async seeds(): Promise<void> {
        try {
            const roles = await this.roleService.findAll({}, { select: "_id type" });

            const map = new Map<string, Types.ObjectId>();
            roles.forEach((role) => {
                map.set(role.type, role._id);
            });

            // await Promise.all(promise);
            const users = await (
                await this.userService.model()
            ).collection
                .find({
                    role: { $type: "string" },
                })
                .toArray();

            // Step 3: Update each user's role to corresponding ObjectId
            for (const user of users) {
                const roleId = map.get(user.role);
                if (!roleId) {
                    console.warn(`No matching role found for user ${user._id} with role type ${user.role}`);
                    return;
                }
                await this.userService.updateOne({ _id: user._id }, { role: roleId });
            }
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

    @Command({
        command: "user:organization",
        describe: "Change user's role id",
    })
    async organizationMigration(): Promise<void> {
        try {
            const organizationRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION);
            const superAdminRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.SUPER_ADMIN);
            const users = await this.UserModel.find({ role: { $nin: [organizationRole._id, superAdminRole._id] } }).populate("role", "type");
            console.log(`Found ${users.length} users to update.`);
            for (const user of users) {
                const organizationId = await this.getOrganizationId(user);

                if (!organizationId) {
                    console.warn(`Skipping user ${user._id} due to missing organizationId.`);
                    continue;
                }
                await this.UserModel.updateOne({ _id: user._id }, { $set: { organizationId } });
            }
            console.log("Finished updating users.");
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

    private async getOrganizationId(user: any): Promise<string | null> {
        const { role } = user;
        if (!role || !role.type) {
            console.warn(`User ${user._id} has no valid role`);
            return null;
        }

        let organizationId: any = null;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staff = await this.StaffProfileModel.findOne({ userId: user.id }).exec();
                if (staff) {
                    organizationId = staff.organizationId;
                } else {
                    console.warn(`No staff profile found for user ${user._id}`);
                }
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
                if (client) {
                    organizationId = client.organizationId;
                } else {
                    console.warn(`No client profile found for user ${user._id}`);
                }
                break;
            default:
                console.warn(`Unhandled role type: ${role.type} for user ${user._id}`);
        }
        return organizationId;
    }
}
