import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException, UnauthorizedException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { Organizations } from "src/organization/schemas/organization.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { OrganizationListDto } from "../dto/organization-list.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { AppLoginDto } from "../dto/app-login.dto";
import { JwtService } from "@nestjs/jwt";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { RequestOtpDto } from "../dto/app-request-otp.dto";
import { Otp } from "src/auth/schemas/otp.schema";
import { AuthTypes } from "src/utils/enums/auth.enum";
import { GeneralService } from "src/utils/services/general.service";
import { MailService } from "src/mail/services/mail.service";
import * as path from "path";
import { VerifyOtpDto } from "../dto/app-verifyOtp";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { OrganizationsListDto } from "../dto/organizations-list.dto";
import { DeleteAccountDto } from "../dto/deleteAccoutn.dto";
import { SetPasswordDto } from "../dto/set-password.dto";
import * as bcrypt from "bcrypt";
import { Msg91Service } from "src/message/service/msg91.service";
import { AuthService } from "src/auth/services/auth.service";
import { RoleService } from "src/role/services/role.service";

@Injectable()
export class MobileAuthService {
    constructor(
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Otp.name) private OtpModel: Model<Otp>,
        private readonly roleService: RoleService,
        private readonly authService: AuthService,
        private readonly transactionService: TransactionService,
        private JwtService: JwtService,
        private readonly generalService: GeneralService,
        private readonly mailService: MailService,
        private readonly msg91Service: Msg91Service,
    ) { }
    private async sendMail(email: string, template: string, context?: object, attachments?: any) {
        try {
            await this.mailService.sendMail({
                to: email?.toString(),
                subject: "OTP for email verification",
                template: template,
                context: context,
                attachments: attachments,
            });
            return "OTP Sent.Please check your mail!";
        } catch (emailError) {
            console.error("Failed to send OTP email:", emailError.message);
            throw new Error("Failed to send OTP email. Please try again.");
        }
    }
    async organizationList(organizationListDto: OrganizationListDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let search = "";
            if (organizationListDto.search) {
                search = organizationListDto.search.trim().split(" ").join("|");
            }
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION, {
                select: "_id",
            });
            if (!role) throw new BadRequestException("Organization role not found");

            const matchConditions: Record<string, any> = {
                role: role._id,
                isActive: true,
            };

            if (search) {
                matchConditions.$or = [{ name: { $regex: search, $options: "i" } }, { email: { $regex: search, $options: "i" } }, { mobile: { $regex: search, $options: "i" } }];
            }

            const pipeline: PipelineStage[] = [
                { $match: matchConditions },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "_id",
                        foreignField: "userId",
                        as: "organizationDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$organizationDetails",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $project: {
                        organizationName: "$name",
                        _id: 1,
                    },
                }
            ];

            const result = await this.UserModel.aggregate(pipeline).session(session);

            await this.transactionService.commitTransaction(session);

            return {
                message: "Organization list fetched successfully",
                data: {
                    list: result,
                    count: result.length,
                },
            };
        } catch (error) {
            if (session.inTransaction()) {
                await this.transactionService.abortTransaction(session);
            }

            throw new InternalServerErrorException("Failed to fetch organization list");
        } finally {
            await session.endSession();
        }
    }

    async organizationsList(organizationListDto: OrganizationsListDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let search = "";
            if (organizationListDto.search) {
                search = organizationListDto.search.trim().split(" ").join("|");
            }
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION, {
                select: "_id",
            });
            if (!role) throw new BadRequestException("Organization role not found");
            const matchConditions: Record<string, any> = {
                role: role._id,
                isActive: true
            };
            if (search) {
                matchConditions.$or = [{ name: { $regex: search, $options: "i" } }, { email: { $regex: search, $options: "i" } }, { mobile: { $regex: search, $options: "i" } }];
            }
            const pipeline: PipelineStage[] = [
                { $match: matchConditions },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "_id",
                        foreignField: "userId",
                        as: "organizationDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$organizationDetails",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $project: {
                        organizationName: "$name",
                        _id: 1,
                        banner: "$organizationDetails.logo",
                    },
                },
            ];
            const result = await this.UserModel.aggregate(pipeline).session(session);
            await this.transactionService.commitTransaction(session);
            return {
                message: "Organization list fetched successfully",
                data: {
                    list: result,
                    count: result.length,
                },
            };
        } catch (error) {
            if (session.inTransaction()) {
                await this.transactionService.abortTransaction(session);
            }

            throw new InternalServerErrorException("Failed to fetch organization list");
        } finally {
            await session.endSession();
        }
    }
    async createOtp(requestOtpDto: RequestOtpDto, organizationId: string): Promise<void | Number> {
        try {
            return await this.generateOTP(requestOtpDto, organizationId);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    private async generateOTP(requestOtpDto: RequestOtpDto, organizationId: String): Promise<any> {
        try {
            const identifier = requestOtpDto[requestOtpDto.type].toLowerCase();

            const user: IUserDocument = await (
                await this.UserModel.findOne({
                    [requestOtpDto.type]: identifier,
                    organizationId,
                })
            ).populate([{ path: "role", select: "type" }]);

            if (!user || user.isActive === false) {
                const organizationDetail = await this.UserModel.findOne({
                    _id: requestOtpDto.organizationId,
                });
                throw new BadRequestException(`You're not registered yet. Please contact ${organizationDetail.name} to get access`);
            }
            const userName = (user?.firstName || user?.lastName ? `${user.firstName || ""} ${user.lastName || ""}` : user?.name);

            const allowedRolesForOrgCheck = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            let resolvedOrganizationId = null;

            if (allowedRolesForOrgCheck.includes(user.role.type)) {
                const staffProfile = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { facilityId: 1, organizationId: 1 }
                );

                const facilityArray = staffProfile?.facilityId?.length ? staffProfile.facilityId.map((facility) => facility) : [];

                const facility = await this.FacilityModel.findOne({ _id: { $in: facilityArray } }, { organizationId: 1 });

                resolvedOrganizationId = facility?.organizationId || null;

                if (!resolvedOrganizationId || resolvedOrganizationId.toString() !== requestOtpDto.organizationId?.toString()) {
                    throw new BadRequestException("Trainer or Front Desk Admin does not belong to this organization");
                }
            }
            if (requestOtpDto.type === AuthTypes.MOBILE) {
                const generatedOtp = this.generalService.randomOTP();

                // Save or update in DB
                const existingRecord = await this.OtpModel.findOne({
                    for: identifier,
                    organizationId: requestOtpDto.organizationId,
                });

                if (existingRecord) {
                    existingRecord.otp = generatedOtp;
                    await existingRecord.save();
                } else {
                    await new this.OtpModel({
                        otp: generatedOtp,
                        for: identifier,
                        organizationId: requestOtpDto.organizationId,
                    }).save();
                }

                // Send OTP via MSG91
                await this.msg91Service.sendOtp(identifier, generatedOtp.toString()); // pass OTP to template

                return;
            }
            const record = await this.OtpModel.findOne({
                for: requestOtpDto[requestOtpDto.type].toLowerCase(),
                organizationId: requestOtpDto.organizationId,
            }).exec();
            const now = new Date();
            const tenMinutesAgo = new Date(now.getTime() - 1 * 60 * 1000);

            if (record) {
                if (record.updatedAt < tenMinutesAgo || !record.otp) {
                    record.otp = this.generalService.randomOTP();
                    await record.save();
                }
                if (requestOtpDto.type === AuthTypes.EMAIL) {
                    let res = this.sendMail(requestOtpDto?.email, "request-otp-without-image", { otp: record.otp, name: userName ?? "User" }, [
                        // {
                        //     filename: "otp-image.png",
                        //     path: path.resolve(__dirname, "../../../image/otp-image.png"),
                        //     cid: "verificationImage",
                        // },
                    ]);
                    return res;
                }
                return record.otp;
            } else {
                const createdOtp = new this.OtpModel({
                    otp: this.generalService.randomOTP(),
                    for: requestOtpDto[requestOtpDto.type].toLowerCase(),
                    organizationId: requestOtpDto.organizationId,
                });
                await createdOtp.save();

                if (requestOtpDto.type === AuthTypes.EMAIL) {
                    let res = this.sendMail(requestOtpDto?.email, "request-otp-without-image", { otp: createdOtp.otp, name: userName ?? "User" }, [
                        // {
                        //     filename: "otp-image.png",
                        //     path: path.resolve(__dirname, "../../../image/otp-image.png"),
                        //     cid: "verificationImage",
                        // },
                    ]);
                    return res;
                }

                return createdOtp.otp;
            }
        } catch (error) {
            throw new Error(error.message);
        }
    }
    async verifyOtp(verifyOtpDto: VerifyOtpDto, organizationId: string): Promise<any> {
        try {
            const identifier = verifyOtpDto[verifyOtpDto.type].toLowerCase();
            if (verifyOtpDto.type === AuthTypes.MOBILE) {
                const msg91Verified = await this.msg91Service.verifyOtp(identifier, verifyOtpDto.otp.toString());
                if (!msg91Verified) {
                    throw new BadRequestException("Invalid or expired OTP");
                }
            }

            const record = await this.OtpModel.findOne({
                for: identifier,
                otp: verifyOtpDto.otp,
                organizationId: verifyOtpDto.organizationId,
            }).exec();

            if (!record) {
                throw new BadRequestException("Invalid OTP");
            }

            const now = new Date();
            const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

            if (record.updatedAt <= tenMinutesAgo) {
                throw new BadRequestException("OTP has expired");
            }

            const user: IUserDocument = await (await this.UserModel.findOne({ [verifyOtpDto.type]: identifier, organizationId })).populate([{ path: "role", select: "type" }]);
            const isPasswordSet = user?.password ? true : false;

            if (!user) {
                record.otp = null;
                await record.save();
                return {
                    message: "OTP verified",
                    data: {
                        otpVerificationCode: record._id,
                        userExist: false,
                        forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                    },
                };
            }

            const allowedRolesForOrgCheck = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            if (allowedRolesForOrgCheck.includes(user.role.type)) {
                const staffProfile = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { facilityId: 1 }
                );

                const facilityIds = staffProfile?.facilityId || [];
                const facility = await this.FacilityModel.findOne({ _id: { $in: facilityIds } }, { organizationId: 1 });

                const resolvedOrgId = facility?.organizationId;

                if (!resolvedOrgId || resolvedOrgId.toString() !== verifyOtpDto.organizationId.toString()) {
                    throw new BadRequestException("Trainer or Front Desk Admin does not belong to this organization");
                }
            }

            user.password = user.salt = undefined;
            // const accessToken = this.JwtService.sign({ userId: user._id });
            const tokenData = await this.authService.createToken(user, verifyOtpDto.organizationId);

            if (user.role.type === ENUM_ROLE_TYPE.USER) {
                const imageDetails = await this.ClientModel.findOne({ userId: user._id }, { photo: 1 });
                user["_doc"]["photo"] = imageDetails?.photo || "";
            }
            if (!verifyOtpDto?.forgotPasswordRequest) {
                await this.OtpModel.findByIdAndDelete(record._id);
            }
            return {
                message: "OTP verified",
                data: {
                    otpVerificationCode: record._id,
                    userExist: true,
                    forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                    user: {
                        ...user.toJSON(),
                        role: user.role.type,
                        // roleObj: user.role,
                    },
                    profiles: [],
                    accessToken: tokenData.accessToken,
                    isPasswordSet: isPasswordSet,
                    roleType: user.role.type,
                    tokenType: tokenData.tokenType,
                    loginDate: tokenData.loginDate,
                    session: null,
                    organizationId: verifyOtpDto.organizationId,
                },
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }
    async forgetPasswordOtp(requestOtpDto: RequestOtpDto, organizationId: string): Promise<void | Number> {
        try {
            const user = await this.UserModel.findOne({ [requestOtpDto.type]: requestOtpDto[requestOtpDto.type], organizationId }).exec();

            if (!user) throw new BadRequestException(`Account with this ${requestOtpDto.type} does not exist.`);

            return await this.generateOTP(requestOtpDto, organizationId);
        } catch (error) {
            throw new Error(error.message);
        }
    }
    async deleteAccount(deleteAccountDto: DeleteAccountDto): Promise<any> {
        try {
            if (deleteAccountDto.role === ENUM_ROLE_TYPE.USER) {
                const userDetails: any = await this.ClientModel.findOne({ userId: deleteAccountDto.userId }).exec();
                const user = await this.UserModel.findOne({ _id: deleteAccountDto.userId }).exec();
                if (!userDetails) throw new NotFoundException("User not found");

                const organizationDetail = await this.UserModel.findOne({ _id: userDetails.organizationId }).exec();
                if (!organizationDetail) throw new NotFoundException("Organization not found");

                const staffProfiles = await this.StaffProfileModel.find({
                    facilityId: userDetails.facilityId,
                }).exec();
                const userIds = staffProfiles.map((staff) => staff.userId);

                const facilityWebMaster = await this.UserModel.findOne({
                    _id: { $in: userIds },
                    role: ENUM_ROLE_TYPE.WEB_MASTER, // or "webMaster"
                }).exec();
                const facilityDetails = await this.FacilityModel.findOne({ _id: userDetails.facilityId }).exec();
                const dataForMail = {
                    userName: user.name,
                    facilityName: facilityDetails.facilityName,
                    organizationName: organizationDetail.name,
                    userId: user._id,
                    roleType: deleteAccountDto.role,
                };

                if (facilityWebMaster) {
                    this.mailService.sendMail({
                        to: facilityWebMaster.email.toString(),
                        subject: `Account Deletion Notice`,
                        template: "account-deletion-request",
                        context: dataForMail,
                    });
                }

                this.mailService.sendMail({
                    to: organizationDetail.email.toString(),
                    subject: `Account Deletion Request`,
                    template: "account-deletion-request",
                    context: dataForMail,
                });
            }
            if (deleteAccountDto.role === ENUM_ROLE_TYPE.TRAINER) {
                const user = await this.UserModel.findOne({ _id: deleteAccountDto.userId }).exec();
                const staffProfiles: any = await this.StaffProfileModel.findOne({
                    userId: deleteAccountDto.userId,
                }).exec();
                const organizationDetail = await this.UserModel.findOne({ _id: staffProfiles.organizationId }).exec();
                if (!organizationDetail) throw new NotFoundException("Organization not found");

                const allStaffList = await this.StaffProfileModel.find({
                    facilityId: staffProfiles.facilityId[0],
                }).exec();
                const userIds = allStaffList.map((staff) => staff.userId);
                const facilityWebMaster = await this.UserModel.find({
                    _id: { $in: userIds },
                    role: ENUM_ROLE_TYPE.WEB_MASTER, // or "webMaster"
                }).exec();
                const facilityDetails = await this.FacilityModel.findOne({ _id: staffProfiles.facilityId[0] }).exec();
                const finalRole = deleteAccountDto.role === ENUM_ROLE_TYPE.TRAINER ? 'Trainer' : 'User'
                const dataForMail = {
                    userName: user?.firstName + " " + user?.lastName,
                    facilityName: facilityDetails.facilityName,
                    organizationName: organizationDetail.name,
                    userId: user._id,
                    roleType: deleteAccountDto.role,
                    role: finalRole,
                };

                if (facilityWebMaster.length) {
                    const emailList = facilityWebMaster.map((wm) => wm.email.toString());

                    for (const email of emailList) {
                        await this.mailService.sendMail({
                            to: email,
                            subject: `Account Deletion Notice`,
                            template: "account-deletion-request",
                            context: dataForMail,
                        });
                    }
                }
                this.mailService.sendMail({
                    to: organizationDetail.email.toString(),
                    subject: `Account Deletion Request`,
                    template: "account-deletion-request",
                    context: dataForMail,
                });
            }
        } catch (error) {
            throw new InternalServerErrorException(error.message);
        }
    }
    async setTrainerPassword(setPasswordDto: SetPasswordDto, user: any): Promise<any> {
        try {
            const userDetails = await this.UserModel.findOne({ _id: user._id, role: ENUM_ROLE_TYPE.TRAINER }).exec();
            if (!userDetails) throw new NotFoundException("User not found");
            if (userDetails.password) throw new UnauthorizedException("Password already set");
            if (userDetails.isActive === false) throw new UnauthorizedException("User Account is disabled");
            if (setPasswordDto.newPassword !== setPasswordDto.confirmPassword) throw new BadRequestException("Password and confirm password does not match");
            const salt = await bcrypt.genSalt();
            const hashedPassword = await this.generalService.hashPassword(setPasswordDto.newPassword, salt);
            const updatedUser = await this.UserModel.findOneAndUpdate(
                { _id: user._id },
                {
                    $set: {
                        password: hashedPassword,
                        salt: salt,
                    },
                },
            );
            return updatedUser;
        } catch (error) {
            throw new InternalServerErrorException(error.message);
        }
    }
}
