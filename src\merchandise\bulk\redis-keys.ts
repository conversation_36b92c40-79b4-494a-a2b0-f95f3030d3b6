// src/merchandise/bulk/redis-keys.ts
export const BATCH_TTL_SECONDS = 7 * 24 * 3600; // 7 days

export function rkeys(batchId: string) {
  return {
    stream:  `batch:${batchId}:stream`,
    stats:   `batch:${batchId}:stats`,
    row:     (rowId: string) => `batch:${batchId}:row:${rowId}`,
    rowPattern: `batch:${batchId}:row:*`,      // ⬅ add this
    group:   `g:${batchId}`,
    errors:  `batch:${batchId}:errors`,
  };
}
