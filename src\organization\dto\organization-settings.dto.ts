import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsBoolean, IsArray, <PERSON>Int, <PERSON>, <PERSON>, <PERSON>idate<PERSON><PERSON> } from "class-validator";
import { Type } from "class-transformer";

export class RenewalReminderDto {
    @ApiProperty({
        description: "Whether renewal reminders are enabled",
        example: true,
        required: false,
        default: false,
    })
    @IsOptional()
    @IsBoolean()
    enabled?: boolean;

    @ApiProperty({
        description: "Days before expiry to send reminder (1–7)",
        example: [1, 3, 7],
        required: false,
    })
    @ValidateIf((o) => o.enabled === true)
    @IsArray()
    @Type(() => Number)
    @IsInt({ each: true })
    @Min(1, { each: true })
    @Max(7, { each: true })
    daysBefore?: number[];

    @ApiProperty({
        description: "Days after expiry to send reminder (1–3)",
        example: [1, 2],
        required: false,
    })
    @ValidateIf((o) => o.enabled === true)
    @IsArray()
    @Type(() => Number)
    @IsInt({ each: true })
    @Min(1, { each: true })
    @Max(3, { each: true })
    daysAfter?: number[];
}

export class OrganizationSettingsDto {
    @ApiProperty({
        description: "Client onboarding details",
        example: "{}",
        required: false,
    })
    @IsOptional()
    clientOnboarding?: object;

    @ApiProperty({
        description: "Staff onboarding details.",
        example: "{}",
        required: false,
    })
    @IsOptional()
    staffOnboarding?: object;

    @ApiProperty({
        description: "Is the organization inclusive of GST for all pricing setting",
        example: true,
        required: false,
    })
    @IsOptional()
    @Type(() => Boolean)
    @IsBoolean()
    isInclusiveofGst?: boolean;

    @ApiProperty({
        description: "Renewal reminder settings",
        type: () => RenewalReminderDto,
        required: false,
    })
    @IsOptional()
    @Type(() => RenewalReminderDto)
    renewalReminder?: RenewalReminderDto;
}