import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { Pricing } from 'src/organization/schemas/pricing.schema';

@Injectable()
export class MigrationVoucher {
    constructor(
        @InjectModel(Purchase.name) private PurchaseModel: Model<PurchaseDocument>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
    ) { }

    @Command({
        command: 'package:item-type',
        describe: 'Add consumers to purchase',
    })
    async seeds(): Promise<void> {

        try {
            const purchases = await this.PurchaseModel.updateMany({ itemType: { $exists: false } }, { itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE });
            const pricings = await this.PricingModel.updateMany({ itemType: { $exists: false } }, { itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE });
            console.log(`Updated ${purchases.modifiedCount} purchases and ${pricings.modifiedCount} pricings`);
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }
}
