import { NestFactory } from "@nestjs/core";
import { MongooseModule } from "@nestjs/mongoose";
import { Module, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import dotenv from "dotenv";
import { Organizations, OrganizationSchema } from "src/organization/schemas/organization.schema";

import { PaymentMethod, PaymentMethodSchema } from "../paymentMethod/schemas/payment-method.schema";
import { Facility, FacilityDocument, FacilitySchema } from "src/facility/schemas/facility.schema";

dotenv.config();

// MongoDB Connection URL
// const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_URI = process.env.MONGODB_URI;
@Injectable()
class policyMethodService {
    constructor(@InjectModel(Organizations.name) private organizationModel: Model<Organizations>) { }

    async insertPolicyMethods() {
        try {
            const organizations = await this.organizationModel.find({});

            const defaultPolicies = [
                {
                    name: 'Facility Waiver',
                    required: false,
                    isShown: true,
                },
                {
                    name: 'Safety Briefing Done',
                    required: false,
                    isShown: true,
                },
                {
                    name: 'Check For ID',
                    required: false,
                    isShown: true,
                },
            ];
            for (const org of organizations) {
                org.set('clientOnboarding.showPolicies', true);
                org.set('clientOnboarding.policies.items', defaultPolicies);

                await org.save();

                console.log(`✅ Updated organization ${org}`);
            }

            console.log('✅ All organizations updated with default policies.');
        } catch (error) {
            console.error('❌ Error inserting policy methods:', error);
        }
    }

}

@Module({
    imports: [
        MongooseModule.forRoot(MONGODB_URI),
        MongooseModule.forFeature([
            { name: Organizations.name, schema: OrganizationSchema },
        ]),
    ],
    providers: [policyMethodService], // Register the service properly
})
class AppModule { }

async function bootstrap() {
    const app = await NestFactory.createApplicationContext(AppModule);
    const paymentMethodService = app.get(policyMethodService);
    await paymentMethodService.insertPolicyMethods();
    await app.close();
}

bootstrap();
