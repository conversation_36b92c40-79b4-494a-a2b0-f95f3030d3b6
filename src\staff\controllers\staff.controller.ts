import { Body, Controller, HttpCode, Patch, Post, Req, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiTags, ApiBearerAuth, ApiOperation } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffService } from "../services/staff.service";
import { CompleteStaffProfileDto } from "../dto/complete-staff-profile.dto.ts";
import { ResetPasswordDto } from "../dto/reset-password.dto";
import { FacilityClientListDto } from "../dto/facility-client-list.dto";
import { ValidatePinDto } from "../dto/validate-pin.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { AuthFullProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response } from "src/common/response/decorators/response.decorator";

@ApiTags("Staff")
@ApiBearerAuth()
@Controller("staff")
export class StaffController {
    constructor(private staffService: StaffService) {}

    @Post("/completeProfile")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @ApiOperation({ summary: "Complete/Update staff profile details Only By Staff" })
    async completeStaffDetails(@Body() completeStaffDto: CompleteStaffProfileDto, @GetUser() user): Promise<any> {
        return await this.staffService.completeStaffDetails(completeStaffDto, user);
    }

    @Patch("/reset-password")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @ApiOperation({ summary: "Reset password of the staff member" })
    async resetPassword(@GetUser() user, @Body() resetPasswordDto: ResetPasswordDto) {
        const { oldPassword, newPassword, confirmPassword } = resetPasswordDto;
        return this.staffService.resetPassword(user._id, oldPassword, newPassword, confirmPassword);
    }

    @Post("/trainer/client/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Trainer's client list" })
    async trainerClientList(@GetUser() user, @Body() facilityClientListDto: FacilityClientListDto): Promise<any> {
        return await this.staffService.trainerClientList(facilityClientListDto, user);
    }


    @ApiOperation({ summary: 'Set user PIN' })
    @Response('Pin changed successfully')
    @AuthFullProtected()
    @Post('pin/set')
    async setPin(
        @Body() setPinDto: ValidatePinDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        await this.staffService.setPin(organizationId, setPinDto);
        return true
    }

    // @ApiOperation({ summary: 'Reset user PIN' })
    // @Response('Pin changed successfully')
    // @AuthFullProtected()
    // @Post('pin/reset')
    // async resetPin(
    //     @Body() setPinDto: ValidatePinDto,
    //     @GetOrganizationId() organizationId: IDatabaseObjectId
    // ) {
    //     await this.staffService.setPin(organizationId, setPinDto);
    //     return true
    // }

}
