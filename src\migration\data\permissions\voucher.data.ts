
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const voucherPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Voucher Write",
        type: ENUM_PERMISSION_TYPE.VOUCHER_WRITE,
        description: 'Grant voucher write access',
        isDelegated: true,
    },
    {
        name: "Voucher Read",
        type: ENUM_PERMISSION_TYPE.VOUCHER_READ,
        description: 'Grant voucher read access',
        isDelegated: true,
    },
    {
        name: "Voucher Update",
        type: ENUM_PERMISSION_TYPE.VOUCHER_UPDATE,
        description: 'Grant voucher update access',
        isDelegated: true,
    },
    {
        name: "Voucher Delete",
        type: ENUM_PERMISSION_TYPE.VOUCHER_DELETE,
        description: 'Grant voucher delete access',
        isDelegated: true,
    },
];

