import { Module } from '@nestjs/common';
import { SchedulingModule } from 'src/scheduling/scheduling.module';

/**
 * RouterModule - A container module for organizing related functionality
 *
 * This module serves as a grouping mechanism for related modules, making the
 * main AppModule cleaner and providing better organization of features.
 *
 * Currently includes:
 * - SchedulingModule: Handles appointment scheduling functionality
 *
 * To add more modules to this router:
 * 1. Import the module at the top
 * 2. Add it to the imports array
 * 3. Add it to the exports array to make it available to the app
 *
 * Example:
 * import { AppointmentModule } from 'src/appointment/appointment.module';
 *
 * Then add AppointmentModule to both imports and exports arrays
 */
@Module({
    imports: [
        // Scheduling related modules
        SchedulingModule,

        // Future modules can be added here
        // AppointmentModule,
        // RoomModule,
        // etc.
    ],
    controllers: [],
    providers: [],
    exports: [
        // Re-export modules to make them available to the app
        SchedulingModule,

        // Future modules should also be exported here
        // AppointmentModule,
        // RoomModule,
        // etc.
    ],
})
export class RouterModule { }
