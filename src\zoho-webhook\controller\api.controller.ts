import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiKeyService } from '../services/api-key.service';
import { CreateApiKeyDto } from '../dto/create_apiKey.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { RolesGuard } from 'src/auth/roles.guard';
import { AuthGuard } from '@nestjs/passport';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
@ApiTags("API key generation")
@ApiBearerAuth()
@Controller('api-key')
export class ApiKeyController {
    constructor(private readonly apiKeyService: ApiKeyService) { }

    @Post('/create')
    @ApiOperation({ summary: "Create a new api for webhook" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async createApiKey(@Body() createApiKeyDto: CreateApiKeyDto) {
        try {
            const { rawKey } = await this.apiKeyService.generateAndStoreApiKey(createApiKeyDto);
            return {
                message: 'API key generated successfully',
                apiKey: rawKey,

            };
        } catch (error) {
            throw new Error('Error generating API key');
        }
    }
}
