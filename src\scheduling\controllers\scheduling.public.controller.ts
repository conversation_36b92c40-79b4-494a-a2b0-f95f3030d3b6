import { BadRequestException, Body, Controller, Get, Param, Patch, Post, HttpCode, Headers, StreamableFile, Query } from "@nestjs/common";
import { SchedulingService } from "../services/scheduling.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateSchedulingDto, SchedulingParamDto, SchedulingQueryDto } from "../dto/create-scheduling.dto";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response } from "src/common/response/decorators/response.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { GetSchedulesByUserDto } from "../dto/get-scheduling-user.dto";
import { plainToInstance } from 'class-transformer';


@ApiTags("module.scheduling")
@ApiBearerAuth()
@Controller("public/scheduling")
export class SchedulingPublicController {
    constructor(private schedulingService: SchedulingService) { }

    @Response("scheduling.create.personalAppointment")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/") 
    async schedulingPersonalAppointment(
        @GetUser() user: any,
        @Query() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null;

        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            data = await this.schedulingService.schedulePersonalAppointment(createSchedulingDto, user);
        } else if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.scheduleSession(createSchedulingDto, user);
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return { data };
    }

    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/create") 
    async createScheduling(
        @GetUser() user: any,
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null;

        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            data = await this.schedulingService.schedulePersonalAppointment(createSchedulingDto, user);
        } else if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.scheduleSession(createSchedulingDto, user);
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return { data };
    }
    @Response("scheduling.create.personalAppointment")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/:organizationId/:facilityId/:clientId/:classType")
    async createSchedulingV1(
        @GetUser() user: any,
        @Param() params: SchedulingParamDto,
        @Query() query: SchedulingQueryDto
    ): Promise<any> {
        const { classType } = params;

        const mergedDto = plainToInstance(CreateSchedulingDto, {
            ...params,
            ...query,
        });
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            data = await this.schedulingService.schedulePersonalAppointment(
                mergedDto,
                user
            );
        } else if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.scheduleSession(mergedDto, user);
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return { data };
    }

    @Response('scheduling.update.personalAppointment')
    @Patch("/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update personal appointment" })
    async editSchedulingPersonalAppointment(
        @GetUser() user: any,
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.updatePersonalAppointment(updateSchedulingDto, user)
        } else if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.updateSession(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            data: data,
        };
    }

    @Post("/get/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get scheduling list" })
    async getSchedulingList(
        @GetUser() user: IUserDocument,
        @Body() body: GetSchedulesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesList(user, organizationId, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data
        }
    }
}