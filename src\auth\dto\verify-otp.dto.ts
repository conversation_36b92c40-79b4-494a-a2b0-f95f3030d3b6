import { IsEmail, IsEnum, Validate<PERSON>f, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsNotEmpty, IsBoolean, IsNumber, IsO<PERSON><PERSON> } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class VerifyOtpDto {
    @ApiProperty({
        description: "The method of authentication for OTP verification. Can be either EMAIL or MOBILE.",
        enum: AuthTypes,
        example: AuthTypes.EMAIL,
    })
    @IsNotEmpty({ message: "Authentication type is required. Can be either email or mobile." })
    @IsEnum([AuthTypes.EMAIL, AuthTypes.MOBILE], { message: "Authentication type must be either EMAIL or MOBILE." })
    type: string;

    @ApiProperty({
        description: "The user email address. Required if the authentication type is EMAIL.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @ValidateIf((req) => req.type === AuthTypes.EMAIL)
    @Transform((param) => param.value.toLowerCase())
    @IsEmail({}, { message: "Invalid email format." })
    @MaxLength(255, { message: "Email can be at most 255 characters long." })
    email: string;

    @ApiProperty({
        description: "The user mobile number. Required if the authentication type is MOBILE.",
        example: "9876543210",
        // minLength: 10,
        // maxLength: 10,
        required: false,
    })
    @IsOptional()
    @ValidateIf((req) => req.type === AuthTypes.MOBILE)
    //@Length(10, 10, { message: "Mobile number must be exactly 10 digits." })
    mobile: string;

    @ApiProperty({
        description: "ForgotPasswordRequest is TRUE when forgetting the password, otherwise it's FALSE.",
        example: false,
        required: true,
    })
    @IsNotEmpty({ message: "ForgotPasswordRequest value is required." })
    @IsBoolean({ message: "ForgotPasswordRequest value must be TRUE or FALSE." })
    forgotPasswordRequest: boolean;

    @ApiProperty({
        description: "The 6-digit OTP sent to the user. Must be a number between 100000 and 999999.",
        example: 123456,
        minimum: 100000,
        maximum: 999999,
    })
    @IsNotEmpty({ message: "OTP is required." })
    @IsNumber({},{message:"OTP should be a Number."})
    @Min(100000, { message: "OTP must be at least 100000." })
    @Max(999999, { message: "OTP must be at most 999999." })
    otp: number;
}