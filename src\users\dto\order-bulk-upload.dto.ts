// src/orders/dto/bulk-create-orders.dto.ts
import { Type } from 'class-transformer';
import {
  ArrayMinSize, IsArray, IsDateString, IsMongoId, IsNotEmpty,
  IsNumber, IsString, ValidateNested, Min, IsInt,
} from 'class-validator';

export class BulkOrderItemDto {
  @IsMongoId()
  itemId!: string;                // ITEM

  @IsInt()
  @Min(1)
  quantity!: number;              // QTY

  @IsDateString()
  startDate!: string;             // START DATE

  @IsDateString()
  endDate!: string;               // END DATE

  @IsNumber()
  @Min(0)
  packagePrice!: number;          // PACKAGE PRICE

  @IsNumber()
  @Min(0)
  amountCollected!: number;       // AMOUNT COLLECTED

  // ⬇️ from frontend object (use `name` as label, and `paymentMethodId` for DB)
  @IsString()
  @IsNotEmpty()
  paymentMethod!: string;         // e.g., "Split Payment"

  @IsMongoId()
  paymentMethodId!: string;       // e.g., "67dd5bc22d59660d595a684f"
}

export class ClientBulkOrderDto {
  @IsMongoId()
  clientId!: string;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => BulkOrderItemDto)
  items!: BulkOrderItemDto[];
}

export class BulkCreateOrdersDto {
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => ClientBulkOrderDto)
  orders!: ClientBulkOrderDto[];
}
