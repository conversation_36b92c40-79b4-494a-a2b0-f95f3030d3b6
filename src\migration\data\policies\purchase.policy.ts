import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from "src/policy/enums/policy.enum";
import { IDefaultPolicies } from "src/policy/interfaces/policy.interface";

export const purchasePolicies: IDefaultPolicies[] = [
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.PURCHASE_PURCHASE],
        description: "Grants update (start package) access",
        isActive: true,
        module: ENUM_POLICY_MODULE.PURCHASE,
        type: ENUM_POLICY_TYPE.PURCHASE_PURCHASE,
        action: ENUM_POLICY_ACTION.UPDATE,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.PURCHASE_PURCHASE],
        description: "Grants full access (start date, end date, and session count of package)",
        isActive: true,
        module: ENUM_POLICY_MODULE.PURCHASE,
        type: ENUM_POLICY_TYPE.PURCHASE_PURCHASE,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];
