import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEnum, IsInt, IsMongoId, IsNot<PERSON>mpty, IsO<PERSON>al, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetPricingByServiceSubtypeDto {
    @ApiProperty({
        description: "The class type of the service category. Must be a valid enum value.",
        example: ClassType.BOOKINGS,
        required: true,
    })
    @IsNotEmpty({ message: "Class type is required." })
    @IsEnum(ClassType, { message: "Invalid class type provided." })
    classType: ClassType;

    @ApiProperty({
        description: "Unique identifier for the client user. Must be a valid MongoDB ObjectId.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Client User ID is required." })
    @IsMongoId({ message: "Invalid Client User ID format. Must be a valid MongoDB ObjectId." })
    clientUserId: string;

    @ApiProperty({
        description: "Unique identifier for the service category. Must be a valid MongoDB ObjectId.",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Service Category ID is required." })
    @IsMongoId({ message: "Invalid Service Category ID format. Must be a valid MongoDB ObjectId." })
    serviceCategoryId: string;

    @ApiProperty({
        description: "Unique identifier for the service sub-type. Must be a valid MongoDB ObjectId.",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "SubType ID is required." })
    @IsMongoId({ message: "Invalid SubType ID format. Must be a valid MongoDB ObjectId." })
    subTypeId: string;

    @ApiProperty({
        description: "Optional search keyword. Must be a string.",
        example: "Knox",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search query must be a valid string." })
    search?: string;

    @ApiProperty({
        description: "Number of records per page. Must be an integer between 1 and 50.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "Page size is required." })
    @IsInt({ message: "Page size must be a valid integer." })
    @Min(1, { message: "Page size must be at least 1." })
    @Max(50, { message: "Page size cannot exceed 50." })
    pageSize: number = 10;

    @ApiProperty({
        description: "Page number to retrieve. Must be an integer greater than or equal to 1.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page number is required." })
    @IsInt({ message: "Page number must be a valid integer." })
    @Min(1, { message: "Page number must be at least 1." })
    page: number = 1;

    @ApiProperty({
        description: "Whether the booking is new or not. Must be a boolean value.",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean({ message: "New booking status must be a valid boolean value." })
    isNewBooking?: boolean;
}