import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsOptional } from "class-validator";
import { TransformToBoolean } from "src/common/decorators/transformers.decorator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";

export class VoucherListDto extends PaginationListDto {
    @ApiProperty({
        description: "Is the voucher active | Boolean",
        example: true,
        required: false,
    })

    @IsOptional()
    @IsBoolean()
    @TransformToBoolean()
    isActive?: boolean;

    @ApiProperty({
        description: "Is the voucher expired | Boolean",
        example: false,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    @TransformToBoolean()
    isExpired?: boolean;

}
