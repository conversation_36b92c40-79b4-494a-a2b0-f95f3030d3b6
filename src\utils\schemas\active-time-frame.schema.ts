import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { DaysOfWeek } from "../enums/days-of-week.enum";

/**
 * Schema for defining time-based availability rules for products and services
 */
@Schema({ _id: false, timestamps: false })
export class ActiveTimeFrame {
    /**
     * Specific date for this time frame (optional)
     * If provided, this time frame applies only on this specific date
     */
    @Prop({
        type: Date,
        required: false,
        index: true,
    })
    date?: Date;

    /**
     * Day of the week this time frame applies to (if no specific date)
     * Each day should have its own time frame document
     */
    @Prop({ type: String, enum: DaysOfWeek, required: false, index: true })
    dayOfWeek?: DaysOfWeek;

    /**
     * Start time for this time frame (HH:MM format)
     */
    @Prop({
        type: String,
        required: true,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    startTime: string;

    /**
     * End time for this time frame (HH:MM format)
     */
    @Prop({
        type: String,
        required: true,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    endTime: string;
}

export const ActiveTimeFrameSchema = SchemaFactory.createForClass(ActiveTimeFrame);
