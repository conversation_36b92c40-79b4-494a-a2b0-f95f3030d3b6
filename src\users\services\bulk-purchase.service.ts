// src/users/services/bulk-purchase.service.ts
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
  import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { ClientSession, Connection, Model, Types } from 'mongoose';
import * as QRCode from 'qrcode';

import {
  BulkCreateOrdersDto,
  ClientBulkOrderDto,
  BulkOrderItemDto,
} from '../dto/order-bulk-upload.dto';

import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { SessionType } from 'src/utils/enums/session-type.enum';
import { Invoice } from '../schemas/invoice.schema';
import { Purchase } from '../schemas/purchased-packages.schema';
import { Pricing } from 'src/organization/schemas/pricing.schema';
import { User } from '../schemas/user.schema';
import { Clients } from '../schemas/clients.schema';
import { UploadService } from 'src/utils/services/upload.service';
import { UsersPipe } from '../pipes/users.pipe';
import { DiscountType } from 'src/utils/enums/discount.enum';

import { InvoiceService } from '../services/invoice.service';

// ---- Local “lean” types used by queries ----
type PricingDoc = {
  _id: Types.ObjectId | string;
  name: string;
  price: number;
  tax?: number; // GST %
  hsnOrSacCode?: string;
  itemType: ENUM_PRODUCT_ITEM_TYPE;
  expiredInDays: number;
  durationUnit?: 'day' | 'week' | 'month' | 'year';
  isBundledPricing?: boolean;
  pricingIds?: (Types.ObjectId | string)[];
  services?: {
    sessionType?: SessionType;
    sessionCount?: number;
    sessionPerDay?: number;
    dayPassLimit?: number;
    type?: string;
  };
  membershipId?: Types.ObjectId | string | null;
  organizationId: Types.ObjectId | string;
};

type ClientProfileLean = {
  _id: Types.ObjectId;
  organizationId: string;
  facilityId?: string;
};

type UserLean = {
  _id: Types.ObjectId;
  parent?: string | Types.ObjectId | null;
};

type PurchaseLean = {
  _id: Types.ObjectId;
  packageId: Types.ObjectId;
  itemType: ENUM_PRODUCT_ITEM_TYPE;
  isExpired?: boolean;
  bundledPricingId?: Types.ObjectId;
};

@Injectable()
export class BulkPurchaseService {
  constructor(
    @InjectConnection() private readonly conn: Connection,

    // MODELS
    @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
    @InjectModel(Purchase.name) private readonly PurchaseModel: Model<Purchase>,
    @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
    @InjectModel(User.name) private UserModel: Model<User>,
    @InjectModel(Clients.name) private ClientModel: Model<Clients>,
    // Use your actual org model name if different:
    @InjectModel('Organizations') private OrganizationsModel: Model<any>,

    // SERVICES
    private readonly usersPipe: UsersPipe,
    private readonly uploadService: UploadService,
    private readonly invoiceService: InvoiceService
  ) {}

  /** Entry: many clients × many items (all-or-nothing). */
  async processBulk(dto: BulkCreateOrdersDto, actor: any) {
    const session = await this.conn.startSession();
    session.startTransaction();
    try {
      const results: any[] = [];
      for (const block of dto.orders) {
        results.push(await this.processClientBlock(block, actor, session));
      }
      await session.commitTransaction();
      return { ok: true, results };
    } catch (err) {
      await session.abortTransaction();
      throw err;
    } finally {
      session.endSession();
    }
  }



  // ------------------- per-client flow -------------------
  private async processClientBlock(
    block: ClientBulkOrderDto,
    actor: any,
    session: ClientSession
  ) {
    const clientUserId = new Types.ObjectId(block.clientId);

    // 0) Resolve organizationId & facilityId from Clients
    const clientProfile = await this.ClientModel.findOne({ userId: clientUserId })
      .lean<ClientProfileLean>()
      .session(session)
      .exec();

    if (!clientProfile) {
      throw new NotFoundException('Client profile not found in Clients collection');
    }

    const organizationId = new Types.ObjectId(clientProfile.organizationId);
    const facilityId = new Types.ObjectId(
      clientProfile.facilityId ?? (actor?.facilityId as string)
    );

    // 0.1) Fetch org-level inclusive GST (match POS)
    const orgData:any = await this.OrganizationsModel.findOne(
      { userId: organizationId },
      'isInclusiveofGst'
    )
      .lean()
      .session(session)
      .exec();
    const orgInclusiveDefault = Boolean(orgData?.isInclusiveofGst);

    // 1) Find user + resolve billing user
    const user = await this.UserModel.findById(clientUserId)
      .lean<UserLean>()
      .session(session)
      .exec();

    const billingUserId = user?.parent ? new Types.ObjectId(user.parent) : clientUserId;

    // Build pipeline & run aggregate
    const pipeline = this.usersPipe.billingDetails(
      billingUserId as any,
      facilityId as any
    );
    const billingAgg = await this.UserModel.aggregate(pipeline)
      .session(session)
      .exec();

    if (!billingAgg || !billingAgg.length) {
      throw new NotFoundException('Insufficient data for billing details');
    }
    const billing = billingAgg[0];

    const clientBillingDetailsSrc = billing?.clientDetails;
    const isForBusiness = false;

    const clientDetailsForInvoice = {
      _id: billing?.clientDetails?.userId || '',
      customerId: billing?.clientDetails?.customerId || '',
      name: billing?.clientDetails?.name || '',
      email: billing?.clientDetails?.email || '',
      phone: billing?.clientDetails?.phone || '',
    };

    const clientBillingDetailsForInvoice = {
      _id: clientBillingDetailsSrc?._id || '',
      customerId: clientBillingDetailsSrc?.customerId || '',
      name: clientBillingDetailsSrc?.name || '',
      addressLine1: clientBillingDetailsSrc?.addressLine1 || '',
      addressLine2: clientBillingDetailsSrc?.addressLine2 || '',
      postalCode: clientBillingDetailsSrc?.postalCode || '',
      cityId: clientBillingDetailsSrc?.cityId
        ? new Types.ObjectId(clientBillingDetailsSrc.cityId)
        : undefined,
      cityName: clientBillingDetailsSrc?.cityName || '',
      stateId: clientBillingDetailsSrc?.stateId
        ? new Types.ObjectId(clientBillingDetailsSrc.stateId)
        : undefined,
      stateName: clientBillingDetailsSrc?.stateName || '',
      gstNumber: clientBillingDetailsSrc?.gstNumber,
      utCode: clientBillingDetailsSrc?.utCode || '',
    };

    const facilityBillingForInvoice = {
      facilityName: billing?.billingDetails?.facilityName || '',
      billingName: billing?.billingDetails?.billingName || '',
      gstNumber: billing?.billingDetails?.gstNumber || '',
      email: billing?.billingDetails?.email || '',
      phone: billing?.billingDetails?.phone || '',
      addressLine1: billing?.billingDetails?.addressLine1 || '',
      addressLine2: billing?.billingDetails?.addressLine2 || '',
      postalCode: billing?.billingDetails?.postalCode || '',
      cityId: billing?.billingDetails?.cityId
        ? new Types.ObjectId(billing.billingDetails.cityId)
        : undefined,
      cityName: billing?.billingDetails?.cityName || '',
      stateId: billing?.billingDetails?.stateId
        ? new Types.ObjectId(billing.billingDetails.stateId)
        : undefined,
      stateName: billing?.billingDetails?.stateName || '',
      utCode: billing?.billingDetails?.utCode || '',
    };

    // 2) Invoice-level date
    const invoiceStartDate = this.getEarliestStart(block.items);
    const currentTime = new Date();
    invoiceStartDate.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );

    // 3) Build invoice purchaseItems[] (DO NOT multiply UI price by quantity)
    const purchaseItems: any[] = [];

    let subTotalAcc = 0;     // sum of line bases (no × qty)
    let totalGstAcc = 0;     // sum of line gst
    let itemDiscountAcc = 0; // sum of line discounts
    let amountPaidAcc = 0;   // sum of collected amounts

    for (const line of block.items) {
      const pkg = await this.PricingModel.findById(line.itemId)
        .lean<PricingDoc>()
        .session(session)
        .exec();

      if (!pkg) throw new NotFoundException(`Pricing not found: ${line.itemId}`);

      const startDate = new Date(line.startDate);
      const endDate = new Date(line.endDate);
      const qty = Math.max(1, Number(line.quantity));

      const taxRate = Number(pkg.tax || 0);

      // Resolve inclusive flag like POS: line > block > org
      const isInclusiveLine =
        (line as any).isInclusiveofGst ??
        (block as any).isInclusiveofGst ??
        orgInclusiveDefault;

      const packagPrice = this.round2(Number(pkg.price || 0));
      let claculatePrice=isInclusiveLine?this.splitGSTInclusivePrice(packagPrice,taxRate):this.addGSTToBasePrice(packagPrice,taxRate)
      let basePrice=claculatePrice.basePrice;
      let gst=claculatePrice.gstAmount
      let finalWithGst=claculatePrice.finalPrice

      // UI "packagePrice" is a LINE value. For display unit price:
      const packagePriceLine = this.round2(Number(line.packagePrice || 0));
      const unitPriceDisplay = this.round2(packagePriceLine / qty); // ← what UI must show
      

      // What was actually billed (gross):
      const amountCollected = this.round2(Number(line.amountCollected || 0));

      // --- Split base/GST from the actually billed gross (POS panel math) ---
      const splitCollected = this.splitGSTInclusivePrice(amountCollected, taxRate);
      const collectedBase = this.round2(splitCollected.basePrice);
      const collectedGst  = this.round2(splitCollected.gstAmount);

      // --- Discount calculation depends on inclusive/exclusive ---
      let discountExcludeCart = 0;
      console.log(isInclusiveLine)
      if (isInclusiveLine) {
        // Inclusive: packagePrice is GROSS; discount on gross
        const plannedGross = packagePriceLine;
        discountExcludeCart = this.round2(Math.max(0, plannedGross - amountCollected));
      } else {
        // Exclusive: packagePrice is BASE; discount on BASE
        // POS behavior in your screenshot: base shortfall = packagePrice(base) - collectedBase
        const plannedBase = basePrice*qty;
        let claculateExcludedDiscount=line.packagePrice-line.amountCollected
        let split=this.splitGSTInclusivePrice(claculateExcludedDiscount,taxRate)
        console.log(split)
        discountExcludeCart = this.round2(Math.max(0,split.basePrice));
      }

      // Accumulate totals (subTotal uses BASE; GST is collectedGst)
      subTotalAcc   = this.round2(subTotalAcc + collectedBase);
      totalGstAcc   = this.round2(totalGstAcc + collectedGst);
      itemDiscountAcc = this.round2(itemDiscountAcc + discountExcludeCart);
      amountPaidAcc   = this.round2(amountPaidAcc + amountCollected);

      purchaseItems.push({
        packageId: new Types.ObjectId(pkg._id as any),
        purchaseIds: [],
        packageName: pkg.name,
        quantity: qty,
        isBundledPricing: !!pkg.isBundledPricing,
        expireIn: Number(pkg.expiredInDays || 0),
        durationUnit: this.normalizeDurationUnit((pkg.durationUnit as any) || 'days'),
        startDate,
        endDate,

        // Display-only per-unit price:
        unitPrice: packagPrice,

        // Store the line gross actually billed:
        price: packagPrice,

        discountType: discountExcludeCart > 0 ? DiscountType.FLAT : undefined,
        discountedBy: discountExcludeCart > 0 ? (actor?._id || null) : undefined,
        discountValue: discountExcludeCart > 0 ? discountExcludeCart : 0,
        discountExcludeCart,
        discountIncludeCart: 0,
        returnDiscountAmount: 0,
        voucherDiscountAmount: 0,

        hsnOrSacCode: pkg.hsnOrSacCode || '',
        tax: taxRate,
        gstAmount: collectedGst,

        promotionLabel: undefined,
        promotionLabelKey: undefined,

        isInclusiveofGst: !!isInclusiveLine,
      });
    }

    // 4) Totals (POS-consistent)
    const subTotal = this.round2(subTotalAcc);
    const totalGstValue = this.round2(totalGstAcc);
    const itemDiscount = this.round2(itemDiscountAcc);
    const totalAmountAfterGst = this.round2(subTotal + totalGstValue); // equals sum(collected)
    const grandTotal = totalAmountAfterGst;

    if (grandTotal < 0) throw new BadRequestException('Grand total cannot be negative');

    // 5) Payments
    const amountPaid = amountPaidAcc;

    const paymentDetails = block.items.map((line) => ({
      paymentMethod: String(line.paymentMethod).trim(),
      paymentMethodId: new Types.ObjectId(line.paymentMethodId),
      paymentGateway: '',
      transactionId: '',
      amount: Number(line.amountCollected || 0),
      paymentDate: invoiceStartDate,
      paymentStatus: PaymentStatus.COMPLETED,
      description: '',
      denominations: undefined,
    }));

    // 6) Invoice number / order id
    const highestInvoice = await this.InvoiceModel.findOne(
      { organizationId, facilityId },
      { invoiceNumber: 1, _id: 0 }
    )
      .sort({ invoiceNumber: -1 })
      .lean<{ invoiceNumber: number }>()
      .session(session)
      .exec();

    const invoiceNumber = highestInvoice?.invoiceNumber
      ? highestInvoice.invoiceNumber + 1
      : 1;

    const highestOrder = await this.InvoiceModel.findOne(
      { organizationId, facilityId },
      { orderId: 1, _id: 0 }
    )
      .sort({ orderId: -1 })
      .lean<{ orderId: number }>()
      .session(session)
      .exec();

    const orderId = highestOrder?.orderId ? highestOrder.orderId + 1 : 1;

    const anyInclusive = purchaseItems.some((i: any) => i.isInclusiveofGst === true);

    // 7) Create invoice
    const invoice = new this.InvoiceModel({
      createdBy: actor._id,
      invoiceNumber,
      orderId,
      userId: clientUserId,
      organizationId,
      facilityId,

      purchaseItems,
      productItem: [],
      customPackageItems: [],

      isInclusiveofGst: anyInclusive,
      subTotal,
      itemDiscount,
      discount: itemDiscount,
      cartDiscount: 0,
      cartDiscountAmount: 0,
      totalGstValue,
      totalAmountAfterGst, // = sum(collected)
      roundOff: 0,
      grandTotal,

      amountPaid,
      amountInWords: this.formatAmountWords(totalAmountAfterGst),
      paymentStatus: PaymentStatus.COMPLETED,
      paymentDetails,
      isSplittedPayment: paymentDetails.length > 1,
      platform: 'BULK_UI',

      clientDetails: {
        _id: billing?.clientDetails?.userId || '',
        customerId: billing?.clientDetails?.customerId || '',
        name: billing?.clientDetails?.name || '',
        email: billing?.clientDetails?.email || '',
        phone: billing?.clientDetails?.phone || '',
      },
      clientBillingDetails: {
        _id: clientBillingDetailsSrc?._id || '',
        customerId: clientBillingDetailsSrc?.customerId || '',
        name: clientBillingDetailsSrc?.name || '',
        addressLine1: clientBillingDetailsSrc?.addressLine1 || '',
        addressLine2: clientBillingDetailsSrc?.addressLine2 || '',
        postalCode: clientBillingDetailsSrc?.postalCode || '',
        cityId: clientBillingDetailsSrc?.cityId
          ? new Types.ObjectId(clientBillingDetailsSrc.cityId)
          : undefined,
        cityName: clientBillingDetailsSrc?.cityName || '',
        stateId: clientBillingDetailsSrc?.stateId
          ? new Types.ObjectId(clientBillingDetailsSrc.stateId)
          : undefined,
        stateName: clientBillingDetailsSrc?.stateName || '',
        gstNumber: clientBillingDetailsSrc?.gstNumber,
        utCode: clientBillingDetailsSrc?.utCode || '',
      },
      billingDetails: {
        facilityName: billing?.billingDetails?.facilityName || '',
        billingName: billing?.billingDetails?.billingName || '',
        gstNumber: billing?.billingDetails?.gstNumber || '',
        email: billing?.billingDetails?.email || '',
        phone: billing?.billingDetails?.phone || '',
        addressLine1: billing?.billingDetails?.addressLine1 || '',
        addressLine2: billing?.billingDetails?.addressLine2 || '',
        postalCode: billing?.billingDetails?.postalCode || '',
        cityId: billing?.billingDetails?.cityId
          ? new Types.ObjectId(billing.billingDetails.cityId)
          : undefined,
        cityName: billing?.billingDetails?.cityName || '',
        stateId: billing?.billingDetails?.stateId
          ? new Types.ObjectId(billing.billingDetails.stateId)
          : undefined,
        stateName: billing?.billingDetails?.stateName || '',
        utCode: billing?.billingDetails?.utCode || '',
      },

      invoiceDate: invoiceStartDate,
      isForBusiness, // false for now
      paymentBy: actor._id,

      invoiceFilePath: '',
    });

    const savedInvoice = await invoice.save({ session });

    await this.InvoiceModel.collection.updateOne(
      { _id: savedInvoice._id },
      { $set: { createdAt: invoiceStartDate, updatedAt: invoiceStartDate } },
      { session } as any
    );

    // 8) Create purchases
    type NewPurchaseItem = {
      packageId: Types.ObjectId;
      quantity: number;
      isBundledPricing?: boolean;
      bundledPricingId?: Types.ObjectId;
      startDate: Date;
      endDate?: Date;
    };
    const newPurchaseItems: NewPurchaseItem[] = [];

    for (const line of block.items) {
      const pkg = await this.PricingModel.findById(line.itemId)
        .lean<PricingDoc>()
        .session(session)
        .exec();

      if (!pkg) throw new NotFoundException(`Pricing not found: ${line.itemId}`);

      const startDate = new Date(line.startDate);
      const endDate = new Date(line.endDate);

      if (pkg.isBundledPricing && Array.isArray(pkg.pricingIds) && pkg.pricingIds.length) {
        for (const childId of pkg.pricingIds) {
          newPurchaseItems.push({
            packageId: new Types.ObjectId(childId as any),
            quantity: Number(line.quantity),
            isBundledPricing: true,
            bundledPricingId: new Types.ObjectId(pkg._id as any),
            startDate,
          });
        }
      } else {
        newPurchaseItems.push({
          packageId: new Types.ObjectId(pkg._id as any),
          quantity: Number(line.quantity),
          isBundledPricing: false,
          startDate,
          endDate,
        });
      }
    }

    const purchaseDocs: any[] = [];
    for (const row of newPurchaseItems) {
      const pkg = await this.PricingModel.findById(row.packageId)
        .lean<PricingDoc>()
        .session(session)
        .exec();

      if (!pkg) throw new NotFoundException(`Pricing not found: ${row.packageId}`);

      const endDate = row.endDate
        ? new Date(row.endDate)
        : this.calculateEndDate(
            row.startDate,
            Number(pkg.expiredInDays || 0),
            (pkg.durationUnit as any) || 'day'
          );

      const isExpired = endDate.getTime() < Date.now();

      for (let i = 0; i < Number(row.quantity); i++) {
        purchaseDocs.push(
          new this.PurchaseModel({
            invoiceId: savedInvoice._id,
            packageId: row.packageId,
            userId: clientUserId,
            sponsorUser: clientUserId,
            consumers: [clientUserId],

            organizationId,
            itemType: pkg.itemType,
            facilityId,
            purchasedBy: actor._id,
            membershipId: pkg?.membershipId || null,

            purchaseDate: invoiceStartDate,
            paymentStatus: PaymentStatus.COMPLETED,

            isActive: true,
            isExpired,

            sessionType: pkg.services?.sessionType,
            sessionPerDay: pkg.services?.sessionPerDay,
            dayPassLimit: pkg.services?.dayPassLimit,
            totalSessions:
              pkg.services?.sessionType === SessionType.SINGLE
                ? 1
                : pkg.services?.sessionCount,

            startDate: row.startDate,
            endDate,
          })
        );
      }
    }

    if (purchaseDocs.length) {
      await this.PurchaseModel.insertMany(purchaseDocs, { session });

      await this.PurchaseModel.collection.updateMany(
        { invoiceId: savedInvoice._id },
        { $set: { createdAt: invoiceStartDate, updatedAt: invoiceStartDate } },
        { session } as any
      );

      const created = await this.PurchaseModel.find({ invoiceId: savedInvoice._id })
        .lean<PurchaseLean[]>()
        .session(session)
        .exec();

      const mutated = [...invoice.purchaseItems];
      for (const p of created) {
        const host =
          mutated.find(
            (i: any) =>
              i.packageId.toString() ===
              (p as any).bundledPricingId?.toString()
          ) ||
          mutated.find(
            (i: any) =>
              i.packageId.toString() === (p as any).packageId.toString()
          );

        if (!host) continue;
        host.purchaseIds = host.purchaseIds || [];
        host.purchaseIds.push((p._id as Types.ObjectId).toString());
      }

      await this.InvoiceModel.updateOne(
        { _id: savedInvoice._id },
        { $set: { purchaseItems: mutated } },
        { session, timestamps: false }
      );

      try {
        const pdfUrl = await this.invoiceService.generateInvoicePdfOnly(
          savedInvoice._id,
          { session }
        );
        if (pdfUrl) {
          await this.InvoiceModel.updateOne(
            { _id: savedInvoice._id },
            { $set: { invoiceFilePath: pdfUrl } },
            { session, timestamps: false }
          );
        }
      } catch {
        // ignore PDF errors
      }

      for (const p of created) {
        try {
          if (p.itemType !== ENUM_PRODUCT_ITEM_TYPE.SERVICE || p.isExpired === true) continue;

          const png = await QRCode.toBuffer(
            JSON.stringify({ purchase_id: p._id }),
            { type: 'png' }
          );
          const s3 = await this.uploadService.upload(
            png,
            'purchase-qr/',
            `purchase-${p._id}.png`
          );

          await this.PurchaseModel.updateOne(
            { _id: p._id },
            { $set: { qrCodeUrl: s3?.Location || '' } },
            { session, timestamps: false }
          );
        } catch {
          // ignore QR failures
        }
      }
    }

    return {
      invoiceId: savedInvoice._id.toString(),
      invoiceNumber,
      orderId,
      grandTotal,
      amountPaid,
      invoiceDate: invoiceStartDate,
    };
  }

  // ------------------- helpers -------------------
  private getEarliestStart(items: BulkOrderItemDto[]): Date {
    return new Date(
      Math.min(...items.map((i) => new Date(i.startDate).getTime()))
    );
  }

  /** Normalize various unit spellings to what's accepted in your schema + used in math. */
  private normalizeDurationUnit(
    u?: string
  ): 'days' | 'weeks' | 'months' | 'years' {
    const s = (u || '').toString().toLowerCase();
    if (s.startsWith('day')) return 'days';
    if (s.startsWith('week')) return 'weeks';
    if (s.startsWith('month')) return 'months';
    if (s.startsWith('year')) return 'years';
    return 'days';
  }

  private calculateEndDate(
    start: Date,
    qty: number,
    unit: 'day' | 'week' | 'month' | 'year'
  ) {
    const d = new Date(start);
    if (unit === 'day') d.setDate(d.getDate() + qty);
    if (unit === 'week') d.setDate(d.getDate() + qty * 7);
    if (unit === 'month') d.setMonth(d.getMonth() + qty);
    if (unit === 'year') d.setFullYear(d.getFullYear() + qty);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  private round2(n: number) {
    return Math.round((Number(n) + Number.EPSILON) * 100) / 100;
  }

  /** If finalPrice already includes GST, extract base & GST */
  private splitGSTInclusivePrice(finalPrice: number, gstPercent: number) {
    const basePrice = +(finalPrice / (1 + gstPercent / 100));
    const gstAmount = +(finalPrice - basePrice).toFixed(2);
    return { basePrice, gstAmount, finalPrice: +finalPrice.toFixed(2) };
  }

  /** If base price excludes GST, add GST on top */
  private addGSTToBasePrice(basePrice: number, gstPercent: number) {
    const gstAmount = +(basePrice * (gstPercent / 100));
    const finalPrice = +(basePrice + gstAmount).toFixed(2);
    return { basePrice: +basePrice.toFixed(2), gstAmount, finalPrice };
  }

  // ---- Amount in words (Indian numbering) ----
  private formatAmountWords(n: number): string {
    const rupees = Math.round(Math.abs(Number(n) || 0));
    if (rupees === 0) return 'Zero Rupees Only';

    const words = this.numberToWordsINR(rupees);
    return `${words.charAt(0).toUpperCase()}${words.slice(1)} Rupees Only`;
  }

  private numberToWordsINR(n: number): string {
    const below20 = [
      'zero',
      'one',
      'two',
      'three',
      'four',
      'five',
      'six',
      'seven',
      'eight',
      'nine',
      'ten',
      'eleven',
      'twelve',
      'thirteen',
      'fourteen',
      'fifteen',
      'sixteen',
      'seventeen',
      'eighteen',
      'nineteen',
    ];
    const tens = [
      '',
      '',
      'twenty',
      'thirty',
      'forty',
      'fifty',
      'sixty',
      'seventy',
      'eighty',
      'ninety',
    ];

    const under100 = (num: number): string => {
      if (num < 20) return below20[num];
      const t = Math.floor(num / 10);
      const r = num % 10;
      return r ? `${tens[t]}-${below20[r]}` : tens[t];
    };

    const parts: string[] = [];

    const crore = Math.floor(n / 10000000);
    n %= 10000000;

    const lakh = Math.floor(n / 100000);
    n %= 100000;

    const thousand = Math.floor(n / 1000);
    n %= 1000;

    const hundred = Math.floor(n / 100);
    const rest = n % 100;

    if (crore) parts.push(`${under100(crore)} crore`);
    if (lakh) parts.push(`${under100(lakh)} lakh`);
    if (thousand) parts.push(`${under100(thousand)} thousand`);
    if (hundred)
      parts.push(
        rest ? `${below20[hundred]} hundred ${under100(rest)}` : `${below20[hundred]} hundred`
      );
    else if (rest) parts.push(under100(rest));

    return parts.join(', ');
  }
}
