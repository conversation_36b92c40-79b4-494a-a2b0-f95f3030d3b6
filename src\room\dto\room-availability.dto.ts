import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsMongoId, IsEnum, IsISO8601 } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class RoomAvailabilityDto {
    @ApiProperty({
        description: "Facility ID (MongoDB ObjectId)",
        example: "66cedf7a731d1269a4157a2d",
        required: true,
    })
    @IsNotEmpty({ message: "Facility ID is required and cannot be empty." })
    @IsMongoId({ message: "Facility ID must be a valid MongoDB ObjectId (24 hex characters)." })
    facilityId: string;

    @ApiProperty({
        description: "Class type for the training session",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: true,
    })
    @IsNotEmpty({ message: "Class type is required and cannot be empty." })
    @IsEnum(ClassType, { message: `Class type must be one of: ${Object.values(ClassType).join(", ")}.` })
    classType: ClassType;

    @ApiProperty({
        description: "Service category ID (MongoDB ObjectId)",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Service category ID is required and cannot be empty." })
    @IsMongoId({ message: "Service category ID must be a valid MongoDB ObjectId (24 hex characters)." })
    serviceCategoryId: string;

    @ApiProperty({
        description: "Date of scheduling (ISO 8601 format)",
        example: "2024-09-15T00:00:00Z",
        required: true,
    })
    @IsNotEmpty({ message: "Date is required and cannot be empty." })
    @IsISO8601({ strict: true }, { message: "Date must be in ISO 8601 format (e.g., 2024-09-15T00:00:00Z)." })
    date: string;
}