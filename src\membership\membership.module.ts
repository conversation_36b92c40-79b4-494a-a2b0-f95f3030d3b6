import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { membership, MembershipSchema } from "./schema/membership.schema";
import { membershipController } from "./controller/membership.controller";
import { MembershipService } from "./service/membership.service";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Module({

    imports: [
        AuthModule,
        UtilsModule,
        MailModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([
            { name: membership.name, schema: MembershipSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [membershipController],
    providers: [MembershipService],
    exports: [MembershipService],
})
export class membershipModule { }