import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsEmail, Length, ValidateIf, IsStrongPassword, IsMongoId, IsNotEmpty, IsOptional } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class ResetPasswordDto {
    @ApiProperty({
        description: "The method of authentication for resetting the password. Can be either EMAIL or MOBILE.",
        enum: AuthTypes,
        example: AuthTypes.EMAIL,
    })
    @IsNotEmpty({ message: "Authentication type is required. It can be either EMAIL or MOBILE." })
    @IsEnum([AuthTypes.EMAIL, AuthTypes.MOBILE], { message: "Authentication type must be either EMAIL or MOBILE." })
    type: string;

    @ApiProperty({
        description: "The user email address. Required if the authentication type is EMAIL.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @ValidateIf((req) => req.type === AuthTypes.EMAIL)
    @Transform((param) => param.value.toLowerCase())
    @IsEmail({}, { message: "Email must be a valid email address." })
    @MaxLength(255, { message: "Email must be at most 255 characters long." })
    email: string;

    @ApiProperty({
        description: "The user mobile number. Required if the authentication type is MOBILE.",
        example: "**********",
        // minLength: 10,
        // maxLength: 10,
        required: false,
    })
    @IsOptional()
    @ValidateIf((req) => req.type === AuthTypes.MOBILE)
    //@Length(10, 10, { message: "Mobile number must be exactly 10 digits long." })
    mobile: string;

    @ApiProperty({
        description: "A new strong password for the user account. Must meet the strong password criteria.",
        example: "N3wStr0ngP@ss!",
    })
    @IsNotEmpty({ message: "Password is required." })
    @IsStrongPassword({}, { message: "Password must meet the strong password criteria." })
    password: string;

    @ApiProperty({
        description: "Confirmation of the new strong password for the user account. Must meet the strong password criteria.",
        example: "N3wStr0ngP@ss!",
    })
    @IsNotEmpty({ message: "Confirm Password is required." })
    @IsStrongPassword({}, { message: "Confirm Password must meet the strong password criteria." })
    confirmPassword: string;

    @ApiProperty({
        description: "The OTP verification code associated with the user. It must be a valid MongoDB ObjectId.",
        example: "507f191e810c19729de860ea",
    })
    @IsNotEmpty({ message: "OTP Verification code is required." })
    @IsMongoId({ message: "OTP Verification code must be a valid MongoDB ObjectId." })
    otpVerificationCode: string;
}