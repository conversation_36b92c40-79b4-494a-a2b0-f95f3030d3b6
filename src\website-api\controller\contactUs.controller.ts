import {
    BadRequestException,
    Body,
    Controller,
    Post,
    HttpCode,

} from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ContactUsService } from "../service/contact-us.service";
import { ContactUsDemoDto } from "../dto/contact-us.dto";

@ApiTags("Website -Contact Us")
@ApiBearerAuth()
@Controller("website/contact-us")
export class ContatcusController {
    constructor(private readonly contactusService: ContactUsService) { }
    @Post("")
    @HttpCode(200)
    @ApiOperation({ summary: "Create a New Contact us Demo" })
    async createContactUs(@Body() contactusDto: ContactUsDemoDto) {
        try {
            const contactUsResponse = await this.contactusService.createContactUs(contactusDto);
            return {
                message: "Contact us created successfully",
                data: contactUsResponse,
            };
        } catch (error) {
            throw new BadRequestException("Error creating Booking Demo");
        }
    }
}
