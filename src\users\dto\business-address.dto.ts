import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, Length } from "class-validator";
import { AddressDto } from "./address.dto";
import { IBusinessAddress } from "../interfaces/business-address.interface";

export class BusinessAddressDto extends AddressDto implements IBusinessAddress {
 
    @ApiProperty({
        description: "Business GST number",
        example: "The business Pvt. Ltd.",
        required: true
    })
    @Type(()=>String)
    @IsNotEmpty()
    @Length(3, 250, { message: "Add a valid Business name" })
    businessName : string

    @ApiProperty({
        description: "Business GST number",
        example: "GSSTIN234jn342i",
        required: true
    })
    @Type(()=>String)
    @IsNotEmpty()
    @Length(3, 250, { message: "Add a valid GST number" })
    gstNumber: string

}

