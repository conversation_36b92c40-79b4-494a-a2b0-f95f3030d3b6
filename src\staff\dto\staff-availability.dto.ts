import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEnum, IsString, IsOptional, IsArray, IsDateString, IsBoolean, Matches, ValidateNested, IsMongoId, ValidateIf } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { StaffPrivacy } from "src/utils/enums/staff-privacy.enum";

export class TimeSlotsDTO {
    @ApiProperty({
        description: 'Start time for availability on specified days (HH:mm)',
        example: '08:00',
    })
    @IsOptional()
    @IsString({ message: 'From time must be a string' })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'From time must be in the format HH:mm' })
    from: string;

    @ApiProperty({
        description: 'End time for availability on specified days (HH:mm)',
        example: '17:00',
    })
    @IsOptional()
    @IsString({ message: 'To time must be a string' })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'To time must be in the format HH:mm' })
    to: string;

    @ApiProperty({
        description: "The IDs of the PayRates for the Staff",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
        isArray: true,
        required: false,
    })
    @IsArray({ message: "payRateIds must be an array of ObjectIds" })
    @IsMongoId({ each: true, message: "Each payRate Id must be a valid ObjectId" })
    payRateIds?: string[];

}

export class ScheduleDTO {

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: 'Monday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    mon: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Tuesday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    tue: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Wednesday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    wed: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Thursday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    thu: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Friday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    fri: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Saturday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    sat: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: 'Sunday must be an array of time slots' })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO) sun: TimeSlotsDTO[];
}

export class StaffAvailabilityDto {
    @ApiProperty({
        description: "The ID of the user",
        example: "66cecb432351713ae4447a6b",
    })
    @IsNotEmpty({ message: "User ID is required" })
    @IsString({ message: "User ID must be a string" })
    userId: string;

    @ApiProperty({
        description: "The ID of the facility",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsNotEmpty({ message: "Facility ID is required" })
    @IsString({ message: "Facility ID must be a string" })
    facilityId: string;

    @ApiProperty({
        description: "Start date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Start date is required" })
    startDate: Date;

    @ApiProperty({
        description: "End date of availability (required if markType is CUSTOM)",
        example: "2024-09-20T00:00:00Z",
    })
    @ValidateIf((obj) => obj.markType === MarkAvailabilityType.CUSTOM)
    @IsNotEmpty({ message: "End date is required when markType is CUSTOM" })
    @Type(() => Date)
    endDate?: Date;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "The availability status of the staff",
        enum: StaffAvailabilityEnum,
        default: StaffAvailabilityEnum.AVAILABLE,
        example: StaffAvailabilityEnum.AVAILABLE,
    })
    @IsEnum(StaffAvailabilityEnum, { message: "Availability status must be a valid enum value" })
    @IsNotEmpty({ message: "Availability status is required" })
    availabilityStatus: StaffAvailabilityEnum;

    @ApiProperty({
        description: "The schedule for staff Availability or UnAvailability",
        type: ScheduleDTO,
    })
    @IsNotEmpty({ message: "schedule  are required" })
    schedule: ScheduleDTO;

    @ApiProperty({
        description: "Privacy setting of staff availability",
        enum: StaffPrivacy,
        required: false,
        example: StaffPrivacy.ALLOW_CLIENT_TO_SEE_SCHEDULE,
    })
    @IsOptional()
    @IsEnum(StaffPrivacy, { message: "Privacy must be a valid enum value" })
    privacy: StaffPrivacy;

    @ApiProperty({
        description: "Reason for unavailability",
        example: "Sick leave",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Reason must be a string" })
    reason: string;

    @ApiProperty({
        description: "ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @ValidateIf((obj) => obj.availabilityStatus === StaffAvailabilityEnum.AVAILABLE)
    @IsNotEmpty({ message: "ClassType is required when available" })
    @IsEnum(ClassType, { message: "ClassType must be a valid enum value" })
    classType?: ClassType;

    @ApiProperty({
        description: "MarkType for Availability",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: false,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "MarkType is required" })
    @IsEnum(MarkAvailabilityType, { message: "MarkType must be a valid enum value" })
    markType?: MarkAvailabilityType;
}






