import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Document } from 'mongoose';
import { OmitType} from '@nestjs/swagger'
import { SubSettingsOptions, SubSettingsOptionsDocument, SubSettingsOptionsSchema } from './sub-setting-options.schema';

@Schema({ timestamps: true })
export class SettingsOptions extends Document {
  @Prop({ required: false, index: true, type: Number, default: 0 })
  @IsNumber()
  @IsNotEmpty()
  order: number;

  @Prop({ required: true, index: true, unique: true, type: String })
  @IsString()
  @IsNotEmpty()
  key: string;

  @Prop({ required: true, index: true, unique: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Prop({ type: String, default: null })
  @IsString()
  @IsOptional()
  description?: string;

  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  isActive: boolean;

  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  @IsOptional()
  default?: boolean;

  @Prop({ type: Boolean, default: true })
  @IsBoolean()
  inOperation: boolean;

  @Prop({ type: String, default: "", required: false })
  href?: "";

  @Prop({
    type: [SubSettingsOptionsSchema],
    required: false,
    default: []
  })
  subSettings?: SubSettingsOptionsDocument[];
}

export const SettingsOptionsSchema = SchemaFactory.createForClass(SettingsOptions);

SettingsOptionsSchema.index({"subSettings.key":1}, {unique: true, partialFilterExpression: { "subSettings.key": { $exists: true, $ne: null } }})
SettingsOptionsSchema.index({"subSettings.name":1}, {unique: true, partialFilterExpression: { "subSettings.name": { $exists: true, $ne: null } }})
