import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { StaffPrivacy } from "src/utils/enums/staff-privacy.enum";

@Schema({ timestamps: true })
export class StaffAvailability extends Document {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;

    @Prop({ type: String, enum: DateRange, required: true })
    dateRange: string;

    @Prop({ type: Date, required: true })
    date: Date;

    @Prop({
        type: [
            {
                from: {
                    type: String,
                    required: true,
                    match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
                },
                to: {
                    type: String,
                    required: true,
                    match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
                },
                availabilityStatus: {
                    type: String,
                    enum: StaffAvailabilityEnum,
                    required: true,
                },
                reason: { type: String, required: false },
                privacy: { type: String, enum: StaffPrivacy, required: false },
                classType: {
                    type: String,
                    enum: ClassType,
                    required: false,
                 },
                payRateIds: {
                    type: [SchemaTypes.ObjectId],
                    required: false,
                },

            },
        ],
        required: true,
    })
    timeSlots: Array<{
        from: string;
        to: string;
        availabilityStatus: string;
        reason?: string;
        privacy?: string,
        classType?: string;
        payRateIds?: string[];
    }>;
}

export const StaffAvailabilitySchema = SchemaFactory.createForClass(StaffAvailability);

//StaffAvailabilitySchema.index({ userId: 1, organizationId: 1, facilityId: 1, date: 1 });
StaffAvailabilitySchema.index({userId: 1,date: 1,facilityId: 1,organizationId: 1},{ unique: true });
// StaffAvailabilitySchema.index({ userId: 1, organizationId: 1,  });
