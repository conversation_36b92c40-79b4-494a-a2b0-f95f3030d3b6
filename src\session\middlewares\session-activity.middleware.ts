import { Injectable, NestMiddleware, ForbiddenException, Logger } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { SessionService } from '../services/session.service';
import { Types } from 'mongoose';
import { CachingService } from 'src/common/caching/services/caching.service';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { IUserDocument } from 'src/users/interfaces/user.interface';

@Injectable()
export class SessionActivityMiddleware implements NestMiddleware {
    private readonly logger = new Logger(SessionActivityMiddleware.name);

    constructor(
        private readonly sessionService: SessionService,
    ) { }

    async use(req: IRequestApp, _res: Response, next: NextFunction): Promise<void> {
        req.__sessionId = "";
        req.__isSessionActive = false;
        req.__delegateSessionId = "";
        req.__isDelegateSessionActive = false;

        if (!req.session?.id) {
            return next();
        }

        try {
            // Store the consistent session ID
            const userId = req.session.userId;
            const userSessionId = req.session.userSessionId;
            const delegatedUserId = req.session.userDelegatedId;
            const delegatedSessionId = req.session.userDelegatedSessionId;

            // Only check activity if user is authenticated
            if (userId && userSessionId) {
                const isActive = await this.sessionService.checkSessionActivity(new Types.ObjectId(userSessionId));
                req.__isSessionActive = isActive;
                if (isActive) {
                    const user = await this.sessionService.getLoginSessionUser(new Types.ObjectId(userSessionId));
                    req.__sessionId = userSessionId;
                    req.__user = user;

                    // Update session activity timestamp to extend session
                    await this.sessionService.updateSessionActivity(new Types.ObjectId(userSessionId));
                }
            }

            if (delegatedUserId && delegatedSessionId) {
                const isActive = await this.sessionService.checkSessionActivity(new Types.ObjectId(delegatedSessionId));
                req.__isDelegateSessionActive = isActive;
                if (isActive) {
                    const user = await this.sessionService.getLoginSessionUser(new Types.ObjectId(delegatedSessionId));
                    req.__delegateSessionId = delegatedSessionId;
                    req.__delegateUser = user;

                    // Update session activity timestamp to extend session
                    await this.sessionService.updateSessionActivity(new Types.ObjectId(delegatedSessionId));
                }
            }

            await new Promise<void>((resolve, reject) => {
                req.session.save((err: any) => {
                    if (err) reject(err);
                    resolve();
                });
            });
            next();
        } catch (error) {
            this.logger.error(`Session check failed: ${error.message}`);
            throw new ForbiddenException('Session validation failed');
        }
    }

}
