import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";
import { Gender } from "src/utils/enums/gender.enum";

export class Address {
    @Prop({ type: SchemaTypes.ObjectId, required: false, default: "" })
    stateId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: false, default: "" })
    cityId: Types.ObjectId;

    @Prop({ type: String, required: false, default: "" })
    street: string;

    @Prop({ type: String, required: false, default: "" })
    country: string;
}

export type StaffDocument = HydratedDocument<StaffProfileDetails>;

@Schema({ timestamps: true })
export class StaffProfileDetails {

    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: [SchemaTypes.ObjectId], required: true, ref: "User" })
    facilityId: [string];

    @Prop({ type: String, required: false })
    staffId: string;

    @Prop({ type: String })
    profilePicture?: string;

    @Prop({ enum: Gender, required: false })
    gender: String;

    @Prop({ type: String })
    pin: String;

    @Prop({ type: Date, required: false })
    dateOfBirth: Date;

    @Prop({ type: Address, required: false, default: {} })
    address: Address;

    @Prop({ type: String, required: false })
    certification: String;

    @Prop({ type: String, required: false })
    experience: string;

    @Prop({ type: String, required: false })
    description: string;

    @Prop({ type: Date, required: false })
    setUpDate: Date;
}

export const StaffSchema = SchemaFactory.createForClass(StaffProfileDetails);
