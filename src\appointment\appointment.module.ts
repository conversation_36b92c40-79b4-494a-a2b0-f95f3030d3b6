import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { AppointmentController } from "./controller/appointment.controller";
import { AppointmentService } from "./service/appointment.service";
import { MongooseModule } from "@nestjs/mongoose";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { Organizations, OrganizationSchema } from "src/organization/schemas/organization.schema";
import { StaffAvailability, StaffAvailabilitySchema } from "src/staff/schemas/staff-availability";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { Appointment, AppointmentSchema } from "./schema/appointment.schema";
import { FacilityAvailability, FacilityAvailabilitySchema } from "src/facility/schemas/facility-availability.schema";
import { Pricing, PricingSchema } from "src/organization/schemas/pricing.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Module({
    imports: [
        AuthModule,
        UtilsModule,
        MailModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([{ name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: Organizations.name, schema: OrganizationSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema },
            { name: StaffAvailability.name, schema: StaffAvailabilitySchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Appointment.name, schema: AppointmentSchema },
            { name: Pricing.name, schema: PricingSchema }
        ], DATABASE_PRIMARY_CONNECTION_NAME)
    ],
    controllers: [AppointmentController],
    providers: [AppointmentService],
})
export class AppointmentModule {}
