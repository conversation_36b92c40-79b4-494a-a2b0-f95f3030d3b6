import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { CategoryService } from "./services/category.service";
import { Category, CategorySchema } from "src/merchandise/schema/category.schema";
import { CategoryController } from "./controller/category.controller";
import { AttributeService } from "./services/attribute.service";
import { AttributeValue, AttributeValueSchema } from "src/merchandise/schema/attribute.schema";
import { AttributeController } from "./controller/attribute.controller";
import { ProductController } from "./controller/product.controller";
import { ProductService } from "./services/product.service";
import { Product, ProductSchema } from "src/merchandise/schema/product.schema";
import { ProductVariant, ProductVariantSchema } from "src/merchandise/schema/product-variant.schema";
import { InventoryService } from "./services/inventory.service";
import { InventoryController } from "./controller/inventory.controller";
import { Inventory, InventorySchema } from "./schema/inventory.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { ProductAttributeController } from "./controller/product.attribute.controller";
import { ProductAttributeService } from "./services/productAttribute.service";
import { ProductVariantAttribute, ProductVariantAttributeSchema } from "./schema/product-attribute-list.schema";
import { PromotionsModule } from "src/promotions/promotions.module";
import { OrganizationModule } from "src/organization/organization.module";
import { UploadBatch, UploadBatchSchema } from "./schema/upload-batch.schema";
import { BullModule } from '@nestjs/bullmq';
import { BulkUploadWorker } from './bulk/bulk-upload.worker';

@Module({
  imports: [
    AuthModule,
    UtilsModule,
    MailModule,
    PromotionsModule,
    OrganizationModule,
    PassportModule.register({ defaultStrategy: "jwt" }),

    // ⬇️ Add global BullMQ connection here (only once in the whole app)
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: Number(process.env.REDIS_PORT || '6379'),
        username: (process.env.REDIS_USERNAME || '').trim() || undefined,
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DATABASE || '0'),
        // tls: {}, // uncomment if your Redis needs TLS
      },
      defaultJobOptions: { removeOnComplete: true },
    }),

    // Queue registration
    BullModule.registerQueue({ name: 'product-upload-queue-redis' }),

    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        global: true,
        secret: configService.get<string>("JWT_SECRET"),
        signOptions: { expiresIn: "24h" },
      }),
      inject: [ConfigService],
    }),

    MongooseModule.forFeature([
      { name: Category.name, schema: CategorySchema },
      { name: AttributeValue.name, schema: AttributeValueSchema },
      { name: Product.name, schema: ProductSchema },
      { name: ProductVariant.name, schema: ProductVariantSchema },
      { name: Inventory.name, schema: InventorySchema },
      { name: Facility.name, schema: FacilitySchema },
      { name: StaffProfileDetails.name, schema: StaffSchema },
      { name: ProductVariantAttribute.name, schema: ProductVariantAttributeSchema },
      { name: UploadBatch.name, schema: UploadBatchSchema },
    ], DATABASE_PRIMARY_CONNECTION_NAME),
  ],
  controllers: [
    CategoryController,
    AttributeController,
    ProductController,
    InventoryController,
    ProductAttributeController,
  ],
  providers: [
    CategoryService,
    AttributeService,
    ProductService,
    InventoryService,
    ProductAttributeService,
    BulkUploadWorker, // worker runs inside this module
  ],
  exports: [
    CategoryService,
    AttributeService,
    ProductService,
    InventoryService,
  ],
})
export class MerchandiseModule { }
