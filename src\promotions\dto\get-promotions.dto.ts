import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsDateString, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Types } from 'mongoose';
import { ENUM_PROMOTION_TARGET } from '../enums/promotion-target.enum';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { Transform, Type } from 'class-transformer';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';
import { ENUM_PRODUCT_ITEM_TYPE } from '../enums/item-type.enum';

export class GetPromotionsDto extends PaginationListDto {

  // @ApiProperty({
  //   description: 'Filter by organization ID',
  //   example: '60d21b4667d0d8992e610c85',
  //   required: true,
  // })
  // @Transform(({ value }) => {
  //   return new Types.ObjectId(value)
  // })
  // @IsNotEmpty()
  // organizationId: Types.ObjectId;

  @ApiProperty({
    description: 'Filter by promotion type',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
    required: false,
  })
  @IsEnum(DiscountType, { message: 'Invalid promotion type' })
  @IsOptional()
  type?: DiscountType;

  @ApiProperty({
    description: 'Filter by promotion target',
    enum: ENUM_PROMOTION_TARGET,
    example: ENUM_PROMOTION_TARGET.MEMBERS_ONLY,
    required: false,
  })
  @IsEnum(ENUM_PROMOTION_TARGET, { message: 'Invalid promotion target' })
  @IsOptional()
  target?: ENUM_PROMOTION_TARGET;

  @ApiProperty({
    description: 'Filter by promotion status',
    example: true,
    required: false,
  })
  @IsOptional()
  // @Type(() => Boolean)
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Order by',
    example: 'updatedAt',
    enum: ['updatedAt', 'name'],
    required: false,
  })
  orderBy?: string;

  @ApiProperty({
    description: 'Start date of the promotion',
    example: new Date(),
    required: false,
  })
  @Type(() => Date)
  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @ApiProperty({
    description: 'End date of the promotion',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
  endDate?: Date;

  @ApiProperty({
    description: 'Filter by facility ID (returns promotions for this facility or promotions with no facility restriction)',
    example: '60d21b4667d0d8992e610c85,60d21b4667d0d8992e610c85',
    required: false,
  })
  @IsOptional()
  facilityIds?: string;

  @ApiProperty({
    description: 'Filter by pricing ID (returns promotions that include or exclude this pricing)',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  @IsOptional()
  pricingId?: string;

  @ApiProperty({
    description: 'Whether to include or exclude the pricing ID (true = include, false = exclude)',
    example: 'true',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  includePricing?: boolean = true;

  @ApiProperty({
    description: 'Filter by item type (returns promotions that include or exclude this item type)',
    example: 'service',
    required: false,
  })
  @IsOptional()
  itemType?: ENUM_PRODUCT_ITEM_TYPE = ENUM_PRODUCT_ITEM_TYPE.SERVICE;
}
