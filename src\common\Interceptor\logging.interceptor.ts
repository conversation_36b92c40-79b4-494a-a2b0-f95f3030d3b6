import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nestjs/common";
import { Observable, tap } from "rxjs";

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  private safeStringify(obj: any): string {
    try {
      return JSON.stringify(obj, (key, value) => {
        // Handle circular references by replacing them with a placeholder
        if (typeof value === 'object' && value !== null) {
          // Check for common circular reference patterns
          if (value.constructor && (
            value.constructor.name === 'Socket' ||
            value.constructor.name === 'HTTPParser' ||
            value.constructor.name === 'IncomingMessage' ||
            value.constructor.name === 'ServerResponse'
          )) {
            return `[${value.constructor.name}]`;
          }
        }
        return value;
      });
    } catch (error) {
      // Fallback for any other circular reference issues
      return '[Circular Reference]';
    }
  }

  intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const { method, url, body } = req;
    const start = Date.now();

    if (process.env.DEBUG_ENABLE === 'true') {
      this.logger.log(`📥 Request: ${method} ${url} body=${this.safeStringify(body)}-----${Date.now()}`);
    }

    return next.handle().pipe(
      tap((data) => {
        if (process.env.DEBUG_ENABLE === 'true') {
          this.logger.log(`📤 Response: ${method} ${url} [${Date.now() - start}ms] → ${this.safeStringify(data)}---${Date.now()}`);
        }
      }),
    );
  }
}