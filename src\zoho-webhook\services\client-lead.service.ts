import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ClientLead } from '../schema/zoho-webhook.schema';
import { ClientLeadListDto } from '../dto/client-list.dto'

@Injectable()
export class ClientLeadService {
    constructor(
        @InjectModel(ClientLead.name) private readonly clientLeadModel: Model<ClientLead>) { }
    async clientLeadList(clientLeadListDto: ClientLeadListDto): Promise<any> {
        const { search, facilityId, page = 1, pageSize = 10 } = clientLeadListDto;

        const andConditions: any[] = [];

        if (facilityId && Types.ObjectId.isValid(facilityId)) {
            andConditions.push({ facilityId: new Types.ObjectId(facilityId) });
        }

        if (search) {
            andConditions.push({
                $or: [
                    { firstName: { $regex: search, $options: 'i' } },
                    { lastName: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } },
                    { phone: { $regex: search, $options: 'i' } },
                ]
            });
        }

        const query = andConditions.length ? { $and: andConditions } : {};

        // Step 1: Fetch all matching leads without pagination
        const clientLeads = await this.clientLeadModel.find({
            $or: [
                { isConvertedToClient: false },
                { isConvertedToClient: { $exists: false } }
            ],
            ...query
        }, {
            firstName: 1,
            lastName: 1,
            email: 1,
            phone: 1,
            minors: 1,
            createdAt: 1,
            isConvertedToClient: 1,
        }).populate('facilityId', 'facilityName').sort({ createdAt: -1 }).exec();

        const allClients = clientLeads.flatMap((client: any) => {
            const parentClient = {
                _id: client._id,
                firstName: client.firstName,
                lastName: client.lastName,
                email: client.email,
                phone: client.phone,
                isConvertedToClient: client.isConvertedToClient,
                createdAt: client.createdAt,
                facilityId: client.facilityId,
                flag: 'main',
            };

            const minors = (client.minors || []).map((minor: any) => ({
                _id: minor._id,
                firstName: minor.firstName,
                lastName: minor.lastName,
                dob: minor.dob,
                gender: minor.gender,
                parentClientId: client._id,
                facilityId: client.facilityId,
                isConvertedToClient: false,
                createdAt: client.createdAt,
                flag: 'minor',
            }));

            return [parentClient, ...minors];
        });

        const totalClientLead = allClients.length;
        const start = (page - 1) * pageSize;
        const paginatedClients = allClients.slice(start, start + pageSize);

        return {
            clientLead: paginatedClients,
            totalClientLead,
            page,
            pageSize,
            totalPages: Math.ceil(totalClientLead / pageSize),
        };
    }





    async clientLeadDetail(clientLeadId: string): Promise<any> {
        const clientLead = await this.clientLeadModel.findById(clientLeadId).exec();
        return clientLead;
    }
    async convertLeadToClient(): Promise<any> {

    }
    async clientMinorList(clientLeadId: string): Promise<any> {
        const clientMinorList = await this.clientLeadModel.aggregate([
            { $match: { _id: new Types.ObjectId(clientLeadId) } },
            {
                $project: {
                    minors: {
                        $filter: {
                            input: '$minors',
                            as: 'minor',
                            cond: {
                                $or: [
                                    { $eq: ['$$minor.isMinorAdded', false] },
                                    { $not: ['$$minor.isMinorAdded'] }
                                ]
                            }
                        }
                    },
                    _id: 1,
                    createdAt: 1
                }
            }
        ]);

        return clientMinorList.length > 0 ? clientMinorList[0] : [];
    }
}

