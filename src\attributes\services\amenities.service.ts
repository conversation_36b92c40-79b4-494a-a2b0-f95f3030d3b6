import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Amenities } from "../schema/amenities.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { Attributes } from "../schema/attribute.schema";
import { AmenitiesDto } from "../dto/amenities.dto";
import { AmenitiesListDto } from "../dto/amenities-list.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";

@Injectable()
export class AmenitiesService {
    constructor(
        @InjectModel(Amenities.name) private AmenityModel: Model<Amenities>,
        @InjectModel(Attributes.name) private AttributeModel: Model<Attributes>,
        @InjectModel(StaffProfileDetails.name) private readonly StaffProfileModel: Model<StaffProfileDetails>,
        private readonly transactionService: TransactionService,
    ) { }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            // case ENUM_ROLE_TYPE.USER:
            //     return user._id

            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        if (!organizationId) {
            throw new BadRequestException("Access denied")
        }
        return organizationId;
    }


    async createAmenities(amenitiesDto: AmenitiesDto, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            let data = {
                organizationId: organizationId,
                amenityType: amenitiesDto.amenityType,
                name: amenitiesDto.name,
            };
            let createAmenity = new this.AmenityModel(data);
            let amenityDetails = await createAmenity.save({ session });
            await this.transactionService.commitTransaction(session);
            return amenityDetails;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async amenitiesListing(amenitiesListDto: AmenitiesListDto, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const pageSize = amenitiesListDto.pageSize ?? 10;
        const page = amenitiesListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let query = {
            organizationId: organizationId,
        };

        if (amenitiesListDto.search) {
            const titleQueryString = amenitiesListDto.search.trim().split(" ").join("|");
            query["$or"] = [{ name: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        }
        const total = await this.AmenityModel.countDocuments(query);
        const data = await this.AmenityModel.find(query, { organizationId: 0 }).sort({ createdAt: -1 }).limit(pageSize).skip(skip).exec();
        return {
            list: data,
            count: total,
        };
    }

    async amenityUpdate(updateAmenityDto: AmenitiesDto, amenityId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            let amenityData = {
                amenityType: updateAmenityDto.amenityType,
                name: updateAmenityDto.name,
            };
            let updateAmenity = await this.AmenityModel.findOneAndUpdate(
                {
                    _id: amenityId,
                    organizationId: organizationId,
                },
                {
                    $set: amenityData,
                },
                {
                    new: true,
                    session,
                },
            );

            if (!updateAmenity) throw new BadRequestException("Amenity not found");
            await this.transactionService.commitTransaction(session);
            return updateAmenity;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async amenityDetails(amenityId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        let amenityDetails = await this.AmenityModel.findOne({ _id: amenityId, organizationId: organizationId });
        if (!amenityDetails) throw new BadRequestException("Amenity not found");
        return amenityDetails;
    }

    async amenityGroupDetails(user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        let pipeline = [
            {
                $match: {
                    organizationId: organizationId,
                },
            },
            {
                $group: {
                    _id: "$amenityType",
                    list: {
                        $push: {
                            name: "$name",
                            _id: "$_id",
                        },
                    },
                },
            },
        ];
        return await this.AmenityModel.aggregate(pipeline);
    }

    async amenityDelete(amenityId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        let deleteAmenity = await this.AmenityModel.findOneAndDelete({ _id: amenityId, organizationId: organizationId });
        if (!deleteAmenity) throw new BadRequestException("Amenity not found");
        return deleteAmenity;
    }
}
