import { Injectable } from '@nestjs/common';
import { ActiveTimeFrame } from '../schemas/active-time-frame.schema';
import { DayIndexToWeekMap, DaysOfWeek, DaysOfWeekMap } from '../enums/days-of-week.enum';

@Injectable()
export class ActiveTimeFrameService {
  /**
   * Checks if an item is active based on its active time frames
   * @param activeTimeFrames Array of active time frames for the item
   * @param currentDate Optional date to check against (defaults to current time)
   * @returns boolean indicating if the item is active
   */
  isActive(activeTimeFrames: ActiveTimeFrame[], currentDate: Date = new Date()): boolean {
    // If no time frames are defined, the item is always active
    if (!activeTimeFrames || activeTimeFrames.length === 0) {
      return true;
    }

    // Get current day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    const currentDayOfWeek = DayIndexToWeekMap[currentDate.getDay()];

    // Format current time as HH:MM
    const currentHours = currentDate.getHours().toString().padStart(2, '0');
    const currentMinutes = currentDate.getMinutes().toString().padStart(2, '0');
    const currentTime = `${currentHours}:${currentMinutes}`;

    // Check each time frame
    for (const timeFrame of activeTimeFrames) {
      // If this is a date-specific time frame
      if (timeFrame.date) {
        const timeFrameDate = new Date(timeFrame.date);

        // Check if the current date matches the time frame date (ignoring time)
        if (
          timeFrameDate.getDate() === currentDate.getDate() &&
          timeFrameDate.getMonth() === currentDate.getMonth() &&
          timeFrameDate.getFullYear() === currentDate.getFullYear()
        ) {
          // Check if current time is within the time frame
          if (currentTime >= timeFrame.startTime && currentTime <= timeFrame.endTime) {
            return true;
          }
        }
      }
      // If this is a recurring day-based time frame
      else if (timeFrame.dayOfWeek) {
        // Check if current day matches the day of week
        if (currentDayOfWeek === timeFrame.dayOfWeek) {
          // Check if current time is within the time frame
          if (currentTime >= timeFrame.startTime && currentTime <= timeFrame.endTime) {
            return true;
          }
        }
      }
    }

    // If no time frames match, the item is not active
    return false;
  }
}
