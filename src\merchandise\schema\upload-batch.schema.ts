import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UploadBatch {
  @Prop({ type: Types.ObjectId, required: true })
  organizationId: Types.ObjectId;

  @Prop({ required: true })
  filePath: string;

  @Prop({ default: 0 })
  totalRows: number;

  @Prop({ default: 0 })
  processedRows: number;

  @Prop({ default: 0 })
  successCount: number;

  @Prop({ default: 0 })
  failedCount: number;

  // ⬇️ ONLY these four
  @Prop({
    type: String,
    enum: ['queued', 'processing', 'completed', 'failed'],
    default: 'queued',
  })
  status: 'queued' | 'processing' | 'completed' | 'failed';

  @Prop()
  errorFilePath?: string;

  @Prop({ default: false })
  isUpdate: boolean;

  @Prop()
  initiatedByUserId?: string;

  @Prop()
  message?: string;

  @Prop({ type: Number, default: 0 })
  processedCount: number;
}

export type UploadBatchDocument = HydratedDocument<UploadBatch>;
export const UploadBatchSchema = SchemaFactory.createForClass(UploadBatch);
