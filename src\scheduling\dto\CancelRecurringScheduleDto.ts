import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsNotEmpty,
    IsString,
    IsDate,
    Validate,
    ValidatorConstraint,
    ValidatorConstraintInterface,
    ValidationArguments,
    IsOptional,
    IsEnum,
    ValidateIf,
    IsArray,
    ArrayNotEmpty,
    IsMongoId
} from "class-validator";
import { Type } from "class-transformer";
import { DateRange } from "src/utils/enums/date-range-enum";

@ValidatorConstraint({ name: "IsEndDateAfterStartDate", async: false })
export class IsEndDateAfterStartDate implements ValidatorConstraintInterface {
    validate(endDate: Date, args: ValidationArguments): boolean {
        const object = args.object as any;
        const startDate: Date = object[args.constraints[0]];
        return startDate && endDate > startDate;
    }

    defaultMessage(args: ValidationArguments): string {
        return "End Date must be greater than Start Date";
    }
}

export class CancelRecurringScheduleDto {
    @ApiPropertyOptional({
        description: "Type of date range: 'single' or 'multiple'",
        enum: DateRange,
        default: DateRange.SINGLE
    })
    @IsOptional()
    @IsEnum(DateRange, { message: "Date range must be either 'single' or 'multiple'" })
    dateRange?: DateRange;

    @ValidateIf(o => o.dateRange === DateRange.MULTIPLE)
    @ApiProperty({ description: "Start date of the recurring session to cancel", example: "2025-07-23", required: false })
    @IsNotEmpty({ message: "Start Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "Start Date must be a valid date" })
    startDate?: Date;

    @ValidateIf(o => o.dateRange === DateRange.MULTIPLE)
    @ApiProperty({ description: "End date of the recurring session to cancel", example: "2025-07-29", required: false })
    @IsNotEmpty({ message: "End Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "End Date must be a valid date" })
    @Validate(IsEndDateAfterStartDate, ["startDate"])
    endDate?: Date;
}

export class CancelSchedulesByIdsDto {
    @ApiProperty({
        description: "Array of schedule IDs to cancel",
        type: [String],
        example: ["64cfe2b14f0c9c1234567890", "64cfe2b14f0c9c1234567891"]
    })
    @IsArray({ message: "scheduleIds must be an array" })
    @ArrayNotEmpty({ message: "scheduleIds cannot be empty" })
    @IsMongoId({ each: true, message: "Each scheduleId must be a valid MongoDB ObjectId" })
    @Type(() => String)
    scheduleIds: string[];
}