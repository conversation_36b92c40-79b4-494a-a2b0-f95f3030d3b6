import {
    Body,
    Controller,
    Get,
    HttpCode,
    Post,
    Req,
    UseGuards,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { UserService } from "../services/user.service";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiBody, ApiConsumes } from "@nestjs/swagger";
import { User } from "../schemas/user.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { CreateUserDto } from "../dto/user-create-profile.dto";

@ApiTags("User")
@ApiBearerAuth()
@Controller("user")
export class UserController {
    constructor(private userService: UserService) { }

    @Post("/register")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.USER)
    @ApiOperation({ summary: "Register a new user" })
    async registerUser(@GetUser() requestingUser, @Body() registerUserDto: CreateUserDto): Promise<User> {
        try {
            return await this.userService.registerUser(registerUserDto, requestingUser._id);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Get("/get-profile-details")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.USER)
    @ApiOperation({ summary: "Fetch profile details" })
    async getUserDetailsById(@GetUser() requestingUser): Promise<any> {
        try {
            const user = await this.userService.getUserDetailsById(requestingUser);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }
}
