
export enum RELATION_ENUM {
    FATHER = 'father',
    <PERSON><PERSON>H<PERSON> = 'mother',
    SISTER = 'sister',
    BROTH<PERSON> = 'brother',
    AUNTY = 'aunty',
    UNCLE = 'uncle',
    GRAND_FATHER = 'grand father',
    GRAND_MOTHER = 'grand mother',
    GUARDIAN = 'guardian',
    <PERSON><PERSON><PERSON><PERSON> = 'husband',
    WIF<PERSON> = 'wife',
    SON = 'son',
    DAUGHTER = 'daughter',
    SPOUSE = 'spouse',
    PARTNER = 'partner',
    FRIEND = 'friend',
    COUSIN = 'cousin',
    NEPHEW = 'nephew',
    NIECE = 'niece',
    GRAND_SON = 'grand son',
    G<PERSON>ND_DAUGHTER = 'grand daughter',
    GRAND_CHILD = 'grand child',
    <PERSON><PERSON>ND_PARENT = 'grand parent',
    PARENT = 'parent',
    CHILD = 'child',
    SIBLING = 'sibling',
    RELATIVE = 'relative',
    LEGAL_GUARDIAN = 'legal guardian',
    OTHER = 'other',
}