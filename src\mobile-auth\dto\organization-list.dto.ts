import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class OrganizationListDto {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Knox",
        required:true
    })
   
    @IsString({ message: "Search must be a string" })
    @IsNotEmpty({ message: "Search must not be empty" })
    search: string;
}
