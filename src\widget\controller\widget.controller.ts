import { BadRequestException, Body, Controller, Get, HttpCode, HttpException, HttpStatus, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { widgetService } from "../service/widget.service";

@ApiTags("widget-api")
@ApiBearerAuth()
@Controller("widget")

export class widgetController {
    constructor(private widgetService: widgetService) { }
    @Post("/get-service-type")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Service Type" })
    async getServiceType(@Body() body: any) {
        return await this.widgetService.getServiceType(body)
    }

    @Post('get-service-type-option')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Service Type" })
    async getServiceOption(@Body() body: any) {
        return await this.widgetService.getServiceOptionType(body)
    }

    @Post('get-trainer-availablity')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Trainer Availablity" })
    async getTrainerAvailablity(@Body() body: any) {
        return await this.widgetService.getAllTrainersTimeSlots({
            organizationId: body.organizationId,
            facilityIds: body.facilityIds,
            startDate: body.startDate,
            endDate: body.endDate,
            classType: body.classType ?? 'personalAppointment',
            serviceCategoryId: body.serviceCategoryId,
            subTypeId: body.subTypeId,
            userIds: body.userIds,
            clientUserId: body.clientUserId,
        });
    }

    @Post('get-course-list')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Course List" })
    async getCourseList(@Body() body: any) {
        return await this.widgetService.getCourseList(body);
    }

    @Post('get-course-details')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Course Detail" })
    async getCourseDetail(@Body() body: any) {
        return await this.widgetService.getCourseDetail(body);
    }

    @Post('get-trainer-time-slots')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Trainer Time Slots based on subtype and availability" })
    async getTrainerTimeSlots(@Body() body: any) {
        return await this.widgetService.getTrainerTimeSlots(body);
    }

    @Post('schedule-personal-appontment')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Schedule Personal Appointment" })
    async schedulePersonalAppointment(@Body() body: any) {
        console.log(body,"body")
        return await this.widgetService.schedulePersonalAppointment(body);
    }
    @Post("get-rooms-availability-bookings")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get room-wise availability for classType='bookings' on a given date" })
    async getRoomsAvailabilityForBookings(@Body() body: any) {
        return await this.widgetService.getRoomsAvailabilityForBookings({
            organizationId: body.organizationId,
            facilityId: body.facilityId,
            serviceCategoryId: body.serviceCategoryId,
            subTypeId: body.subTypeId,
            date: body.date,

        });
    }
    @Post("/list-by-service-category")
    @ApiOperation({ summary: "Pricing list by service category" })
    async pricingListByServiceCategory(@Body() pricingListDto: any): Promise<any> {
        let data = await this.widgetService.pricingListByService(pricingListDto);
        return {
            message: "Pricing list by service category",
            data: data,
        };
    }
    @Post("/forgot-password")
    @ApiOperation({ summary: "Forgot password of the API" })
    async setPassword(@Body() body: any): Promise<any> {
        let data = await this.widgetService.sendForgotPasswordLink(body)
        return {
            message: "Password Reset Link Send Successfully",
            data: data
        }
    }
}

