import { IDatabaseCreateOptions, IDatabaseFindAllOptions, IDatabaseGetTotalOptions, IDatabaseCreateManyOptions, IDatabaseSaveOptions, IDatabaseDeleteManyOptions, IDatabaseFindOneOptions, IDatabaseOptions } from 'src/common/database/interfaces/database.interface';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';
import { PermissionUpdateRequestDto } from 'src/policy/dtos/request/permission.update.request.dto';
import { PermissionGetResponseDto } from 'src/policy/dtos/response/permission.get.response.dto';
import { PermissionListResponseDto } from 'src/policy/dtos/response/permission.list.response.dto';
import { PermissionDocument } from 'src/policy/repository/entities/permission.entity';

export interface IPermissionService {
    findAll(find?: Record<string, any>, options?: IDatabaseFindAllOptions): Promise<PermissionDocument[]>;
    getTotal(find?: Record<string, any>, options?: IDatabaseGetTotalOptions): Promise<number>;
    findAllActive(find?: Record<string, any>, options?: IDatabaseFindAllOptions): Promise<PermissionDocument[]>;
    findOneById(_id: string, options?: IDatabaseFindOneOptions): Promise<PermissionDocument>;
    findOne(find: Record<string, any>, options?: IDatabaseFindOneOptions): Promise<PermissionDocument>;
    findOneByName(name: string, options?: IDatabaseFindOneOptions): Promise<PermissionDocument>;
    existByName(name: string, options?: IDatabaseOptions): Promise<boolean>;
    create(data: PermissionCreateRequestDto, options?: IDatabaseCreateOptions): Promise<PermissionDocument>;
    update(repository: PermissionDocument, data: PermissionUpdateRequestDto, options?: IDatabaseSaveOptions): Promise<PermissionDocument>;
    active(repository: PermissionDocument, options?: IDatabaseSaveOptions): Promise<PermissionDocument>;
    inactive(repository: PermissionDocument, options?: IDatabaseSaveOptions): Promise<PermissionDocument>;
    deleteMany(find: Record<string, any>, options?: IDatabaseDeleteManyOptions): Promise<boolean>;
    createMany(data: PermissionCreateRequestDto[], options?: IDatabaseCreateManyOptions): Promise<boolean>;
    mapList(permissions: PermissionDocument[]): PermissionListResponseDto[];
    mapGet(permission: PermissionDocument): PermissionGetResponseDto;
}