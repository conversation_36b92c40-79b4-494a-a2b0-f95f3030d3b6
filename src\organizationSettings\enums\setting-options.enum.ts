export enum SettingOptionsKeyEnums {
    ROOM = 'settings_room',
    CLIENT_ONBOARDING = 'settings_client_onboarding',
    STAFF_ONBOARDING = 'settings_staff_onboarding',
    FEATURE_TABS = 'settings_feature_tabs',
    AMENITIES = 'settings_amenities',
    MEMBERSHIP = 'settings_membership',
    ANNOUNCEMENT = 'settings_announcement',
}

export const SubSettingOptionsKeyEnums = {
    CLIENT_ONBOARDING_ASSESSMENT : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_assessment`,
    CLIENT_ONBOARDING_WIGHT : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_weight`,
    CLIENT_ONBOARDING_MEASUREMENT : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_measurement`,
    CLIENT_ONBOARDING_POLICIES : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_policies`,
    CLIENT_ONBOARDING_FACILITY_WAIVER : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_facility_waiver`,
    CLIENT_ONBOARDING_FACILITY_SAFETY_BRIEFING : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_facility_safety_briefing`,
    CLIENT_ONBOARDING_FACILITY_CHECK_ID : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_facility_check_id`,
    CLIENT_ONBOARDING_NOTE : `sub${SettingOptionsKeyEnums.CLIENT_ONBOARDING}_note`,

    //----
    STAFF_ONBOARDING_PIN : `sub${SettingOptionsKeyEnums.STAFF_ONBOARDING}_pin`,
    STAFF_ONBOARDING_SKILL_EXPERIENCE : `sub${SettingOptionsKeyEnums.STAFF_ONBOARDING}_skill_experience`,
    STAFF_ONBOARDING_SKILL_PAYRATE : `sub${SettingOptionsKeyEnums.STAFF_ONBOARDING}_skill_payrate`,

} as const;

// Replace enum with type
// ... existing code ...


export const SettingKeyEnums = {...SettingOptionsKeyEnums, ...SubSettingOptionsKeyEnums};
