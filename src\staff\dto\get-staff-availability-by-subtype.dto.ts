import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsDateString, IsArray, ValidateNested, IsMongoId, IsEnum } from "class-validator";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";

export class StaffAvailabilityByTypeDto {
    @ApiProperty({
        description: "The IDs of the users",
        example: ["66cecb432351713ae4447a6b", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsOptional()
    userId?: Types.ObjectId;

    @ApiProperty({
        description: "The IDs of the Branches where the trainer is assigned.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsNotEmpty()
    facilityId?: Types.ObjectId;

    @ApiProperty({
        description: "Date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Date is required" })
    date: Date;

    @ApiProperty({
        description: "The ID of the TimeSlot",
        example: "66cecb432351713ae4447a6b",
    })
    @IsOptional()
    timeSlotId?: string;

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    @IsNotEmpty()
    classType?: ClassType;

    @ApiProperty({
        description: "The IDs of the SubTypes for the Staff",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
        required: false,
    })
    @IsNotEmpty()
    subTypeId: string;

    @ApiProperty({
        description: "The IDs of the SubTypes for the Staff",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
        required: false,
    })
    @IsNotEmpty()
    serviceCategory: string;
}

