import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>rra<PERSON>, IsMongoId, IsNotEmpty } from "class-validator";

export class AssignPricingDto {
    @ApiProperty({
        description: "The list of selected pricing IDs",
        example: ["66cecb432351713ae4447a6b", "76fdbfd5235472b3cd3548e6"],
        required: true,
    })
    @IsArray({ message: "Pricing IDs must be an array" })
    @IsMongoId({ each: true, message: "Each pricing ID must be a valid MongoDB ObjectId" })
    @IsNotEmpty({ message: "Pricing IDs are required" })
    pricingIds: string[];

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required service category details" })
    @IsMongoId({ message: "Required valid service category details" })
    serviceCategoryId: string;

    @ApiProperty({
        description: "The Id of the subType (single value from UI)",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required subType details" })
    @IsMongoId({ message: "Required valid subType details" })
    subTypeId: string; 
}