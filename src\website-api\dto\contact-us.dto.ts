import { IsEmail, IsNotEmpty, IsPhone<PERSON>umber, IsString, Length } from 'class-validator';
import { ApiProperty } from "@nestjs/swagger";

export class ContactUsDemoDto {
    @ApiProperty({
        description: "Name of the Person",
        example: "<PERSON><PERSON>",
    })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({
        description: "Email of the Person who want to book the Demo",
        example: "<EMAIL>",
    })
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: "Message of the person",
        example: "Hey can we schedule demo ",
    })
    @IsNotEmpty()
    message: string;
}

