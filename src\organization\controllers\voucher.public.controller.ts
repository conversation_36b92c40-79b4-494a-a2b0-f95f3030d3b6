import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiParam, ApiProperty, ApiResponse, ApiTags } from "@nestjs/swagger";
import { GetDelegatedUser, GetUser } from "src/auth/decorators/get-user.decorator";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { GetOrganizationId } from "../decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { VoucherCreateDto } from "../dto/create-voucher.dto";
import { VoucherService } from "../services/voucher.service";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { VoucherStatusUpdateDto, VoucherUpdateDto } from "../dto/update-voucher.dto";
import { PaginationQuery } from "src/common/pagination/decorators/pagination.decorator";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from "src/utils/decorators/pagination-query.decorator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { VoucherListDto } from "../dto/list-voucher.dto";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { MongoIdPipeTransform } from "src/common/database/pipes/mongo-id.pipe";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { VoucherResponseDto } from "../dto/detail-voucher.response.dto";
import { IResponse, IResponsePaging } from "src/common/response/interfaces/response.interface";
import moment from "moment";
import { VoucherPurchasedResponseDto } from "../dto/purchased-voucher.response.dto";

@ApiTags("modules.voucher")
@ApiBearerAuth()
@Controller("/public/voucher")
export class VoucherPublicController {
    constructor(
        private readonly voucherService: VoucherService,
        private readonly paginationService: PaginationService,
    ) { }

    @ApiOperation({ summary: "User's purchase Voucher List" })
    @ResponsePaging("voucher.list")
    @Get('/purchased/list')
    @AuthJwtAccessProtected()
    async usersVoucherList(
        @PaginationQuery({
            defaultOrderBy: 'updatedAt',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['updatedAt'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() query: VoucherListDto,
        @Body() body: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @GetUser('_id') userId: IUserDocument
    ): Promise<IResponsePaging<VoucherPurchasedResponseDto>> {
        const filter: any = {
            organizationId: organizationId,
            userId: userId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
            isExpired: false,
            isActive: true,
            startDate: { $lte: moment().utc(true).startOf('day').toDate() },
            endDate: { $gte: moment().utc(true).endOf('day').toDate() },
            search: _search
        };

        if (body.isActive !== undefined) filter.isActive = body.isActive;

        const data = await this.voucherService.usersVoucherList(filter, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });

        return {
            _pagination: {
                total: data.count,
                totalPage: this.paginationService.totalPage(data.count, _limit),
                pageSize: query.pageSize,
                page: query.page,
            },
            data: data.list,
        };
    }

    @ApiOperation({ summary: "Voucher Details" })
    @ApiParam({ name: 'voucherId', description: 'Voucher ID', example: "659d268dee4b6081dacd41fd OR voucherCode", type: 'string', required: true })
    @ApiResponse({ status: 200, description: "Returns the voucher details", type: VoucherPurchasedResponseDto })
    @Response("voucher.details")
    @Get('/purchased/:voucherId/get')
    @AuthJwtAccessProtected()
    async voucherPurchasedDetails(
        @Param("voucherId") voucherId: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<IResponse<VoucherPurchasedResponseDto>> {
        const filter = {
            $or: [
                { _id: voucherId },
                { voucherCode: voucherId }
            ],
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }
        const data = await this.voucherService.voucherPurchasedDetails(filter);
        return {
            data
        };
    }

}
