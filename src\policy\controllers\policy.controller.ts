
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PolicyService } from 'src/policy/services/policy.service';
import { PaginationDto } from 'src/utils/dto/pagination.dto';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { Response, ResponsePaging } from 'src/common/response/decorators/response.decorator';
import { MongoIdPipeTransform } from 'src/common/database/pipes/mongo-id.pipe';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PolicyAssignRequestDto } from '../dtos/request/policy.assign.request.dto';
import { GetUserPipe } from 'src/users/pipes/get.user.pipe';
import { UserDocument } from 'src/users/schemas/user.schema';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { PolicyAbilityProtected } from '../decorators/policy.decorator';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';

@ApiTags('modules.policy')
@Controller({
    version: '1',
    path: '/policy',
})
@ApiBearerAuth()
export class PolicyController {
    constructor(
        private readonly policyService: PolicyService,
        private readonly paginationService: PaginationService,
    ) { }

    @ApiOperation({ summary: 'Get a policy by ID' })
    @Response('policy.get')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_READ)
    @AuthJwtAccessProtected()
    @Get('/:id/get')
    async findOneById(@Param('id', MongoIdPipeTransform) id: IDatabaseObjectId): Promise<any> {
        const policy = await this.policyService.findOneById(id, {
            join: [
                {
                    path: 'permissions',
                    select: '-createdAt -updatedAt -__v',
                }
            ]
        });
        return {
            data: policy
        };
    }

    @ApiOperation({ summary: 'Get policy permissions for settings' })
    @ResponsePaging('policy.setting.permissions')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_READ)
    @AuthJwtAccessProtected()
    @Get('/:id/permissions-setting')
    async getPermissions(
        @Param('id', MongoIdPipeTransform) id: IDatabaseObjectId,
    ): Promise<any> {
        const { data, total } = await this.policyService.getAllPermissionsForPolicySetting(
            {
                id,
                isActive: true,
            },

            {
                paging: {
                    limit: 2000,
                    offset: 0,
                },
            }
        );
        return {
            _pagination: { total },
            data: data
        };
    }

    @Response('Fetched successfully')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_READ)
    @AuthJwtAccessProtected()
    @Get('/:subjectType/get-subject')
    @ApiOperation({ summary: 'Get policies grouped by subject and subject' })
    async findBySubject(@Param('subjectType') type: string): Promise<any> {
        const { data, total } = await this.policyService.getAllForSettingPolicies(
            {
                isActive: true,
                type: type
            },
            {
                paging: {
                    limit: 1000,
                    offset: 0,
                },
            }
        );

        const subjects = data.length ? data[0]?.subjects[0] : null;
        return {
            data: subjects
        };
    }

    @ApiOperation({ summary: 'Get all policies' })
    @ResponsePaging("policy.setting-list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_READ)
    @AuthJwtAccessProtected()
    @Post('/list')
    async findAll(
        @Body() { pageSize: limit, page }: Omit<PaginationDto, 'search'>
    ): Promise<any> {
        const _limit = limit || 10;
        const _page = page || 1;
        const _offset = this.paginationService.offset(_page, _limit);

        const { data: policies, total } = await this.policyService.getAllForSettingPolicies(
            {
                isActive: true,
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
            }
        );
        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        return {
            _pagination: { totalPage, total, page: _page },
            data: policies
        };;
    }


    @ResponsePaging("policy.users.list")
    @ApiOperation({ summary: 'Get all policies by user for setting' })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_READ)
    @AuthJwtAccessProtected()
    @Post('/:userId/list')
    async getUsersUnAssignedPolicies(
        @Body() { pageSize: limit, page }: Omit<PaginationDto, 'search'>,
        @Param('userId', MongoIdPipeTransform, GetUserPipe) user: UserDocument,
    ) {
        const _limit = limit || 1000;
        const _page = page || 1;
        const _offset = 0 //this.paginationService.offset(_page, _limit);

        const { data, total } = await this.policyService.getAllUserSettingPolicies({
            isActive: true,
            userId: user._id
        }, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
        });

        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        return {
            _pagination: { totalPage, total, page: _page },
            data,
        };

    }

    @Response("policy.list")
    @ApiOperation({ summary: 'Get all policies by user' })
    @AuthJwtAccessProtected()
    @Get('/:userId/assigned')
    async getUsersPolicies(
        @Param('userId', MongoIdPipeTransform) userId: IDatabaseObjectId,
    ) {
        const { data } = await this.policyService.getUsersAllPolicy(userId) as any;
        return {
            data: data,
        };

    }

    @Response("User policy updated successfully")
    @HttpCode(HttpStatus.ACCEPTED)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.POLICY_ASSIGN)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: 'Assign policies to a user' })
    @Post('/:userId/assign')
    async setUsersPolicies(
        @Param('userId', MongoIdPipeTransform) userId: IDatabaseObjectId,
        @Body() body: PolicyAssignRequestDto,
    ) {
        const data = await this.policyService.setUsersPolicies(userId, body);
        return {
            data: true
        };

    }

}