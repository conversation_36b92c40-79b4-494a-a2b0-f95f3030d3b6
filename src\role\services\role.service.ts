import { Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Document, Types } from 'mongoose';
import {
    IDatabaseCreateOptions,
    IDatabaseFindAllOptions,
    IDatabaseGetTotalOptions,
    IDatabaseCreateManyOptions,
    IDatabaseSaveOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseFindOneOptions,
    IDatabaseOptions,
    IDatabaseUpdateManyOptions,
    IDatabaseUpdateOptions,
} from 'src/common/database/interfaces/database.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { RoleCreateRequestDto } from 'src/role/dtos/request/role.create.request.dto';
import { RoleUpdateRequestDto } from 'src/role/dtos/request/role.update.request.dto';
import { RoleGetResponseDto } from 'src/role/dtos/response/role.get.response.dto';
import { RoleListResponseDto } from 'src/role/dtos/response/role.list.response.dto';
import { RoleShortResponseDto } from 'src/role/dtos/response/role.short.response.dto';
import {
    RoleDocument,
    RoleEntity,
} from 'src/role/repository/entities/role.entity';
import { RoleRepository } from 'src/role/repository/repositories/role.repository';
import { IPaginationOptions } from 'src/common/pagination/interfaces/pagination.interface';
import { PipelineStage } from 'mongoose';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PolicyService } from 'src/policy/services/policy.service';

@Injectable()
export class RoleService {
    constructor(
        private readonly roleRepository: RoleRepository,
        private readonly policyService: PolicyService
    ) { }

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<RoleDocument[]> {
        return this.roleRepository.findAll(find, options);
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.roleRepository.getTotal(find, options);
    }

    async findAllActive(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<RoleDocument[]> {
        return this.roleRepository.findAll(
            { ...find, isActive: true },
            options
        );
    }

    async getTotalActive(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.roleRepository.getTotal(
            { ...find, isActive: true },
            options
        );
    }

    async findAllActiveByType(
        type: ENUM_ROLE_TYPE,
        options?: IDatabaseFindAllOptions
    ): Promise<RoleDocument[]> {
        return this.roleRepository.findAll({ type, isActive: true }, options);
    }

    async findAllByTypes(
        types: ENUM_ROLE_TYPE[],
        options?: IDatabaseFindAllOptions
    ): Promise<RoleDocument[]> {
        return this.roleRepository.findAll(
            {
                type: {
                    $in: types,
                },
            },
            options
        );
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<RoleDocument> {
        return this.roleRepository.findOneById(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<RoleDocument> {
        return this.roleRepository.findOne(find, options);
    }

    async findOneByName(
        name: string,
        options?: IDatabaseFindOneOptions
    ): Promise<RoleDocument> {
        return this.roleRepository.findOne({ name }, options);
    }

    async findOneByType(
        type: string,
        options?: IDatabaseFindOneOptions
    ): Promise<RoleDocument> {
        return this.roleRepository.findOne({ type }, options);
    }

    async findOneActiveById(
        _id: string,
        options?: IDatabaseFindOneOptions
    ): Promise<RoleDocument> {
        return this.roleRepository.findOne({ _id, isActive: true }, options);
    }

    async existByName(
        name: string,
        options?: IDatabaseOptions
    ): Promise<boolean> {
        return this.roleRepository.exists(
            {
                name,
            },
            options
        );
    }

    async create(
        { name, description, type, policies: permissions }: RoleCreateRequestDto,
        options?: IDatabaseCreateOptions
    ): Promise<RoleDocument> {
        const create: RoleEntity = new RoleEntity();
        create.name = name;
        create.description = description;
        create.type = type;
        create.policies = [];
        create.isActive = true;

        return this.roleRepository.create<RoleEntity>(create, options);
    }

    async update(
        repository: RoleDocument,
        { policies: permissions, type, description }: RoleUpdateRequestDto,
        options?: IDatabaseSaveOptions
    ): Promise<RoleDocument> {
        repository.description = description;
        repository.type = type;
        repository.policies = [];

        return this.roleRepository.save(repository, options);
    }

    async updateOne(
        find: Record<string, any>,
        data: Record<string, any>,
        options?: IDatabaseUpdateOptions
    ): Promise<RoleEntity> {
        return this.roleRepository.update(find, data, options);
    }

    async updateMany(
        find: Record<string, any>,
        data: Partial<RoleEntity>,
        options?: IDatabaseUpdateManyOptions
    ): Promise<any> {
        return this.roleRepository.updateMany(find, data, options);
    }

    async active(
        repository: RoleDocument,
        options?: IDatabaseSaveOptions
    ): Promise<RoleDocument> {
        repository.isActive = true;

        return this.roleRepository.save(repository, options);
    }

    async inactive(
        repository: RoleDocument,
        options?: IDatabaseSaveOptions
    ): Promise<RoleDocument> {
        repository.isActive = false;

        return this.roleRepository.save(repository, options);
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.roleRepository.deleteMany(find, options);

        return true;
    }

    async createMany(
        data: RoleCreateRequestDto[],
        options?: IDatabaseCreateManyOptions
    ): Promise<boolean> {
        const create: RoleEntity[] = data.map(({ _id, type, name, policies }) => {
            const entity: RoleEntity = new RoleEntity();
            entity._id = _id ? new Types.ObjectId(_id) : entity._id;
            entity.type = type;
            entity.isActive = true;
            entity.name = name;
            entity.policies = policies;

            return entity;
        }) as RoleEntity[];

        await this.roleRepository.createMany<RoleEntity>(create, options);

        return true;
    }

    async model() {
        return this.roleRepository.model();
    }

    mapList(roles: RoleDocument[] | RoleEntity[]): RoleListResponseDto[] {
        return plainToInstance(
            RoleListResponseDto,
            roles.map((e: RoleDocument | RoleEntity) =>
                e instanceof Document ? e.toObject() : e
            )
        );
    }

    mapGet(role: RoleDocument | RoleEntity): RoleGetResponseDto {
        return plainToInstance(
            RoleGetResponseDto,
            role instanceof Document ? role.toObject() : role
        );
    }

    mapShort(roles: RoleDocument[] | RoleEntity[]): RoleShortResponseDto[] {
        return plainToInstance(
            RoleShortResponseDto,
            roles.map((e: RoleDocument | RoleEntity) =>
                e instanceof Document ? e.toObject() : e
            )
        );
    }

    async getRolePolicies(
        roleId: IDatabaseObjectId,
        options?: IDatabaseOptions
    ): Promise<RoleDocument> {
        const role = await this.roleRepository.findOneById(roleId, {
            ...options,
            join: [{
                path: 'policies',
                populate: {
                    path: 'permissions',
                    select: '-createdAt -updatedAt -__v',
                }
            }]
        });

        if (!role) {
            throw new NotFoundException({
                statusCode: 404,
                message: 'role.error.notFound',
            });
        }

        return role;
    }

    async getRoleSettingPolicies(
        roleId: IDatabaseObjectId,
        options?: IDatabaseOptions & { paging?: IPaginationOptions }
    ): Promise<{ data: any[]; total: number }> {
        const role = await this.roleRepository.findOneById(roleId, {
            join: [{
                path: 'policies',
                select: '_id'
            }]
        });

        if (!role) {
            throw new NotFoundException({
                statusCode: 404,
                message: 'role.error.notFound',
            });
        }

        const policyIds = role.policies.map((policy) => new Types.ObjectId(policy._id));

        const pipeline: PipelineStage[] = [
            // Stage 0: Match active policies
            {
                $match: {
                    isActive: true
                }
            },
            // Stage 1: Group by module and subject
            {
                $group: {
                    _id: {
                        module: "$module",
                        subject: "$subject"
                    },
                    type: { $first: "$type" },
                    actions: {
                        $push: {
                            _id: "$_id",
                            isActive: "$isActive",
                            subject: "$subject",
                            module: "$module",
                            action: "$action",
                            permitted: {
                                $cond: {
                                    if: { $in: ["$_id", policyIds] },
                                    then: true,
                                    else: false
                                }
                            },
                            description: "$description"
                        }
                    }
                }
            },

            // Stage 2: Group by module
            {
                $group: {
                    _id: "$_id.module",
                    type: { $first: "$type" },
                    subjects: {
                        $push: {
                            subject: "$_id.subject",
                            actions: "$actions"
                        }
                    }
                }
            },

            // Stage 3: Format final result
            {
                $project: {
                    _id: 0,
                    module: "$_id",
                    type: 1,
                    subjects: 1
                }
            },

            // Stage 4: Add pagination
            {
                $facet: {
                    data: [
                        { $sort: { module: 1 } },
                        { $skip: options?.paging?.offset || 0 },
                        { $limit: options?.paging?.limit || 20 },
                    ],
                    total: [{ $count: "total" }],
                },
            }
        ];

        const res = await this.policyService.aggregate<any>(pipeline);
        const data = res[0].data;
        const total = res[0].total[0]?.total || 0;

        return {
            data: data,
            total
        };
    }

    async addPolicies(
        repository: RoleDocument,
        policyIds: string[],
    ): Promise<RoleDocument> {
        // Convert string IDs to ObjectIds
        const policyObjectIds = policyIds.map(id => new Types.ObjectId(id));

        // Verify policies exist
        const policies = await this.policyService.findAll(
            { _id: { $in: policyObjectIds } },
            { select: '_id isDelegated' }
        );

        if (policies.length !== policyIds.length) {
            throw new NotFoundException({
                statusCode: 404,
                message: 'Selected policies not found',
            });
        }

        // Separate delegated and non-delegated policies
        const regularPolicies = policies
            .map(policy => policy._id);

        // Add new policies to existing ones, avoiding duplicates
        repository.policies = Array.from(policyObjectIds)

        return this.roleRepository.save(repository);
    }
}
