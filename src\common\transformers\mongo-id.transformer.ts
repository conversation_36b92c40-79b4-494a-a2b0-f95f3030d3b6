import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { Types } from 'mongoose';

@Injectable()
export class MongoIdTransformPipe implements PipeTransform {
    transform(value: any, metadata: ArgumentMetadata) {
        if (!value) return value;

        try {
            if (Array.isArray(value)) {
                return value.map(item => {
                    if (item instanceof Types.ObjectId) return item;
                    return new Types.ObjectId(item.toString());
                });
            }

            if (value instanceof Types.ObjectId) return value;
            return new Types.ObjectId(value.toString());
        } catch (error) {
            throw new BadRequestException('Invalid MongoDB ObjectId');
        }
    }
}

// Custom parameter decorator
export const TransformToMongoId = () => {
    return (target: any, propertyKey: string) => {
        let value: any;
        
        const getter = () => {
            return value;
        };
        
        const setter = (newVal: any) => {
            try {
                if (Array.isArray(newVal)) {
                    value = newVal.map(item => {
                        if (item instanceof Types.ObjectId) return item;
                        return new Types.ObjectId(item.toString());
                    });
                } else if (newVal) {
                    value = newVal instanceof Types.ObjectId 
                        ? newVal 
                        : new Types.ObjectId(newVal.toString());
                }
            } catch (error) {
                throw new BadRequestException('Invalid MongoDB ObjectId');
            }
        };
        
        Object.defineProperty(target, propertyKey, {
            get: getter,
            set: setter,
            enumerable: true,
            configurable: true,
        });
    };
};