import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

export type PaymentCredentialDocument = PaymentCredential & Document;

@Schema({ timestamps: true })
export class PaymentCredential {
    @Prop({ type: String, required: true })
    keyId: string;
    @Prop({ type: String, required: true })
    keySecret: string;
    @Prop({ type: String, required: false })
    webhookSecret: string;
    @Prop({ type: String, required: true })
    paymentGateway: String;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string
    @Prop({ type: Boolean, default: false })
    isDeleted: boolean;
}

export const PaymentCredentialSchema = SchemaFactory.createForClass(PaymentCredential);
