import { Schema, Types } from 'mongoose';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import {
    DatabaseEntity,
    DatabaseProp,
    DatabaseSchema,
} from 'src/common/database/decorators/database.decorator';
import { IDatabaseDocument } from 'src/common/database/interfaces/database.interface';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { PromotionTableName } from './promotion.entity';
import { Pricing } from 'src/organization/schemas/pricing.schema';
import { User } from 'src/users/schemas/user.schema';
import { Services } from 'src/organization/schemas/services.schema';

export const PromotionItemTableName = 'promotionItems';

@DatabaseEntity({ collection: PromotionItemTableName })
export class PromotionItemEntity extends DatabaseEntityBase {
    @DatabaseProp({
        required: true,
        type: Schema.Types.ObjectId,
        ref: User.name,
        index: true,
    })
    organizationId: Types.ObjectId;

    @DatabaseProp({
        required: true,
        type: Schema.Types.ObjectId,
        ref: PromotionTableName,
        index: true,
    })
    promotion: Types.ObjectId;

    @DatabaseProp({
        required: true,
        enum: ENUM_PRODUCT_ITEM_TYPE,
        index: true,
    })
    itemType: ENUM_PRODUCT_ITEM_TYPE;

    @DatabaseProp({
        required: function (this: PromotionItemEntity) {
            return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
        },
        type: Schema.Types.ObjectId,
        // index: true,
        refPath: Pricing.name,
    })
    item: Types.ObjectId;

    @DatabaseProp({
        required: false,
        validate: function (this: PromotionItemEntity) {
            return this.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT;
        },
        type: Schema.Types.ObjectId,
        index: true,
        default: undefined,
    })
    variantId: Types.ObjectId;

    @DatabaseProp({
        required: true,
        default: true,
        index: true,
        type: Boolean,
    })
    isActive: boolean;
}

export const PromotionItemSchema = DatabaseSchema(PromotionItemEntity);
export type PromotionItemDocument = IDatabaseDocument<PromotionItemEntity>;

PromotionItemSchema.index({ organizationId: 1, promotion: 1, item: 1 }, {  });
