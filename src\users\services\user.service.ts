import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import * as bcrypt from 'bcrypt';
import { User, UserDocument } from "../schemas/user.schema";
import { Connection, Model, Types, UpdateWriteOpResult } from "mongoose";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { UploadService } from "src/utils/services/upload.service";
import { CreateUserDto } from "../dto/user-create-profile.dto";
import { ClientProfileDetails } from "../schemas/user-profile-details.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { PaginationDTO } from "../dto/pagination.dto";
import { IUserDocument } from "../interfaces/user.interface";
import { QueryOptions } from "mongoose";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PolicyAssignDto } from "src/policy/dtos/request/policy.assign.request.dto";
import { PermissionDocument } from "src/policy/repository/entities/permission.entity";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
@Injectable()
export class UserService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(ClientProfileDetails.name) private ClientProfileModel: Model<ClientProfileDetails>,
        @InjectConnection() private readonly connection: Connection,
        private UploadService: UploadService,
    ) { }

    async model() {
        return this.UserModel;
    }

    // find all
    async findAll(
        find?: Record<string, any>,
        options?: QueryOptions<UserDocument>
    ): Promise<UserDocument[]> {
        return await this.UserModel.find(find, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: QueryOptions<UserDocument>
    ): Promise<UserDocument> {
        return await this.UserModel.findOne(find, options);
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: QueryOptions<UserDocument>
    ): Promise<UserDocument> {
        const user = await this.UserModel.findById<UserDocument>(_id, options);
        return user;
    }

    async findOneWithRoleAndPermissions(
        find: Record<string, any>,
        options?: QueryOptions<UserDocument>
    ): Promise<IUserDocument> {
        const user = await this.UserModel.findOne<IUserDocument>(find, options).populate([
            {
                path: "role",
                populate: {
                    path: "policies",
                    populate: {
                        path: "permissions",
                        select: "-createdAt -updatedAt -__v",
                    },
                    select: "-createdAt -updatedAt -__v",
                },
            },
            {
                path: "assignedPolicies",
                populate: {
                    path: "permissions",
                    select: "-createdAt -updatedAt -__v",
                },
                select: "-createdAt -updatedAt -__v",
            },
            {
                path: "restrictedPolicies",
                populate: {
                    path: "permissions",
                    select: "-createdAt -updatedAt -__v",
                }
            },
        ]);
        // user.permissions = await this.getUserPermissionsSet(user);
        return user;
    }

    async getUserPermissionsSet(user: IUserDocument): Promise<ENUM_PERMISSION_TYPE[]> {
        const { role, assignedPolicies, restrictedPolicies } = user;
        if (role.type === ENUM_ROLE_TYPE.SUPER_ADMIN || role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return Object.values(ENUM_PERMISSION_TYPE);
        }
        const restrictedPoliciesSet = new Set(restrictedPolicies.map(policy => policy._id.toString()));
        const policies = [...role.policies, ...assignedPolicies].filter(policy => !restrictedPoliciesSet.has(policy._id.toString()));
        const permissionsSet = new Set<ENUM_PERMISSION_TYPE>();
        policies.forEach(policy => {
            policy.permissions.forEach(permission => {
                permissionsSet.add(permission.type);
            });
        });
        return [...permissionsSet];
    }

    // Update One
    async updateOne(
        find: Record<string, any>,
        data: Partial<UserDocument>,
        options?: QueryOptions<UserDocument>
    ): Promise<UserDocument> {
        return await this.UserModel.findOneAndUpdate(find, {
            $set: data
        }, options);
    }

    // Update Many
    async updateMany(
        find: Record<string, any>,
        data: Partial<UserDocument>,
        options?: QueryOptions<UserDocument>
    ): Promise<UpdateWriteOpResult> {
        return await this.UserModel.updateMany(find, {
            $set: data
        }, options);
    }

    async updatePolicies(
        find: Record<string, any>,
        assignPolicies: Types.ObjectId[],
        restrictPolicies: Types.ObjectId[],
        options?: QueryOptions<UserDocument>
    ): Promise<boolean> {
        // Check for policy conflicts
        const session = await this.UserModel.startSession();
        try {
            session.startTransaction();

            // Add new policy assignments
            await this.UserModel.updateOne(
                find,
                {
                    $set: {
                        assignedPolicies: assignPolicies,
                        restrictedPolicies: restrictPolicies
                    }
                },
                { ...options, session }
            );

            await session.commitTransaction();
            return true;
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    // async updatePolicies(
    //     find: Record<string, any>,
    //     policies: IDatabaseObjectId[],
    //     restrictedPolicies: IDatabaseObjectId[],
    //     options?: QueryOptions<UserDocument>
    // ): Promise<UpdateWriteOpResult> {
    //     if(policies.some(policy => restrictedPolicies.includes(policy))) {
    //         throw new BadRequestException("A policy cannot be assigned and restricted at the same time");
    //     }
    //     return await this.UserModel.updateMany(find, {
    //         $addToSet: { assignedPolicies: { $each: policies } },
    //         $pull: { restrictedPolicies: { $in: restrictedPolicies } }
    //     }, options);
    // }

    async getUserDetailsById(requestingUser: Object): Promise<any> {
        try {
            const id = new Types.ObjectId(requestingUser["_id"]);
            const user = await this.UserModel.aggregate([
                {
                    $match: {
                        _id: new Types.ObjectId(id),
                    },
                },
                {
                    $lookup: {
                        from: "clientprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "userDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$userDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        password: 0,
                        salt: 0,
                        "userDetails._id": 0,
                        "userDetails.createdAt": 0,
                        "userDetails.updatedAt": 0,
                    },
                },
            ]);

            if (user.length === 0) {
                return {
                    message: "User details not found",
                    data: [],
                };
            }
            return {
                message: "User details fetched successfully",
                data: user[0],
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async registerUser(createUserReq: CreateUserDto, id: string): Promise<any> {
        try {
            const userId = new Types.ObjectId(id);
            let user = await this.ClientProfileModel.findOne({ userId: userId });

            if (user) {
                user = await this.ClientProfileModel.findByIdAndUpdate(user._id, createUserReq, { new: true });
                return {
                    message: "User profile updated successfully",
                    data: user,
                };
            } else {
                const lastUser = await this.ClientProfileModel.findOne({})
                    .sort({ clientId: -1 })
                    .select('clientId')
                    .exec();
                const lastClientIdNumber = lastUser ? parseInt(lastUser.clientId.split('-')[1], 10) : 0;
                const nextClientIdNumber = lastClientIdNumber + 1;
                const clientId = `C-${nextClientIdNumber}`;

                const newUser = new this.ClientProfileModel({ ...createUserReq, clientId, userId });
                await newUser.save();
                return {
                    message: "User profile created successfully",
                    data: newUser,
                };
            }
        } catch (error) {
            throw new Error(`Operation failed: ${error.message}`);
        }
    }

    async adminGetUserDetailsById(userId: string): Promise<any> {
        try {
            const id = new Types.ObjectId(userId);
            const user = await this.UserModel.aggregate([
                {
                    $match: {
                        _id: new Types.ObjectId(id),
                    },
                },
                {
                    $lookup: {
                        from: "clientprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        password: 0,
                        salt: 0,
                        "userDetails._id": 0,
                        "userDetails.createdAt": 0,
                        "userDetails.updatedAt": 0,
                    },
                },
            ]);

            if (user.length === 0) {
                return {
                    message: "User details not found",
                    data: [],
                };
            }
            return {
                message: "User details fetched successfully",
                data: user[0],
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async adminGetAllUserList(paginationDTO: PaginationDTO): Promise<any> {
        try {
            let { page, pageSize, search } = paginationDTO
            let query = { role: ENUM_ROLE_TYPE.USER }
            let searchQuery: any = [];
            if (isNaN(page) || page < 1) {
                page = 1;
            }
            if (isNaN(pageSize) || pageSize < 1) {
                pageSize = 10;
            }
            const skip = (page - 1) * pageSize;
            if (search?.length > 0) {
                searchQuery.push(
                    { name: { $regex: search, $options: 'i' } },
                    { mobile: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } });
            }
            if (searchQuery.length > 0) {
                query['$or'] = searchQuery;
            }
            const result = await this.UserModel.aggregate([
                {
                    $match: query
                },
                {
                    $sort: {
                        updatedAt: -1,
                    },
                },
                {
                    $lookup: {
                        from: "clientprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "userDetails"
                    }
                },
                {
                    $unwind: {
                        path: "$userDetails",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $facet: {
                        metadata: [
                            { $count: "total" }
                        ],
                        data: [
                            {
                                $project: {
                                    _id: 1,
                                    userId: "$userDetails.userId",
                                    clientId: "$userDetails.clientId",
                                    name: 1,
                                    mobile: 1,
                                    email: 1,
                                    isActive: 1
                                }
                            }, { $skip: skip },
                            { $limit: pageSize }
                        ]
                    }
                }
            ]);
            const totalUsers = result[0].metadata[0] ? result[0].metadata[0].total : 0;
            const totalPages = Math.ceil(totalUsers / pageSize);

            return {
                message: result[0].data.length > 0 ? "User list fetched successfully" : "User list not found",
                data: result[0].data,
                pagination: {
                    currentPage: page,
                    currentPageSize: pageSize,
                    totalPages: totalPages,
                    totalCount: totalUsers
                }
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async findUsersByRole(
        roleId: Types.ObjectId,
        search: Record<string, any>,
        options: {
            paging?: {
                limit: number;
                offset: number;
            };
            order?: Record<string, any>;
        }
    ): Promise<{
        users: any[];
        total: number;
    }> {
        const { paging, order } = options;

        let searchQuery: any[] = [];
        if (Object.keys(search).length > 0) {
            searchQuery = [
                search.name ? { name: { $regex: search.name, $options: 'i' } } : {},
                search.name ? { mobile: { $regex: search.mobile, $options: 'i' } } : {},
                search.name ? { email: { $regex: search.email, $options: 'i' } } : {}
            ];
        }

        const baseQuery = {
            role: roleId,
            isActive: true,
            ...(searchQuery.length > 0 ? { $or: searchQuery } : {})
        };

        const [users, total] = await Promise.all([
            this.UserModel.aggregate([
                {
                    $match: baseQuery
                },
                {
                    $lookup: {
                        from: "clientprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "userDetails"
                    }
                },
                {
                    $lookup: {
                        from: "roles",  // assuming this is your roles collection name
                        localField: "role",
                        foreignField: "_id",
                        as: "roleInfo"
                    }
                },
                {
                    $unwind: {
                        path: "$userDetails",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $unwind: {
                        path: "$roleInfo",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        email: 1,
                        mobile: 1,
                        isActive: 1,
                        createdAt: 1,
                        "userDetails.clientId": 1,
                        role: {
                            _id: "$roleInfo._id",
                            name: "$roleInfo.name",
                            type: "$roleInfo.type",
                            isActive: "$roleInfo.isActive"
                        }
                    }
                },
                {
                    $sort: order || { createdAt: -1 }
                },
                {
                    $skip: paging?.offset || 0
                },
                {
                    $limit: paging?.limit || 10
                }
            ]),
            this.UserModel.countDocuments(baseQuery)
        ]);

        return {
            users,
            total
        };
    }
}
