import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const payRatePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Pay Rate Write",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_WRITE,
        description: 'Grant pay rate write access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Read",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_READ,
        description: 'Grant pay rate read access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Update",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_UPDATE,
        description: 'Grant pay rate update access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Delete",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_DELETE,
        description: 'Grant pay rate delete access',
        isDelegated: true,
    },
];