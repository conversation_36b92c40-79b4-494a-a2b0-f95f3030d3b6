import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class MasterLead extends Document {
  @Prop({ required: true })
  firstName: string;

  @Prop()
  lastName?: string;

  @Prop({ required: true })
  mobile: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  source: string;
}

export const MasterLeadSchema = SchemaFactory.createForClass(MasterLead);
