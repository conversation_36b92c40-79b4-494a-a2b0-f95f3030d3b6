import {
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import { Request } from 'express';
import * as crypto from 'crypto';
import { Model } from 'mongoose';

// Extend Express Request interface to include organizationId
declare module 'express-serve-static-core' {
    interface Request {
        organizationId?: string;
    }
}
import { InjectModel } from '@nestjs/mongoose';
import {
    PaymentCredential,
    PaymentCredentialDocument,
} from '../schema/paymentCredential.schema';

@Injectable()
export class RazorpaySignatureGuard implements CanActivate {
    constructor(
        @InjectModel(PaymentCredential.name)
        private readonly paymentCredentialModel: Model<PaymentCredentialDocument>,
    ) { }

    private readonly IV_LENGTH = 16;

    private decrypt(encrypted: string): string {
        const [keyHex, ivHex, encryptedText] = encrypted.split(':');
        const key = Buffer.from(keyHex, 'hex');
        const iv = Buffer.from(ivHex, 'hex');
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
       const req: Request = context.switchToHttp().getRequest();
        const signature = req.headers['x-razorpay-signature'] as string;
        const body = req.body;
        const orgId = body?.payload?.payment?.entity?.notes?.organizationId;
        if (!orgId) {
            throw new HttpException(
                'Missing organization reference in webhook payload',
                HttpStatus.BAD_REQUEST,
            );
        }
        if (!signature) {
            throw new HttpException('Missing Razorpay signature', HttpStatus.BAD_REQUEST);
        }

        const credential = await this.paymentCredentialModel.findOne({
            organizationId: orgId,
            paymentGateway: 'razorpay',
            isDeleted: false,
        });

        if (!credential || !credential.webhookSecret) {
            throw new HttpException(
                'Webhook secret not configured',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }

        const decryptedWebhookSecret = this.decrypt(credential.webhookSecret);
        const expectedSignature = crypto
            .createHmac('sha256', decryptedWebhookSecret)
            .update(JSON.stringify(body))
            .digest('hex');

        if (signature !== expectedSignature) {
            throw new HttpException('Invalid Razorpay signature', HttpStatus.UNAUTHORIZED);
        }
 req.organizationId = orgId;
        return true;
    }
}
