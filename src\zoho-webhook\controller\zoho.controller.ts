import {
  Controller,
  Post,
  Body,
  Headers,
  Req,
  UploadedFile,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ZohowebhookService } from '../services/zoho-service/zoho-webhook.service';
import { ApiKeyGuard } from '../guards/api-key.guard';
@Controller('zoho-form/webhook')
export class ZohoWebhookController {
  constructor(private zohowebhookService: ZohowebhookService) { }

  @Post()
  @UseGuards(ApiKeyGuard)
  @UseInterceptors(FileInterceptor('client_signature'))
  async handleWebhook(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Headers() headers: any,
    @Req() req: any,
  ) {
    try {
      const { organizationId, facilityId } = req.apiKeyRecord;
      console.log('Org:', organizationId);
      console.log('Facility:', facilityId);
      console.log('📩 Webhook Received!');
      console.log('🔸 Headers:', headers);
      console.log('🔸 Body:', body);
      console.log('📎 File:', file);
      const result = await this.zohowebhookService.createWebhook(body, file,organizationId,facilityId);
      return { message: 'Webhook received successfully!' };

    } catch (error) {
      throw new Error('Error processing webhook');
    }
  }
}
