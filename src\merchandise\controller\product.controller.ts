import {
  BadRequestException,
  Body,
  Controller,
  Post,
  UseInterceptors,
  Get,
  Param,
  Patch,
  Delete,
  UploadedFile,
  ParseFilePipe,
  FileTypeValidator,
  HttpCode,
  Headers,
  Res,
  Query,
  Inject,
  MaxFileSizeValidator,
  StreamableFile,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { CreateProductDto } from '../dto/createProduct.dto';
import { ProductService } from '../services/product.service';
import { UpdateProductStatusDto } from '../dto/productupdateStatus.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { ExportProductsDto } from '../dto/productExport.dto';
import { ExportService } from 'src/utils/services/export.service';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { PolicyAbilityRoleProtected } from 'src/policy/decorators/policy.decorator';
import { BrandProductListingDto } from '../dto/brand-product-listing.dto';
import * as multer from 'multer';
import { Response } from 'express';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { parse as fastCsvParse } from '@fast-csv/parse';
import { Readable } from 'stream';
import { randomUUID } from 'crypto';
import { rkeys, BATCH_TTL_SECONDS } from '../bulk/redis-keys';

/**
 * Normalize CSV headers to exactly what ProductService expects.
 * - Keeps exact matches (case-insensitive) for known keys.
 * - Maps common variants (e.g., "master sku" -> "master_sku").
 * - Unknown headers pass through (treated as dynamic variant attributes).
 */
function normalizeHeader(h: string): string {
  const raw = String(h || '').trim();

  // Keep exact casing used by ProductService if it's a direct match
  const exact = [
    'name',
    'sku',
    'master_sku',
    'type',
    'Category',
    'Sub-Category',
    'Brand',
    'hsn',
    'gst',
    'variantTitle',
    'variantSku',
  ];
  const direct = exact.find((e) => e.toLowerCase() === raw.toLowerCase());
  if (direct) return direct;

  // Collapse spaces/dashes/underscores and map common variants
  const key = raw.replace(/[\s_\-]+/g, ' ').toLowerCase();
  const map: Record<string, string> = {
    'master sku': 'master_sku',
    'sub category': 'Sub-Category',
    'sub-category': 'Sub-Category',
    brand: 'Brand',
    'variant sku': 'variantSku',
    variant_sku: 'variantSku',
    variantsku: 'variantSku',
    'variant title': 'variantTitle',
    varianttitle: 'variantTitle',
  };
  return map[key] ?? raw;
}

@ApiTags('product')
@ApiBearerAuth()
@Controller('product')
export class ProductController {
  constructor(
    private productService: ProductService,
    private readonly exportService: ExportService,
    @InjectQueue('product-upload-queue-redis') private readonly uploadQueue: Queue,
    @Inject('REDIS') private readonly redis: any,
  ) { }

  @Post('/create')
  @ApiOperation({ summary: 'Create a new Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async createProduct(@Body() dto: CreateProductDto, @GetUser() user: any) {
    const product = await this.productService.createProduct(dto, user);
    return { message: 'Product Created', data: product };
  }

  @Post('/list')
  @ApiOperation({ summary: 'list of  Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async ProductListing(@Body() body: any, @GetUser() user: any) {
    const data = await this.productService.getProductsList(body, user);
    return { message: 'Product List', data };
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get the product Detail' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async getProductDetails(@Param('id') id: string) {
    const data = await this.productService.getProductDetails(id);
    return { message: 'Product Details', data };
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update product Detail' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async updateProduct(
    @Param('id') id: string,
    @Body() dto: CreateProductDto,
    @GetUser() user: any,
  ) {
    const data = await this.productService.updateProduct(id, dto, user);
    return { message: 'Product Updated', data };
  }

  @Patch('/status/:id')
  @ApiOperation({ summary: 'Update product Status ' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async updateProductStatus(
    @Param('id') id: string,
    @Body() dto: UpdateProductStatusDto,
  ): Promise<any> {
    return {
      message: 'Product status updated',
      data: await this.productService.updateProductStatus(id, dto),
    };
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Delete product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async deleteProductDetails(@Param('id') id: string) {
    const data = await this.productService.deleteProduct(id);
    return { message: 'Product Delete Successfully', data };
  }

  // ────────────────────────────────────────────────────────────────
  // BULK UPLOAD (CREATE) — CSV → Redis (no disk)
  // ────────────────────────────────────────────────────────────────
  @Post('/bulkUpload')
  @UseInterceptors(
    FileInterceptor('productFile', { storage: multer.memoryStorage() }),
  )
  @ApiOperation({
    summary: 'Queue bulk product upload (CSV via Redis Stream)',
  })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  @HttpCode(202)
  async bulkProductUpload(
    @Body() body: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              /^(text\/csv|application\/csv|application\/vnd\.ms-excel|text\/plain)$/i,
          }),
          new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024 }),
        ],
      }),
    )
    file: Express.Multer.File,
    @GetUser() user: any,
    @Res() res: Response,
  ) {
    if (!file?.buffer?.length) throw new BadRequestException('No CSV received');

    const isUpdate = String(body?.mode ?? '').toLowerCase() === 'update';
    const batch = await this.productService.createUploadBatch({
      user,
      filePath: 'redis',
      isUpdate,
    });

    await this.ingestCsvBufferToRedis(String(batch._id), file.buffer);

    await this.uploadQueue.add(
      'process-upload-redis',
      { batchId: String(batch._id) },
      {
        jobId: String(batch._id),
        attempts: 3,
        backoff: { type: 'exponential', delay: 5000 },
        removeOnComplete: true,
      },
    );

    return res.status(202).json({
      message: 'Upload queued',
      batchId: batch._id,
      statusUrl: `/product/bulkUpload/status/${batch._id}`,
    });
  }

  // ────────────────────────────────────────────────────────────────
  // BULK UPDATE — CSV → Redis (no disk)
  // ────────────────────────────────────────────────────────────────
  @Post('/bulkUpdate')
  @UseInterceptors(
    FileInterceptor('productFile', { storage: multer.memoryStorage() }),
  )
  @ApiOperation({
    summary: 'Queue bulk product UPDATE (CSV via Redis Stream)',
  })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  @HttpCode(202)
  async bulkUpdateProducts(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              /^(text\/csv|application\/csv|application\/vnd\.ms-excel|text\/plain)$/i,
          }),
          new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024 }),
        ],
      }),
    )
    file: Express.Multer.File,
    @GetUser() user: any,
    @Res() res: Response,
  ) {
    if (!file?.buffer?.length) throw new BadRequestException('No CSV received');

    const batch = await this.productService.createUploadBatch({
      user,
      filePath: 'redis',
      isUpdate: true,
    });

    await this.ingestCsvBufferToRedis(String(batch._id), file.buffer);

    await this.uploadQueue.add(
      'process-upload-redis',
      { batchId: String(batch._id) },
      {
        jobId: String(batch._id),
        attempts: 3,
        backoff: { type: 'exponential', delay: 5000 },
        removeOnComplete: true,
      },
    );

    return res.status(202).json({
      message: 'Update queued',
      batchId: batch._id,
      statusUrl: `/product/bulkUpload/status/${batch._id}`,
    });
  }

  @Post('/export')
  @HttpCode(200)
  @ApiOperation({ summary: 'Export Products' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async exportProductList(
    @Headers('X-Timezone') userTimezone: string,
    @GetUser() user: any,
    @Body() body: ExportProductsDto,
  ): Promise<any> {
    userTimezone =
      userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
    const products = await this.productService.getProductsForExport(
      user,
      body.productType,
      body.startDate,
      body.endDate,
    );
    if (!products.length) return { message: 'No products found to export', data: [] };

    const buffer = await this.exportService.generateExportFile(
      products,
      body.fileType,
    );

    if (body.responseType === 'stream') {
      const contentType = this.exportService.getMimeType(body.fileType);
      const extension = body.fileType;
      const fileName = `products-${Date.now()}.${extension}`;
      return new StreamableFile(new Uint8Array(buffer), {
        type: contentType,
        disposition: `attachment; filename=${fileName}`,
      });
    }
    return { message: 'Products fetched successfully', data: products };
  }

  @Post('/brand-product-list')
  @ApiOperation({ summary: 'List of Product' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async BrandProductListing(
    @Body() body: BrandProductListingDto,
    @GetUser() user: any,
  ) {
    const data = await this.productService.bulkProductListing(body, user);
    return { message: 'Product List', data };
  }

  @Get('/bulkUpload/status/:batchId')
  @ApiOperation({ summary: 'Get bulk upload status' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async getUploadStatus(@Param('batchId') batchId: string) {
    const doc = await this.productService.getUploadBatch(batchId);
    if (!doc) throw new BadRequestException('Invalid batchId');

    const k = rkeys(batchId);

    // Read Redis stats (may be empty if flushed)
    let stats: Record<string, any> = {};
    try {
      stats = await this.redis.hGetAll(k.stats);
    } catch {
      // ignore
    }

    // Normalize counters using Redis first, then Mongo fallback
    const total = Number(stats.total || doc.totalRows || 0);
    const processed = Number(stats.processed || doc.processedCount || 0);
    const failed = Number(stats.failed || doc.failedCount || 0);
    const done = processed + failed;

    // Best-effort pending count
    let pending = 0;
    try {
      const pend: any = await this.redis.xPending(k.stream, k.group);
      if (pend && typeof pend === 'object' && 'count' in pend) {
        pending = Number(pend.count || 0);
      } else if (Array.isArray(pend)) {
        // some clients return [count, minId, maxId, consumers[]]
        pending = Number(pend[0] || 0);
      }
    } catch {
      // stream may not exist yet / already flushed
    }

    // Compute progress %
    const pct = total > 0 ? Math.min(100, Math.round((processed / total) * 100)) : 0;

    // Safety net: finalize here if it's actually done
    const stillProcessing = doc.status === 'processing' || doc.status === 'queued';
    const allAccounted = total > 0 && done >= total;
    const nothingPending = pending === 0;

    if (stillProcessing && (allAccounted || total === 0) && nothingPending) {
      const finalStatus = failed > 0 ? 'completed_with_errors' : 'completed';
      await this.productService.finalizeBatch(batchId, {
        processedCount: processed,
        failedCount: failed,
        status: finalStatus,            // << ensure status is set
      } as any);

      const fresh = await this.productService.getUploadBatch(batchId);
      return {
        ...fresh,
        redisStats: stats,
        computed: { total, processed, failed, pending, progressPct: pct },
      };
    }

    // Otherwise just return the current view
    return {
      ...doc,
      redisStats: stats,
      computed: { total, processed, failed, pending, progressPct: pct },
    };
  }

  @Get('/bulkUpload/peek/:batchId')
  @ApiOperation({ summary: 'Peek first few rows in batch stream' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async peek(@Param('batchId') batchId: string, @Query('limit') limit = '5') {
    const k = rkeys(batchId);
    const n = Math.max(1, Math.min(100, Number(limit) || 5));
    const entries = await this.redis.xRange(k.stream, '-', '+', { COUNT: n });
    const stats = await this.redis.hGetAll(k.stats);
    const streamLen = await this.redis.xLen(k.stream);
    return { streamLen, stats, first: entries };
  }

@Get('/bulkUpload/errors/:batchId/preview')
@ApiOperation({ summary: 'Bulk upload errors (JSON preview from Redis)' })
@PolicyAbilityRoleProtected(
  ENUM_ROLE_TYPE.ORGANIZATION,
  ENUM_ROLE_TYPE.WEB_MASTER,
  ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
  ENUM_ROLE_TYPE.TRAINER,
)
@AuthJwtAccessProtected()
async getUploadErrorsPreview(
  @Param('batchId') batchId: string,
  @Query('offset') offset = '0',
  @Query('limit') limit = '200',
  @Query('clear') clear = 'false',
) {
  const doc = await this.productService.getUploadBatch(batchId);
  if (!doc) throw new BadRequestException('Invalid batchId');

  const k = rkeys(batchId);

  // raw list length (each item should be one JSON string)
  const rawListLen = await this.redis.lLen(k.errors);
  if (!rawListLen) {
    return {
      total: Number(doc.failedCount || 0),
      offset: 0,
      limit: Number(limit) || 200,
      data: [],
      meta: { rawRedisListLen: rawListLen, failedCount: Number(doc.failedCount || 0) },
    };
  }

  // Read ALL then slice after flattening — makes it resilient to legacy pushes
  const rawAny = await this.redis.lRange(k.errors, 0, -1);
  const rawItems: string[] = rawAny.map((v: any) =>
    Buffer.isBuffer(v) ? v.toString('utf8') : String(v)
  );

  // Flatten + parse safely
  const flat: any[] = [];
  for (const s of rawItems) {
    try {
      const parsed = JSON.parse(s);
      if (Array.isArray(parsed)) {
        for (const x of parsed) {
          if (!x) continue;
          flat.push(typeof x === 'object' ? x : { message: String(x) });
        }
      } else if (parsed && typeof parsed === 'object') {
        flat.push(parsed);
      } else {
        flat.push({ message: String(parsed) });
      }
    } catch {
      flat.push({ message: s });
    }
  }

  // Normalize fields so UI can render consistently
  const normalized = flat
    .filter(Boolean)
    .map((e: any) => ({
      lineNumber: e.lineNumber ?? e.line ?? '',
      message: e.message ?? e.msg ?? '',
      field: e.field ?? '',
      sku: e.sku ?? e.code ?? '',
      value: e.value ?? '',
      at: e.at ?? e.timestamp ?? '',
    }));

  const off = Math.max(0, Number(offset) || 0);
  const lim = Math.max(1, Math.min(1000, Number(limit) || 200));
  const data = normalized.slice(off, off + lim);

  if (String(clear).toLowerCase() === 'true') await this.redis.del(k.errors);

  return {
    total: normalized.length,
    offset: off,
    limit: lim,
    data,
    meta: {
      rawRedisListLen: rawListLen,
      failedCount: Number(doc.failedCount || 0),
    },
  };
}



  @Get('/bulkUpload/errors/:batchId/download')
  @ApiOperation({ summary: 'Download bulk upload errors as CSV (from Redis)' })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER,
  )
  @AuthJwtAccessProtected()
  async downloadUploadErrors(
    @Param('batchId') batchId: string,
    @Res() res: Response,
  ) {
    const k = rkeys(batchId);
    const items = await this.redis.lRange(k.errors, 0, -1);
    const rows = items.map((s) => {
      try {
        return JSON.parse(s);
      } catch {
        return { raw: s };
      }
    });

    const headers = ['lineNumber', 'message', 'field', 'sku', 'value', 'at'];
    const esc = (v: any) => {
      const s = v == null ? '' : String(v);
      return /[",\n]/.test(s) ? `"${s.replace(/"/g, '""')}"` : s;
    };

    let csv = headers.join(',') + '\n';
    for (const r of rows) {
      csv +=
        [
          esc(r.lineNumber),
          esc(r.message),
          esc(r.field),
          esc(r.sku),
          esc(r.value),
          esc(r.at),
        ].join(',') + '\n';
    }

    const fileName = `bulk-errors-${batchId}.csv`;
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.send(csv);
  }

  // ────────────────────────────────────────────────────────────────
  // Helper: parse CSV buffer → Redis Hashes + Stream
  // ────────────────────────────────────────────────────────────────
  private async ingestCsvBufferToRedis(batchId: string, buffer: Buffer) {
    const k = rkeys(batchId);

    await this.redis.hSet(k.stats, {
      total: 0,
      enqueued: 0,
      processed: 0,
      failed: 0,
    });
    await this.redis.expire(k.stats, BATCH_TTL_SECONDS);

    try {
      await this.redis.xGroupCreate(k.stream, k.group, '$', { MKSTREAM: true });
    } catch (e: any) {
      if (!String(e?.message || '').includes('BUSYGROUP')) throw e;
    }

    const readable = Readable.from(buffer);
    let total = 0,
      enqueued = 0;
    let pipe = this.redis.multi();

    await new Promise<void>((resolve, reject) => {
      let normalizedHeaders: string[] | undefined = undefined;
      readable
        .pipe(
          fastCsvParse({
            headers: (headers: string[]) => {
              normalizedHeaders = headers.map(normalizeHeader);
              return normalizedHeaders;
            },
            renameHeaders: true,
            trim: true,
            ignoreEmpty: true,
          }),
        )
        .on('data', (row: Record<string, any>) => {
          total++;
          const rowId = randomUUID();
          const rowKey = k.row(rowId);

          // ensure plain strings
          const normalized = Object.fromEntries(
            Object.entries(row).map(([kk, vv]) => [
              kk,
              vv == null ? '' : String(vv).trim(),
            ]),
          );

          pipe.hSet(rowKey, { status: 'PENDING', ...normalized });
          pipe.expire(rowKey, BATCH_TTL_SECONDS);
          pipe.xAdd(k.stream, '*', { rowKey, rowId });
          enqueued++;

          if (enqueued % 500 === 0) {
            pipe.exec();
            pipe = this.redis.multi();
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    await pipe.exec();
    await this.redis.hSet(k.stats, { total, enqueued });
  }
}
