import { faker } from '@faker-js/faker';
import { Optional } from '@nestjs/common';
import {
    ApiHideProperty,
    ApiProperty,
    IntersectionType,
    OmitType,
    PickType,
} from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, <PERSON><PERSON>ength, IsOptional } from 'class-validator';
import { RoleUpdateRequestDto } from 'src/role/dtos/request/role.update.request.dto';

export class RoleCreateRequestDto extends IntersectionType(
    OmitType(RoleUpdateRequestDto, ['description'] as const),
    PickType(RoleUpdateRequestDto, ['description'] as const)
) {
    @ApiHideProperty()
    @IsString()
    @IsOptional()
    _id?: string;

    @ApiProperty({
        description: 'Name of role',
        example: faker.person.jobTitle(),
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(3)
    @MaxLength(30)
    name: string;

    @ApiHideProperty()
    @ApiProperty({
        description: 'Representative for role type',
        example: true,
        required: true,
    })
    @Optional()
    default?: boolean = false;
}
