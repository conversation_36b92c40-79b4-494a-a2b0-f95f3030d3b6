import { Controller, Post, Body, Get } from '@nestjs/common';
import { LeadsService } from '../services/leads.service';
import { CreateLeadDto } from '../dtos/create-lead.dto';

@Controller('api/raw-leads')
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

@Post()
    create(@Body() dto: CreateLeadDto) {
    return this.leadsService.create(dto);
}


  @Get()
  findAll() {
    return this.leadsService.findAll();
  }
}
