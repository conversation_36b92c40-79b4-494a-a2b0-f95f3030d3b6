import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsNotEmpty } from "class-validator";

export class InstantCheckinDto {
    @ApiProperty({ type: [String], description: "Array of purchase IDs" })
    @IsArray({ message: "Purchase IDs must be an array" })
    @IsNotEmpty({ each: true })
    purchaseIds: string[];

    @ApiProperty({ type: String, description: "Organization ID" })
    @IsNotEmpty({ message: "Organization ID is required" })
    organizationId: string;

    @ApiProperty({ type: String, description: "Facility ID" })
    @IsNotEmpty({ message: "Facility ID is required" })
    facilityId: string;

}