import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { CreateFeatureDto, featureListDto } from "../dto/feature.dto";
import { Feature } from "../schema/feature.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { DiscountType } from "src/utils/enums/discount.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";


@Injectable()
export class FeatureService {
    constructor(
        @InjectModel(Feature.name) private readonly FeatureModel: Model<Feature>,
        @InjectModel(StaffProfileDetails.name) private readonly StaffProfileModel: Model<StaffProfileDetails>,
        private readonly transactionService: TransactionService, // Transaction service for managing transactions
    ) { }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            // case ENUM_ROLE_TYPE.USER:
            //     return user._id

            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId;
    }


    // Create a new feature
    async createFeature(createFeatureDto: CreateFeatureDto, user: any): Promise<any> {
        let organizationId = null;
        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break
            default:
                throw new BadRequestException("Access denied");
        }
        const session = await this.transactionService.startTransaction();
        try {
            let data = {
                name: createFeatureDto.name,
                isActive: createFeatureDto.isActive ? createFeatureDto.isActive : false,
                packageList: createFeatureDto.packageList,
                organizationId: organizationId
            };
            let createFeature = new this.FeatureModel(data)
            let featureDetail = await createFeature.save({ session });
            await this.transactionService.commitTransaction(session);
            return featureDetail
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async getFeaturesForUser(user: any, featureListDto: featureListDto): Promise<any> {
        const { search, isActive, limit, skip } = featureListDto;

        let organizationId = null;
        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break
            default:
                throw new BadRequestException("Access denied");
        }

        const query: any = {
            organizationId: organizationId
        };

        if (search) {
            query.name = { $regex: search, $options: 'i' }; // Case-insensitive search
        }

        if (typeof isActive === 'boolean') {
            query.isActive = isActive;
        }

        const features = await this.FeatureModel
            .find(query)
            .skip(skip)
            .limit(limit)
            .sort({ updatedAt: -1 })
            .exec();
        return features;
    }

    async getFeatureById(user: any, featureId: any): Promise<any> {

        let organizationId = null;
        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break
            default:
                throw new BadRequestException("Access denied");
        }

        try {
            const pipeline = [
                {
                    $match: {
                        _id: new Types.ObjectId(featureId), // Match the feature by ID
                        organizationId: new Types.ObjectId(organizationId), // Match the organizationId
                    },
                },
                {
                    $unwind: '$packageList', // Decompose the packageList array into individual documents
                },
                {
                    $lookup: {
                        from: 'pricings', // Reference the "Pricing" collection
                        localField: 'packageList.packageId', // Field in the current document
                        foreignField: '_id', // Field in the "Pricing" collection
                        as: 'packageDetails', // Alias for the joined data
                        pipeline: [
                            {
                                $match: {
                                    isActive: true,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: '$packageDetails', // Decompose the packageDetails array
                },
                {
                    $group: {
                        _id: '$_id', // Group by the feature ID
                        name: { $first: '$name' }, // Include the feature name
                        isActive: { $first: '$isActive' }, // Include the active status
                        organizationId: { $first: '$organizationId' }, // Include the organizationId
                        packageList: {
                            $push: {
                                packageId: '$packageList.packageId',
                                color: '$packageList.color',
                                sorting: '$packageList.sorting',
                                packageDetails: '$packageDetails', // Include detailed package info
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        packageList: {
                            $sortArray: {
                                input: '$packageList',
                                sortBy: { sorting: 1 }, // Sort the packageList array by the 'sorting' field in ascending order
                            },
                        },
                    },
                },
            ];



            const result = await this.FeatureModel.aggregate(pipeline).exec();
            if (result[0]?.packageList?.length > 0) {
                result[0].packageList = result[0].packageList.map((element) => {
                    const price = Number(element.packageDetails.price);
                    let discountedValue = 0.0;

                    if (element.packageDetails?.discount?.type === DiscountType.PERCENTAGE) {
                        discountedValue = Number(((element.packageDetails.discount.value / 100) * price).toFixed(2));
                    } else if (element.packageDetails?.discount?.type === DiscountType.FLAT) {
                        discountedValue = Number(element.packageDetails.discount.value.toFixed(2));
                    }

                    return {
                        ...element,
                        packageDetails: {
                            ...element.packageDetails,
                            discountedValue,
                        },
                    };
                });
            }


            return result[0]; // Return the first (and only) result
        } catch (error) {
            throw error;
        }
    }


    async updateFeature(updateFeatureDto: CreateFeatureDto, featureId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user);
        const session = await this.transactionService.startTransaction();
        try {
            // Fetch the current feature details
            const existingFeature = await this.FeatureModel.findOne({ _id: featureId, organizationId });


            // Log or process the existing status if needed

            // Prepare data for update
            const packageList = updateFeatureDto.packageList.map((pkg: any) => ({
                packageId: pkg.packageId,
                color: pkg.color,
                sorting: pkg.sorting,
            }));

            // Prepare data for update
            let dataToUpdate = {
                name: updateFeatureDto.name,
                isActive: existingFeature.isActive, // Retain existing status
                packageList: packageList,
                organizationId: organizationId,
            };
            // Find the feature by ID and update it
            const updatedFeature = await this.FeatureModel.findOneAndUpdate(
                { _id: featureId, organizationId },
                { $set: dataToUpdate },
                { new: true, session } // Return the updated document
            );

            if (!updatedFeature) {
                throw new Error('Failed to update feature.');
            }

            // Commit the transaction
            await this.transactionService.commitTransaction(session);
            return updatedFeature;
        } catch (error) {
            // Abort the transaction in case of an error
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            // End the session
            session.endSession();
        }
    }
    async deleteFeature(featureId: string): Promise<any> {
        try {
            const deletedFeature = await this.FeatureModel.findByIdAndDelete(featureId).exec();
            return {
                message: 'Feature deleted successfully',
                deletedFeature,
            };
        } catch (error) {
            throw error instanceof NotFoundException
                ? error
                : new BadRequestException(`Failed to delete feature: ${error.message}`);
        }
    }
    async updateFeatureStatus(isActive: any, featureId: string) {
        const session = await this.transactionService.startTransaction();
        try {
            const updateFeatureStatus = await this.FeatureModel.updateOne({
                _id: featureId
            },
                {
                    $set: { isActive: isActive.isActive || false },
                },
                {
                    session,
                },
            )
            if (!updateFeatureStatus) throw new BadRequestException("Feature not found");
            await this.transactionService.commitTransaction(session);
            return updateFeatureStatus;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
}

