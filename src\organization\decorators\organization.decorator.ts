import { createParamDecorator, ExecutionContext, Inject } from "@nestjs/common";
import { OrganizationDocument } from "../schemas/organization.schema";
import { Types } from "mongoose";
import { IRequestApp } from "src/common/request/interfaces/request.interface";

export const GetOrganizationId = createParamDecorator(
    async (data: keyof OrganizationDocument, ctx: ExecutionContext): Promise<Types.ObjectId> => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();
        if (!request.__organizationId ) {
            throw new Error("Unable to determine organization");
        }
        return new Types.ObjectId(request.__organizationId);
    },
);
