import { BadRequestException, Body, Controller, Delete, FileTypeValidator, Get, Headers, MaxFileSizeValidator, Param, ParseFilePipe, Patch, Post, Put, UploadedFile, UseGuards, UseInterceptors } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ClientsService } from "../services/clients.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ClientsDto } from "../dto/clients.dto";
import { GetDelegatedUser, GetUser } from "src/auth/decorators/get-user.decorator";
import { ClientsListDto } from "../dto/clients-list.dto";
import { UpdateMeasurementsDto } from "../dto/update-measurements.dto";
import { UpdatePoliciesDto } from "../dto/update-policies.dto";
import { Types } from "mongoose";
import { UpdateClientsMobileDto } from "../dto/update-clients-mobile.dto";
import { updateStaffStatusDto } from "src/staff/dto/update-staff-status.dto";
import { SharePassDto, ShareVoucherDto } from "src/users/dto/share-pass.dto";
import { ClientsListDtoV1 } from "../dto/clientsv1-list.dto";
import { SharePassListDto } from "src/users/dto/share-pass-list.dto";
import { CreateMinorRequestDto } from "../dto/miner.request.dto";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityDelegateProtected, PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { IUserDocument } from "../interfaces/user.interface";
import { FacilityProtected } from "src/facility/decorators/user.facility.decorator";
import { GetFacilities } from "src/facility/decorators/get.facility.decorators";
import { SharePassMultipleDto } from "../dto/share-pass-multiple.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { UploadMultimediaDto } from "../dto/upload-document.dto";
import { CreateDocumentDto } from "../dto/create-document.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { Response } from "src/common/response/decorators/response.decorator";

@ApiTags("clients")
@ApiBearerAuth()
@Controller("clients")
export class ClientsController {
    constructor(private clientService: ClientsService) { }

    @Post("/register")
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.CLIENTS_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Register a new client" })
    async registerClient(@Body() createClientDto: ClientsDto, @GetDelegatedUser() user: IUserDocument, @Headers("x-organization") organizationId: string): Promise<any> {
        let checkValidFacility = await this.clientService.validFacility(createClientDto.facilityId, user);
        if (!checkValidFacility) throw new BadRequestException("Invalid facility");
        let output = await this.clientService.registerClient(createClientDto, user?._id, organizationId);
        return {
            message: "Client registered successfully",
            data: output,
        };
    }

    @Post("/list")
    @FacilityProtected()
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client list" })
    async clientList(@GetUser() user, @Body() clientListDto: ClientsListDto, @GetFacilities("_id") facilityIds: string[]): Promise<any> {
        let locationId = [];
        if (clientListDto?.locationId?.length > 0) {
            locationId = clientListDto?.locationId.map((id) => new Types.ObjectId(id));
        }

        let output =
            user.role.type === ENUM_ROLE_TYPE.TRAINER && clientListDto.roleSpecific
                ? await this.clientService.clientListForTrainer(clientListDto, facilityIds, user)
                : await this.clientService.clientList(clientListDto, facilityIds);
        return {
            message: "Client list",
            data: output,
        };
    }

    @Post("/list/v1")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "This Api Will fetch Active Client list Based on Facility or Organization" })
    async clientListV1(@GetUser() user, @Body() clientListDto: ClientsListDtoV1): Promise<any> {
        let facilityList = await this.clientService.facilityListV1(clientListDto, user);
        let output = await this.clientService.clientListV1(clientListDto, facilityList);
        return {
            message: "Client list Fetched successfully",
            data: output,
        };
    }

    @Patch("/update/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client Update" })
    async clientUpdate(@Param("clientId") clientId: string, @GetUser() user, @Body() updateClientDto: ClientsDto): Promise<any> {
        let checkValidFacility = await this.clientService.validFacility(updateClientDto.facilityId, user);
        if (!checkValidFacility) throw new BadRequestException("Invalid facility");
        let output = await this.clientService.updateClient(updateClientDto, clientId);
        return {
            message: "Client updated successfully",
            data: output,
        };
    }

    @Patch("/sharePass")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Share Pass" })
    async sharePass(@Body() sharePassDto: SharePassDto): Promise<any> {
        let output = await this.clientService.sharePass(sharePassDto);
        return {
            message: "Share passed successfully",
            data: output,
        };
    }

    @Post("/sharePassList")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Shared pass list by client" })
    async sharedPassList(@Body() sharePassListDto: SharePassListDto): Promise<any> {
        let output = await this.clientService.sharedPassList(sharePassListDto);
        return {
            data: output,
        };
    }

    @Patch("/mobile/update")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client Update" })
    async clientMobileUpdate(@GetUser() user, @Body() updateClientDto: UpdateClientsMobileDto): Promise<any> {
        let output = await this.clientService.updateClientMobile(updateClientDto, user._id);
        return {
            message: "Client updated successfully",
            data: output,
        };
    }

    @Get("/mobile/:clientId")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client details for mobile app" })
    async clientDetailsForApp(@Param("clientId") clientId: string): Promise<any> {
        let output = await this.clientService.clientDetailsForApp(clientId);
        return {
            message: "Client details for mobile app",
            data: output,
        };
    }

    @Delete("/delete/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_DELETE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client delete" })
    async deleteClient(@Param("clientId") clientId: string): Promise<any> {
        let output = await this.clientService.deleteClient(clientId);
        return output;
    }

    @ApiOperation({ summary: "Update Client Assessment" })
    @Patch("/updateAssessment/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    async updateAssessment(@Param("clientId") clientId: string, @GetUser() user, @Body() updateAssessmentDto: UpdateMeasurementsDto): Promise<any> {
        let checkValidFacility = await this.clientService.validFacility(updateAssessmentDto.facilityId, user);
        if (!checkValidFacility) throw new BadRequestException("Invalid facility");
        let output = await this.clientService.updateAssessment(updateAssessmentDto, clientId);
        return {
            message: "Client Assessment updated",
            data: output,
        };
    }

    @Patch("/updatePolicy/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Client Policies" })
    async updatePolicies(@Param("clientId") clientId: string, @GetUser() user, @Body() updatePoliciesDto: UpdatePoliciesDto): Promise<any> {
        let checkValidFacility = await this.clientService.validFacility(updatePoliciesDto.facilityId, user);
        if (!checkValidFacility) throw new BadRequestException("Invalid facility");
        let output = await this.clientService.updatePolicy(updatePoliciesDto, clientId);
        return {
            message: "Client Policies updated",
            data: output,
        };
    }

    @Get("/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Client details" })
    async clientDetails(@Param("clientId") clientId: string): Promise<any> {
        let output = await this.clientService.clientDetails(clientId);
        return {
            message: "Client details",
            data: output,
        };
    }

    @Put("/updateStatus/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Client status" })
    async updateClientStatus(@Body() UpdateStaffStatusDto: updateStaffStatusDto, @GetUser() user, @Param("id") id: string): Promise<any> {
        return await this.clientService.adminUpdateClientStatus(UpdateStaffStatusDto, id);
    }

    @Patch("/set-default-address/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Client status" })
    async setDefaultAddress(@Param("clientId") clientId: string, @Body("id") addressId: string): Promise<any> {
        return await this.clientService.setDefaultAddress(clientId, addressId);
    }

    // get Clients minor
    @Get("/minor/:clientId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Clients minor" })
    async getMinor(@Param("clientId") clientId: string): Promise<any> {
        const data = await this.clientService.getMinor(clientId);
        return {
            message: "Minor clients fetched successfully",
            data: data,
        };
    }

    @Post("/minor/register")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.CLIENTS_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Register a new minor client" })
    async registerMinor(
        @GetDelegatedUser() user: IUserDocument,
        @Body() body: CreateMinorRequestDto, @Headers("x-organization") organizationId: string
    ): Promise<any> {
        let output = await this.clientService.registerMinor(user, body, organizationId);
        return {
            message: "Minor client registered successfully",
            data: output,
        };
    }

    @Post("/share-pass-multiple")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Share pass to multiple users" })
    async sharePassToMultiple(
        @Body() sharePassDto: SharePassMultipleDto,
        @GetUser() user: IUserDocument
    ): Promise<any> {
        return await this.clientService.sharePassToMultiple(user, sharePassDto);
    }

    @Post("/upload-file")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiConsumes("multipart/form-data")
    @UseInterceptors(FileInterceptor("file"))
    @ApiOperation({ summary: "Upload PDF/Image file" })
    @ApiBody({
        schema: {
            type: "object",
            properties: {
                image: {
                    type: "string",
                    format: "binary",
                },
            },
        },
    })
    async uploadFile(
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 5 }),
                ],
            }),
        )
        file: Express.Multer.File,
        // @Body() uploadFileDto: UploadMultimediaDto,
        @GetUser() user: IUserDocument
    ): Promise<any> {
        try {
            const data = await this.clientService.uploadFile(file, user);
            return {
                message: "File uploaded successfully",
                data,
            };
        } catch (error) {
            throw new BadRequestException(error.message || "File upload failed");
        }
    }

    @Post("/create-document")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create new document entry" })
    async create(
        @GetUser() user: IUserDocument,
        @Body() createDocumentDto: CreateDocumentDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
    ): Promise<any> {
        return this.clientService.createDocument(createDocumentDto, user, organizationId);
    }

    @Get("/list-documents/:userId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "List all documents for a user with role lookup" })
    async list(
        @Param("userId") userId: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
    ): Promise<any> {
        return this.clientService.listDocuments(userId, organizationId);
    }

    @Delete("/delete-document/:id")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete a document" })
    async delete(
        @Param("id") id: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
    ): Promise<any> {
        return this.clientService.deleteDocument(id, organizationId);
    }

}
