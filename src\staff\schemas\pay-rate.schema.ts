import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRates } from "src/utils/enums/pay-rate.type.enum";

export type PayRateDocument = HydratedDocument<PayRate>;

@Schema({ timestamps: true })
export class PayRate {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    createdBy: string;

    @Prop({ type: String, enum: ClassType, required: true })
    serviceType: ClassType;

    @Prop({ type: SchemaTypes.ObjectId, required: true })
    serviceCategory: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false })
    appointmentType?: string;

    @Prop({ type: String, enum: PayRates, required: true })
    payRate: PayRates;

    @Prop({ type: String, required: false })
    value?: string;
}

export const PayRateSchema = SchemaFactory.createForClass(PayRate);
