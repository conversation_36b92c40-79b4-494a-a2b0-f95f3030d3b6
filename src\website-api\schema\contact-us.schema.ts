import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document,Types } from 'mongoose';

@Schema({ timestamps: true })
export class ContactUs extends Document {
    @Prop({ type: String, required: true })
    name: string;
    @Prop({ type: String, required: true })
    email: string;
    @Prop({ type: String, default: true })
    message: string;
}

export const ContactUsSchema = SchemaFactory.createForClass(ContactUs);
