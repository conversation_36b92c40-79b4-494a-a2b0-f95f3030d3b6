import { faker } from '@faker-js/faker';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';

export class SessionCreateRequestDto {
    @ApiProperty({
        example: new Types.ObjectId(),
        required: true,
    })
    @IsNotEmpty()
    @Transform(({ value }) => new Types.ObjectId(value))
    user: Types.ObjectId;
}
