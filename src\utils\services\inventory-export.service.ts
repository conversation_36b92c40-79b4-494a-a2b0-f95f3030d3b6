// src/inventory/services/inventory-export.service.ts
import { Injectable, BadRequestException } from '@nestjs/common';
import * as XLSX from 'xlsx';

type Row = Record<string, any>;
type FileType = 'csv' | 'xlsx';

@Injectable()
export class InventoryExportService {
  /**
   * Map rows and return a Buffer for the requested file type.
   * @param rows - output of inventoryService.getInventoryForExport(user, dto)
   * @param fileType - 'csv' | 'xlsx'
   * @param timezone - optional IANA tz (unused for now; we keep ISO)
   */
  async generateExportFile(
    rows: Row[],
    fileType: FileType,
    timezone?: string
  ): Promise<Buffer> {
    const { headers, mapper } = this.getColumnConfig();

    // Normalize/format rows once here so CSV/XLSX stay consistent
    const mapped = rows.map((r) => mapper(r, timezone));

    switch (fileType) {
      case 'csv':
        return this.makeCsvBuffer(headers, mapped);
      case 'xlsx':
        return this.makeXlsxBuffer(headers, mapped);
      default:
        throw new BadRequestException(`Unsupported fileType: ${fileType}`);
    }
  }

  getMimeType(fileType: FileType): string {
    return fileType === 'xlsx'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8';
  }

  getDefaultExtension(fileType: FileType): string {
    return fileType === 'xlsx' ? 'xlsx' : 'csv';
  }

  /**
   * Single source of truth for column order + per-field formatting.
   * Add/remove columns here and all formats update together.
   */
  private getColumnConfig(): {
    headers: { key: string; label: string }[];
    mapper: (row: Row, tz?: string) => Row;
  } {
    const headers = [
      { key: 'storeName',        label: 'Store' },
      { key: 'productType',      label: 'Product Type' },
      { key: 'productName',      label: 'Product Name' },
      { key: 'productSku',       label: 'Product SKU' },
      { key: 'variantName',      label: 'Variant Name' },
      { key: 'variantSku',       label: 'Variant SKU' },
      { key: 'mrp',              label: 'MRP' },
      { key: 'salePrice',        label: 'Sale Price' },
      { key: 'discount',         label: 'Discount' },
      { key: 'discountPrice',    label: 'Discount Price' },
      { key: 'effectivePrice',   label: 'Effective Price' },
      { key: 'quantity',         label: 'Quantity' },
      { key: 'totalValue',       label: 'Total Value' },
      { key: 'expiryDate',       label: 'Expiry Date' },
      { key: 'promotion',        label: 'Promotion Id' },
      { key: 'organizationId',   label: 'Organization Id' },
      { key: 'createdAt',        label: 'Created At' },
      { key: 'updatedAt',        label: 'Updated At' },
      // raw IDs (keep at end)
      { key: 'storeId',          label: 'Store Id' },
      { key: 'productId',        label: 'Product Id' },
      { key: 'productVariantId', label: 'Product Variant Id' },
    ];

    const num = (v: any) => {
      const n = Number(v);
      return Number.isFinite(n) ? n : 0;
    };

    const fmtISO = (d?: any) => {
      if (!d) return '';
      const dt = new Date(d);
      return Number.isNaN(+dt) ? '' : dt.toISOString();
    };

    const mapper = (row: Row) => ({
      storeName: row.storeName ?? '',
      productType: row.productType ?? '',
      productName: row.productName ?? '',
      productSku: row.productSku ?? '',
      variantName: row.variantName ?? '',
      variantSku: row.variantSku ?? '',
      mrp: num(row.mrp),
      salePrice: num(row.salePrice),
      discount: num(row.discount),
      discountPrice: num(row.discountPrice),
      effectivePrice: num(
        row.effectivePrice ?? (row.discountPrice > 0 ? row.discountPrice : row.salePrice)
      ),
      quantity: num(row.quantity),
      totalValue: num(row.totalValue ?? (Number(row.quantity) * Number(row.salePrice || 0))),
      expiryDate: row.expiryDate ? String(row.expiryDate).slice(0, 10) : '',
      promotion: row.promotion ?? '',
      organizationId: row.organizationId ?? '',
      createdAt: fmtISO(row.createdAt),
      updatedAt: fmtISO(row.updatedAt),
      storeId: row.storeId ?? '',
      productId: row.productId ?? '',
      productVariantId: row.productVariantId ?? '',
    });

    return { headers, mapper };
  }

  // -------- CSV (buffer) --------
  private makeCsvBuffer(headers: { key: string; label: string }[], rows: Row[]): Buffer {
    const esc = (v: any) => {
      if (v === null || v === undefined) return '';
      const s = typeof v === 'string' ? v : String(v);
      // Escape if contains comma, quote or newline
      return /[",\n]/.test(s) ? `"${s.replace(/"/g, '""')}"` : s;
    };

    const head = headers.map((h) => esc(h.label)).join(',');
    const body = rows.map((r) => headers.map((h) => esc(r[h.key])).join(',')).join('\n');
    const csv = head + '\n' + body + (body ? '\n' : '');
    return Buffer.from(csv, 'utf-8');
  }

  // -------- XLSX (buffer) --------
  private makeXlsxBuffer(headers: { key: string; label: string }[], rows: Row[]): Buffer {
    const aoa = [
      headers.map((h) => h.label),
      ...rows.map((r) => headers.map((h) => r[h.key])),
    ];
    const ws = XLSX.utils.aoa_to_sheet(aoa);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Inventory');
    // return as Buffer
    return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' }) as Buffer;
  }
}
