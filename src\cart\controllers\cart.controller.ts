import { BadRequestException, Body, Controller, HttpCode, Post, Res, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CartService } from '../services/cart.service';
import { CartRequestDto } from '../dto/cart.request.dto';
import { CartResponseDto } from '../dto/response/cart.response.dto';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { GetOrganizationId } from 'src/organization/decorators/organization.decorator';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { Response } from 'src/common/response/decorators/response.decorator';
import { IResponse } from 'src/common/response/interfaces/response.interface';

@ApiTags('modules.cart')
@ApiBearerAuth()
@Controller('cart')
export class CartController {
  constructor(private readonly cartService: CartService) { }

  @Response("cart.success")
  @Post('validate')
  @HttpCode(200)
  @ApiOperation({ summary: 'Validate a cart before purchase' })
  @ApiResponse({
    status: 200,
    description: 'The cart has been validated successfully',
    type: CartResponseDto,
  })
  @AuthJwtAccessProtected()
  async validateCart(
    @Body() cartRequestDto: CartRequestDto,
    @GetOrganizationId() organizationId: IDatabaseObjectId,
    @Res({ passthrough: true }) response: any
  ): Promise<IResponse<CartResponseDto>> {
    // Ensure the organizationId in the request matches the authenticated organization
    cartRequestDto.organizationId = organizationId.toString();
    const data = await this.cartService.revalidateCart(cartRequestDto);
    // if (data.validationErrors && data.validationErrors.length > 0) {
    //   response.status(422);
    // }
    return {
      data: data,
    };
  }
}
