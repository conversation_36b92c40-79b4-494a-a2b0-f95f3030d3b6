import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { createReadStream, createWriteStream, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { parse } from 'fast-csv';
import { ProductService } from '../services/product.service';
import * as fsp from 'fs/promises';
@Processor('product-upload-queue')
export class ProductUploadProcessor extends WorkerHost {
  constructor(private readonly productService: ProductService) { super(); }

  @OnWorkerEvent('active')
  async onActive(job: Job) {
    await this.productService.markBatchProcessing(job.data.batchId);
  }

  async process(job: Job): Promise<void> {
    const { batchId, filePath, isUpdate, user } = job.data;

    const errDir = join(process.cwd(), 'uploads', 'errors');
    if (!existsSync(errDir)) mkdirSync(errDir, { recursive: true });
    const errorCsvPath = join(errDir, `${batchId}-errors.csv`);
    const errorWriter = createWriteStream(errorCsvPath, { flags: 'w' });
    errorWriter.write('lineNumber,message,value,field,sku\n');

    const stream = createReadStream(filePath, { highWaterMark: 64 * 1024 });
    const parser = stream.pipe(parse({ headers: true, ignoreEmpty: true, trim: true }));

    const batchSize = 500; // tune
    let buffer: any[] = [];
    let total = 0, processed = 0, success = 0, failed = 0;

    const flush = async () => {
      if (!buffer.length) return;
      const report = await this.productService.bulkUploadProductsBatch(buffer, user, isUpdate, batchId);
      success += report.success || 0;
      failed  += (report.errors?.length || 0);
      processed += buffer.length;

      for (const e of report.errors || []) {
        const safe = (v: any) => String(v ?? '').replace(/"/g, '""');
        errorWriter.write(`${e.lineNumber ?? ''},"${safe(e.message)}","${safe(e.value)}",${e.field ?? ''},${e.sku ?? ''}\n`);
      }

      buffer = [];
      await this.productService.updateBatchProgress(batchId, {
        processedRows: processed, successCount: success, failedCount: failed,
      });
    };

    for await (const row of parser) {
      total++;
      buffer.push(row);
      if (buffer.length >= batchSize) await flush();
    }
    await flush();

    errorWriter.end();
    await new Promise<void>(resolve => errorWriter.once('finish', () => resolve()));

// Build the finalize patch
const patch: any = {
  totalRows: total,
  successCount: success,
  failedCount: failed,
  status: 'completed',
};
if (failed > 0) {
  patch.errorFilePath = errorCsvPath;
} else {
  // no errors → delete the error CSV we created with just a header
  await fsp.unlink(errorCsvPath).catch(() => {});
}
   await this.productService.finalizeBatch(batchId, patch);
    await fsp.unlink(filePath).catch(() => {});
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job, err: Error) {
    await this.productService.failBatch(job.data.batchId, err?.message || 'Processing failed');
  }
}
