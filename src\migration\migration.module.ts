import { Module } from '@nestjs/common';
import { CommandModule } from 'nestjs-command';
import { CommonModule } from 'src/common/common.module';
import { RoleModule } from 'src/role/role.module';
import { MigrationRoleSeed } from './seeds/migration.role.seed';
import { MigrationPermissionSeed } from './seeds/migration.permissions.seed';
import { PolicyModule } from 'src/policy/policy.module';
import { MigrationPolicySeed } from './seeds/migration.policy.seed';
import { UsersModule } from 'src/users/users.module';
import { MigrationChangeRoleId } from './seeds/migration.change-role-id.seed';
import { UtilsModule } from 'src/utils/utils.module';
import { FacilityModule } from 'src/facility/facility.module';
import { MigrationItemType } from './migrate/migration.purchase';
import { MigrationVoucher } from './migrate/migration.voucher-remaining-amount';

@Module({
    imports: [
        CommonModule,
        CommandModule,
        UtilsModule,
        RoleModule,
        PolicyModule,
        UsersModule,
        FacilityModule,
    ],
    providers: [
        MigrationRoleSeed,
        MigrationPermissionSeed,
        MigrationPolicySeed,
        MigrationChangeRoleId,
        MigrationItemType,
        MigrationVoucher,
    ],
    exports: [],
})
export class MigrationModule { }
