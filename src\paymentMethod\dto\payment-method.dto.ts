import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsBoolean, IsOptional, IsNumber, IsMongoId, IsUrl } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class CreatePaymentMethodDto {
    @ApiProperty({
        description: "Name of the payment method",
        example: "Mastercard",
    })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({
        description: "URL of the payment method icon.",
        example: "https://example.com/icon.png",
    })
    @IsOptional()
    @IsString()
    @IsUrl()
    imageUrl: string;
}

export class AddPaymentMethodDto {
    @ApiProperty({
        description: "The ID of the facility where the payment method will be added.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Invalid facility ID format." })
    facilityId: string;

    @ApiProperty({
        description: "The ID of the payment method to add.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Invalid payment method ID format." })
    paymentMethodId: string;
}

export class PaymentMethodListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search keyword to filter payment methods.",
        example: "Visa",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search keyword must be a valid string." })
    search?: string;

    @ApiProperty({
        description: "Facility ID to exclude already added payment methods.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid facility ID format." })
    facilityId?: string;

    @ApiProperty({
        description: "Filter by active status of payment methods.",
        example: true,
        default: true,
    })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

export class AddedPaymentMethodListDto {
    @ApiProperty({
        description: "Facility ID to filter added payment methods.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Invalid facility ID format." })
    facilityId: string;

    @ApiProperty({
        description: "Filter by active status of added payment methods.",
        example: true,
        default: true,
    })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

export class UpdatePaymentMethodStatusDto {
    @ApiProperty({
        description: "The ID of the facility where the payment method will be added.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid facility ID format." })
    facilityId: string;

    @ApiProperty({
        description: "The ID of the payment method to add.",
        example: "63f1c19e8b4c5c0012f3b123",
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Invalid payment method ID format." })
    paymentMethodId: string;

    @ApiProperty({
        description: "Status to update.",
        example: true,
        default: true,
        required: true,
    })
    @IsNotEmpty()
    @IsBoolean()
    isActive?: boolean;
}
