import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const merchandisePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Merchandise Write",
        type: ENUM_PERMISSION_TYPE.MERCHANDISE_WRITE,
        description: 'Grant merchandise write access',
        isDelegated: true,
    },
    {
        name: "Merchandise Read",
        type: ENUM_PERMISSION_TYPE.MERCHANDISE_READ,
        description: 'Grant merchandise read access',
        isDelegated: true,
    },
    {
        name: "Merchandise Update",
        type: ENUM_PERMISSION_TYPE.MERCHANDISE_UPDATE,
        description: 'Grant merchandise update access',
        isDelegated: true,
    },
    {
        name: "Merchandise Delete",
        type: ENUM_PERMISSION_TYPE.MERCHANDISE_DELETE,
        description: 'Grant merchandise delete access',
        isDelegated: true,
    },
];