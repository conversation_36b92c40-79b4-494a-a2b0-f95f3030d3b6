import { ApiProperty } from "@nestjs/swagger";
import {IsEnum, IsNotEmpty, IsArray } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetAllServiceCategoryByStaffDto extends PaginationDTO {
    @ApiProperty({
        description: "Array of serviceType for Availability",
        enum: ClassType,
        isArray: true,
        example: [ClassType.PERSONAL_APPOINTMENT, ClassType.CLASSES],
        required: true,
    })
    @IsArray({ message: "serviceType must be an array of valid enum values" })
    @IsEnum(ClassType, { each: true, message: "Each serviceType must be a valid enum value" })
    @IsNotEmpty({ message: "serviceType is required" })
    serviceType: ClassType[];
}