import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { CartRequestDto } from 'src/cart/dto/cart.request.dto';
import { PaymentMethod } from 'src/utils/enums/paymentMethod.enum';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { Types } from 'mongoose';

export class PaymentDetailsDto {
  @ApiProperty({
    description: 'Payment method used for the purchase',
    example: PaymentMethod.CREDIT_CARD,
    enum: PaymentMethod,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({
    description: 'Payment method ID',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  paymentMethodId?: string;

  @ApiProperty({
    description: 'Transaction ID for the payment',
    example: 'txn_123456',
    required: false,
  })
  @IsString()
  @IsOptional()
  transactionId?: string;

  @ApiProperty({
    description: 'Amount paid',
    example: 100.00,
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: 'Payment date',
    example: new Date(),
    required: true,
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  paymentDate: Date;

  @ApiProperty({
    description: 'Payment status',
    example: PaymentStatus.COMPLETED,
    enum: PaymentStatus,
    required: true,
  })
  @IsString()
  @IsEnum(PaymentStatus)
  @IsNotEmpty()
  paymentStatus: string;

  @ApiProperty({
    description: 'Payment gateway used',
    example: 'Stripe',
    required: false,
  })
  @IsString()
  @IsOptional()
  paymentGateway?: string;

  @ApiProperty({
    description: 'Additional description for the payment',
    example: 'Payment for January subscription',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Denominations as key-value pairs (for cash payments)',
    example: { 100: 5, 500: 2 },
    required: false,
  })
  @IsObject()
  @IsOptional()
  denominations?: Record<number, number>;
}

export class PurchaseRequestDto {
  @ApiProperty({
    description: 'Cart details',
    type: CartRequestDto,
  })
  @ValidateNested()
  @Type(() => CartRequestDto)
  @IsNotEmpty()
  cart: CartRequestDto;

  @ApiProperty({
    description: 'Payment details',
    type: [PaymentDetailsDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PaymentDetailsDto)
  @IsNotEmpty()
  paymentDetails: PaymentDetailsDto[];

  @ApiProperty({
    description: 'Whether the payment is split',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isSplittedPayment?: boolean;

   @ApiProperty({
    description: 'Whether the payment is in cash opr not',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isCashPayment?: boolean;

  @ApiProperty({
    description: 'Total amount paid',
    example: 100.00,
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  amountPaid: number;

  @ApiProperty({
    description: 'Platform used for purchase',
    example: 'Web',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  platform: string;

  @ApiProperty({
    description: 'Billing address ID',
    example: '60d21b4667d0d8992e610c85',
    required: true,
  })
  @IsMongoId()
  @IsNotEmpty()
  billingAddressId: string;

  @ApiProperty({
    description: "Payment by user",
    example: '60d21b4667d0d8992e610c85',
    required: false
  })
  @IsNotEmpty()
  paymentBy?: string

  @ApiProperty({
    description: "Payment by user",
    example: '60d21b4667d0d8992e610c85',
    required: false
  })
  @IsOptional()
  date?: string

  @ApiProperty({
    description: "Array of returned purchase IDs for exchange/refund",
    example: [
      "677f78dbc6a56b77d8ae0743",
      "67823eddab0a9e0f1c733eae"
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  returnItems?: string[];
}