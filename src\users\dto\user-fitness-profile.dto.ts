import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsEnum, IsOptional, ValidateNested } from "class-validator";
import { Type } from "class-transformer";
import { Gender } from "src/utils/enums/gender.enum";
import { ActivityLevel } from "src/utils/enums/activity-level.enum";
import { Goal } from "src/utils/enums/goal.enum";
import { MeasurementDto } from "./user-measurement-profile.dto";

export class UserFitnessProfileDto {
    @ApiProperty({ description: "Gender of the user", enum: Gender, example: Gender.MALE })
    @IsOptional()
    @IsEnum(Gender, { message: "Gender must be a valid Gender enum value" })
    gender?: Gender;

    @ApiProperty({ description: "Age of the user", example: "25" })
    @IsOptional()
    @IsString({ message: "Age must be a string" })
    age?: string;

    @ApiProperty({ description: "Weight of the user in kg", example: "70" })
    @IsOptional()
    @IsString({ message: "Weight must be a string" })
    weight?: string;

    @ApiProperty({ description: "Height of the user in cm", example: "175" })
    @IsOptional()
    @IsString({ message: "Height must be a string" })
    height?: string;

    @ApiProperty({ description: "Fitness goal of the user", enum: Goal, example: Goal.BODY_BUILDING })
    @IsOptional()
    @IsEnum(Goal, { message: "Goal must be a valid Goal enum value" })
    goal?: Goal;

    @ApiProperty({ description: "Activity level of the user", enum: ActivityLevel, example: ActivityLevel.BEGINNER })
    @IsOptional()
    @IsEnum(ActivityLevel, { message: "Activity Level must be a valid ActivityLevel enum value" })
    activityLevel?: ActivityLevel;

    @ApiProperty({ description: "Body measurements of the user", type: MeasurementDto })
    @IsOptional()
    @ValidateNested({ each: true, message: "Measurement must be a valid MeasurementDto object" })
    @Type(() => MeasurementDto)
    measurement?: MeasurementDto;
}