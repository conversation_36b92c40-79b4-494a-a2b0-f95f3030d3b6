<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>Invoice Payment Pending</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f9f9f9;
      margin: 0;
      padding: 20px;
    }

    .invoice-container {
      max-width: 800px;
      margin: auto;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 24px;
    }

    .section {
      margin-bottom: 24px;
    }

    h2 {
      margin: 0 0 12px 0;
      font-size: 20px;
      border-bottom: 1px solid #ccc;
      padding-bottom: 6px;
    }

    .details {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
    }

    .details div {
      width: 48%;
    }

    .details p {
      margin: 4px 0;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 12px;
      font-size: 14px;
    }

    th,
    td {
      padding: 10px;
      border: 1px solid #ddd;
    }

    th {
      background: #8143D1;
      color: #fff;
    }

    .totals {
      margin-top: 20px;
      text-align: right;
      font-size: 15px;
    }

    .totals p {
      margin: 6px 0;
    }

    .grand-total {
      font-weight: bold;
      font-size: 17px;
    }

    .qr-section {
      text-align: center;
      margin-top: 32px;
    }

    .qr-section p {
      text-align: center;
    }

    .qr-section a {
      text-align: center;
    }

    .qr-section img {
      margin: 12px 0;
    }

    .pay-now {
      display: inline-block;
      padding: 10px 20px;
      background-color: #8143D1;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      margin-top: 10px;
    }

    .amount-in-words {
      margin-top: 16px;
      font-weight: 600;
    }
  </style>
</head>

<body>
  <div class="invoice-container">

    <div class="section">
      <h2>Order Summary</h2>
      <p><strong>Invoice No:</strong> {{invoiceNumber}}</p>
      <p><strong>Order Date:</strong> {{invoiceDate}}</p>
    </div>


    <div class="section">
      <table>
        <thead>
          <tr>
            <th>Sr. No.</th>
            <th>Item</th>
            <th>QTY</th>
            <th>Validity</th>
            <th>Unit Price (₹)</th>
            <th>Discount (₹)</th>
            <th>HSN/SAC</th>
            <th>GST%</th>
          </tr>
        </thead>
        <tbody>
          {{#each purchaseItems}}
          <tr>
            <td>{{inc @index}}</td>
            <td>{{this.packageName}}</td>
            <td>{{this.quantity}}</td>
            <td>{{this.expireIn}} {{this.durationUnit}}</td>
            <td>{{this.unitPrice}}</td>
            <td>{{this.discountExcludeCart}}</td>
            <td>{{this.hsnOrSacCode}}</td>
            <td>{{this.tax}}</td>
          </tr>
          {{/each}}

          {{#each customPackageItems}}
          <tr>
            <td>{{inc @index}}</td>
            <td>{{this.packageName}}</td>
            <td>{{this.quantity}}</td>
            <td>-</td>
            <td>{{this.unitPrice}}</td>
            <td>{{this.discountExcludeCart}}</td>
            <td>{{this.hsnOrSacCode}}</td>
            <td>{{this.tax}}</td>
          </tr>
          {{/each}}

          {{#each productItem}}
          <tr>
            <td>{{inc @index}}</td>
            <td>{{this.productName}}</td>
            <td>{{this.quantity}}</td>
            <td>-</td>
            <td>{{this.salePrice}}</td>
            <td>{{this.discountExcludeCart}}</td>
            <td>{{showZero this.hsnOrSacCode}}</td>
            <td>{{showZero this.tax}}</td>
          </tr>
          {{/each}}
        </tbody>
      </table>
    </div>

    <div class="totals">
      <!-- Discount and Cart Discount first -->
      <p><strong>Discount:</strong> ₹ {{discount}}</p>

      {{#if cartDiscountAmount}}
      {{#if (eq cartDiscountType 'Flat')}}
      <p><strong>Cart Discount (₹{{cartDiscount}}):</strong> ₹ {{cartDiscountAmount}}</p>
      {{else}}
      <p><strong>Cart Discount ({{cartDiscount}}%):</strong> ₹ {{cartDiscountAmount}}</p>
      {{/if}}
      {{/if}}

      <p><strong>Sub Total:</strong> ₹ {{subTotal}}</p>

      <p><strong>GST:</strong> ₹ {{totalGstValue}}</p>
      <p><strong>Total:</strong> ₹ {{totalAmountAfterGst}}</p>
      <p><strong>Round Off:</strong> ₹ - {{roundOff}}</p>
      <p class="grand-total"><strong>Grand Total:</strong> ₹ {{grandTotal}}</p>
    </div>

    <p class="amount-in-words">Amount In Words: {{amountInWords}}</p>

    {{#if paymentLinkUrl}}
    <div class="qr-section">
      <p><strong>Scan QR Code to Pay:</strong></p>
      <img src="cid:qrCodeCid" alt="QR Code" width="150" height="150" />
      <p>OR</p>
      <div>
        <a class="pay-now" href="{{paymentLinkUrl}}" style="color: white" target="_blank">Payment Link</a>
      </div>
    </div>
    {{/if}}

  </div>
</body>

</html>