import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsMongoId, IsNotEmpty, IsOptional, Length, Max, Min } from "class-validator";
import { IAddress } from "../interfaces/address.interface";

export class AddressDto implements IAddress {
    @ApiProperty({
        description: "The address line 1 of the Client.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Address line 1 must be between 2 and 1000 characters" })
    addressLine1: string;

    @ApiProperty({
        description: "The address line 2 of the Client.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Address line 1 must be between 2 and 1000 characters" })
    addressLine2: string;

    @ApiProperty({
        description: "Please specify postal code of the city. || Must be a number",
        example: 122016,
        minLength: 6,
        maxLength: 6,
        required: false,
    })
    @IsOptional()
    @Min(100000, { message: "Postal code must be a number" })
    @Max(999999, { message: "Invalid postal code" })
    postalCode: number;

    @ApiProperty({
        description: "The ID of the city where client lives.",
        example: "659e8032293375c3166a99a0",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of city" })
    @IsNotEmpty({ message: "City is required" })
    city: string;

    @ApiProperty({
        description: "The ID of the state where client lives.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of state" })
    @IsNotEmpty({ message: "State is required" })
    state: string;

    @ApiProperty({
        description: "Country Name",
        example: "India",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Invalid country" })
    country: string;

    @ApiProperty({
        description: "Mark as default address",
        example: false,
        required: false,
    })
    @IsOptional()
    @Type(()=>Boolean)
    isDefault?: boolean = false;
}
