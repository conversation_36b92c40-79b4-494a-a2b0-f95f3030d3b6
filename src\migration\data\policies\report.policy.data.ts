
import { ENUM_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const reportPolicies: IDefaultPolicies[] = [
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.REPORTS_REPORT_EXPORT],
        description: 'Grant full access to reports',
        isActive: true,
        module: ENUM_POLICY_MODULE.REPORTS,
        type: ENUM_POLICY_TYPE.REPORTS_REPORT_EXPORT,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];