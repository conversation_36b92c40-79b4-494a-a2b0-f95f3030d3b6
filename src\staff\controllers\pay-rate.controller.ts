import { BadRequestException, Body, Controller, Delete, Get, Param, Patch, Post, Put, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from "@nestjs/swagger";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { PayRateService } from "src/staff/services/pay-rate.service";
import { CreatePayRateDto } from "src/staff/dto/create-pay-rate.dto";
import { UpdatePayRateDto } from "src/staff/dto/update-pay-rate.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { GetPayRatesDto } from "src/staff/dto/get-pay-rate.dto";
import { GetAllServiceCategoryByStaffDto } from "src/staff/dto/get-service-category-staff.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { IUserDocument } from "src/users/interfaces/user.interface";

@ApiTags("PayRate")
@ApiBearerAuth()
@Controller("payRate")
export class PayRateController {
    constructor(private payRateService: PayRateService) { }

    @Post("/create")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Create a new Pay Rate" })
    async createPayRate(@Body() createPayRateDto: CreatePayRateDto, @GetUser() user: any) {
        return await this.payRateService.create(createPayRateDto, user);
    }

    // @ApiOperation({ summary: "Create a new Pay Rate by Trainer" })
    // @Post("/own-create")
    // @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.TRAINER)
    // @AuthJwtAccessProtected()
    // async createOwnPayRate(
    //     @Body() createPayRateDto: CreatePayRateDto,
    //     @GetUser() user: IUserDocument
    // ) {
    //     if (!user._id.equals(createPayRateDto.userId)) {
    //         throw new BadRequestException("You are not authorized to create pay rate for this instructor.\nMake sure you are creating pay rate for yourself.");
    //     }

    //     return await this.payRateService.create(createPayRateDto, user);
    // }

    @Get(":id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Get Pay Rate by ID" })
    async getPayRate(@Param("id") id: string) {
        return await this.payRateService.findById(id);
    }

    @Get("allClassType/:staffId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Get All Class Type By StaffID" })
    async getAllClassType(@Param("staffId") staffId: string) {
        return await this.payRateService.getAllClassType(staffId);
    }

    @Post("allServiceCategory/:staffId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Get all Service Category By StaffID" })
    async getAllServiceCategoryByStaff(@Param("staffId") staffId: string, @Body() getAllServiceCategoryByStaff: GetAllServiceCategoryByStaffDto) {
        return await this.payRateService.getAllServiceCategoryByStaff(getAllServiceCategoryByStaff, staffId);
    }

    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Get all Pay Rates" })
    async getAllPayRates(@Body() getPayRatesDto: GetPayRatesDto, @GetUser() user: any) {
        return await this.payRateService.findAll(getPayRatesDto);
    }

    @Put(":id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Update Pay Rate by ID" })
    async updatePayRate(@Param("id") id: string, @Body() updatePayRateDto: UpdatePayRateDto, @GetUser() user: any) {
        return await this.payRateService.update(id, updatePayRateDto, user);
    }

    @Delete(":id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Delete Pay Rate by ID" })
    async deletePayRate(@Param("id") id: string) {
        return await this.payRateService.delete(id);
    }
}
