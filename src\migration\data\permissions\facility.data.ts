import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const facilityPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Facility Write",
        type: ENUM_PERMISSION_TYPE.FACILITY_WRITE,
        description: 'Grant facility write access',
        isDelegated: true,
    },
    {
        name: "Facility Read",
        type: ENUM_PERMISSION_TYPE.FACILITY_READ,
        description: 'Grant facility read access',
        isDelegated: true,
    },
    {
        name: "Facility Update",
        type: ENUM_PERMISSION_TYPE.FACILITY_UPDATE,
        description: 'Grant facility update access',
        isDelegated: true,
    },
    {
        name: "Facility Delete",
        type: ENUM_PERMISSION_TYPE.FACILITY_DELETE,
        description: 'Grant facility delete access',
        isDelegated: true,
    },
];

