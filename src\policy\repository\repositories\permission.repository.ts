import { Injectable } from '@nestjs/common';
import { Model, PopulateOptions } from 'mongoose';
import { DatabaseRepositoryBase } from 'src/common/database/bases/database.repository';
import { InjectDatabaseModel } from 'src/common/database/decorators/database.decorator';
import {
    PermissionDocument,
    PermissionEntity,
} from 'src/policy/repository/entities/permission.entity';

@Injectable()
export class PermissionRepository extends DatabaseRepositoryBase<
    PermissionEntity,
    PermissionDocument
> {

    constructor(
        @InjectDatabaseModel(PermissionEntity.name)
        private readonly permissionModel: Model<PermissionEntity>
    ) {
        super(permissionModel);
    }
}
