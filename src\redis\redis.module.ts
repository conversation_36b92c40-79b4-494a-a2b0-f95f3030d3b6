import { Global, Module } from '@nestjs/common';
import { createClient } from 'redis';

@Global()
@Module({
  providers: [
    {
      provide: 'REDIS',
      useFactory: async () => {
        const client = createClient({
          socket: {
            host: process.env.REDIS_HOST || '127.0.0.1',
            port: Number(process.env.REDIS_PORT || '6379'),
            // tls: true, // uncomment if your Redis needs TLS
          },
          username: (process.env.REDIS_USERNAME || '').trim() || undefined,
          password: process.env.REDIS_PASSWORD || undefined,
          database: Number(process.env.REDIS_DATABASE || '0'),
        });
        client.on('error', (e) => console.error('Redis error:', e));
        await client.connect();
        return client;
      },
    },
  ],
  exports: ['REDIS'],
})
export class RedisModule {}
