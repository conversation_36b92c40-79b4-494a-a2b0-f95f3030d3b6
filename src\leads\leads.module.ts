import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LeadsController } from './controllers/leads.controller';
import { LeadsService } from './services/leads.service';
import { Lead, LeadSchema } from './schemas/lead.schema';
import { MasterLeadModule } from '../masterLead/master-lead.module';

@Module({
  imports: [MongooseModule.forFeature([{ name: Lead.name, schema: LeadSchema }]), MasterLeadModule],
  controllers: [LeadsController],
  providers: [LeadsService],
})
export class LeadsModule {}
