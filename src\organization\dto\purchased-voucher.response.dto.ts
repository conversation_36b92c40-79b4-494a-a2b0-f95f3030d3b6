import { ApiProperty, PickType } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { TransformObjectId, TransformNestedObjectId } from "src/common/transformers/objectid.transformer";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { VoucherResponseDto } from "./detail-voucher.response.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PaymentStatus as ENUM_PAYMENT_STATUS } from "src/utils/enums/payment.enum";

export class VoucherPurchasedResponseDto extends PickType(VoucherResponseDto, [
    'name', 'price', 'expiredInDays', 'isActive', 'description',
] as const)
{

    @ApiProperty({
        description: "The invoice id of the purchased voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    invoiceId: string;

    @ApiProperty({
        description: "The id of the purchased voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    _id: IDatabaseObjectId;

    @ApiProperty({
        description: "The facility id of the purchased voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    facilityId: IDatabaseObjectId;

    @ApiProperty({
        description: "The id of the voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    voucherId: IDatabaseObjectId;

    @ApiProperty({
        description: "The user id of the purchased voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    userId: string;

    @ApiProperty({
        description: "The voucher code of the purchased voucher.",
        example: "GRB4G2U4HFB49NR",
        required: true,
    })
    @Expose()
    voucherCode: string;

    @ApiProperty({
        description: "The amount consumed of the purchased voucher.",
        example: 0,
        required: true,
    })
    @Expose()
    amountConsumed: number;

    @ApiProperty({
        description: "The remaining amount of the purchased voucher.",
        example: 100,
        required: true,
    })
    @Expose()
    remainingAmount: number;

    @ApiProperty({
        description: "The purchase date of the purchased voucher.",
        example: "2023-11-20T06:50:15.000Z",
        required: true,
    })
    @Expose()
    purchaseDate: Date;

    @ApiProperty({
        description: "The payment status of the purchased voucher.",
        example: ENUM_PAYMENT_STATUS.COMPLETED,
        required: true,
    })
    @Expose()
    paymentStatus: string;

    @ApiProperty({
        description: "The start date of the purchased voucher.",
        example: "2023-11-20T06:50:15.000Z",
        required: true,
    })
    @Expose()
    startDate: Date;

    @ApiProperty({
        description: "The end date of the purchased voucher.",
        example: "2023-11-20T06:50:15.000Z",
        required: true,
    })
    @Expose()
    endDate: Date;

    @ApiProperty({
        description: "The QR code URL of the purchased voucher.",
        example: "https://example.com/qr-code",
        required: false,
    })
    @Expose()
    qrCodeUrl?: string;
}
