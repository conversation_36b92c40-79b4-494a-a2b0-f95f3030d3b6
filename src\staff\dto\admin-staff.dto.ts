// import { ApiProperty } from "@nestjs/swagger";
// import { Transform } from "class-transformer";
// import { ArrayMinSize, IsArray, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Matches, MaxLength } from "class-validator";
// import { Gender } from "src/utils/enums/gender.enum";

// export class AdminStaffDto {
//     @ApiProperty({
//         description: "Gym id must be a mongoId",
//         example: "66b351hj27e63f77ac10a4e7",
//     })
//     @IsMongoId({ message: "Required gym details" })
//     gymId: string;

//     @ApiProperty({
//         description: "Name of the staff",
//         example: "Sam",
//     })
//     @IsString({ message: "Name is required" })
//     @IsNotEmpty({ message: "Name cannot be empty" })
//     fullName: string;

//     @ApiProperty({
//         description: "Gender type",
//         enum: Gender,
//         example: Gender.MALE,
//     })
//     @IsEnum(Gender, { message: "Invalid gender" })
//     gender: Gender;

//     @ApiProperty({
//         description: "Email is required",
//         example: "<EMAIL>",
//     })
//     @Transform((param) => param.value.toLowerCase())
//     @IsEmail({}, { message: "Invalid email" })
//     @MaxLength(255)
//     email: string;

//     @ApiProperty({
//         description: "Mobile number is required | String",
//         example: "7896549823",
//     })
//     @Matches(/^[+\-\s]*\d+[+\-\s]*$/, {
//         message: "Invalid mobile number",
//     })
//     mobile: String;

//     @ApiProperty({
//         description: "Alternate Mobile number is required | String",
//         example: "7896549823",
//     })
//     @Matches(/^[+\-\s]*\d+[+\-\s]*$/, {
//         message: "Invalid alternate mobile number",
//     })
//     alternateMobile: String;

//     @ApiProperty({
//         description: "Experience must be string",
//         example: "4 years",
//     })
//     @IsString({ message: "Invalid experience details" })
//     @IsNotEmpty({ message: "Experience details required" })
//     experience: string;

//     @ApiProperty({
//         description: "Certification must be string || optional field",
//         example: "4 years",
//     })
//     @IsOptional()
//     @IsString({ message: "Invalid certification details" })
//     @IsNotEmpty({ message: "Certification details required" })
//     certification: string;

//     @ApiProperty({
//         description: "Certification must be array of string",
//         example: "[Cardio, gymnastic]",
//     })
//     @IsArray({ message: "Required expertise in array format" })
//     @ArrayMinSize(1, { message: "Please select at least one of your expertise" })
//     expertise: Array<string>;
// }
