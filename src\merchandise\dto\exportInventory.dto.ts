import { IsEnum, IsOptional, IsString, IsMongoId } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ExportInventoryDto {
  @ApiProperty({
    description: 'The type of export file to generate',
    enum: ['csv', 'xlsx'],
    example: 'csv',
  })
  @IsEnum(['csv', 'xlsx', 'pdf'])
  fileType: 'csv' | 'xlsx' ;

  @ApiProperty({
    description: 'Whether to return file as a stream download or JSON data',
    enum: ['stream', 'json'],
    required: false,
    default: 'stream',
    example: 'stream',
  })
  @IsOptional()
  @IsEnum(['stream', 'json'])
  responseType?: 'stream' | 'json';

  @ApiProperty({
    description: 'Optional facility ID to filter inventory by specific facility',
    required: false,
    example: '66c01f6f9a764b7a5d5d1a2f',
  })
  @IsOptional()
  @IsMongoId()
  facilityId?: string;
}
