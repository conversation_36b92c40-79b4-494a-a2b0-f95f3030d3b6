{"version": "0.2", "language": "en", "words": ["abcde<PERSON><PERSON><PERSON><PERSON><PERSON>", "alphanum", "apikey", "apikeys", "appt", "blabla", "bullmq", "casl", "cgst", "CHECKEDIN", "checkin", "DDTHH", "DIQU", "dtos", "emiting", "ength", "golevelup", "headerapikey", "hopwellness", "hsnor", "igst", "ijkl", "Inclusiveof", "Kvnwr", "lastcounter", "luxon", "maxlength", "mediumpassword", "MILIS", "nodemon", "ntegral", "officedocument", "ONCEADAY", "openxmlformats", "Payrate", "presign", "presigned", "presigner", "presignUrl", "pricings", "promotionitems", "qrcode", "QUDRX", "randexp", "requestid", "safestring", "schedulings", "sgst", "spreadsheetml", "ssword", "Streamable", "strongpassword", "subsettings", "superadmin", "testuser", "userhistories", "userpasswords", "uuidv", "VJMV", "weakpassword", "Zgoh", "sharepass"], "ignorePaths": ["node_modules/**", "endpoints/**", "*coverage/**", ".husky/**", ".github/**", "dist/**", "logs/**", "**/**/*.json"]}