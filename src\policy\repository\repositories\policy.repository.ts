import { Injectable } from '@nestjs/common';
import { Model, PopulateOptions } from 'mongoose';
import { DatabaseRepositoryBase } from 'src/common/database/bases/database.repository';
import { InjectDatabaseModel } from 'src/common/database/decorators/database.decorator';
import { PermissionEntity } from '../entities/permission.entity';
import { PolicyEntity, PolicyDocument } from '../entities/policy.entity';

@Injectable()
export class PolicyRepository extends DatabaseRepositoryBase<
    PolicyEntity,
    PolicyDocument
> {
    static populateOptions: PopulateOptions[] = [
        {
            path: 'permissions',
            localField: 'permissions',
            foreignField: '_id',
            model: PermissionEntity.name,
            // justOne: true,
            match: {
                isActive: true,
            },
        },
    ];

    readonly _joinActive: PopulateOptions[] = PolicyRepository.populateOptions;

    constructor(
        @InjectDatabaseModel(PolicyEntity.name)
        private readonly policyModel: Model<PolicyEntity>
    ) {
        super(policyModel, PolicyRepository.populateOptions);
    }
}
