import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ENUM_SESSION_STATUS_CODE_ERROR } from '../enums/session.status-code.enum';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { OrganizationSettingService } from 'src/organizationSettings/services/organization-settings.service';
import { PERMISSION_META_DELEGATE_OPTIONAL_KEY } from 'src/policy/constants/policy.constant';

@Injectable()
export class SessionActiveGuard implements CanActivate {

    constructor(
        private readonly organizationSettingService: OrganizationSettingService,
        private readonly reflector: Reflector,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const req = context.switchToHttp().getRequest<IRequestApp>();
        const { user, __isDelegateSessionActive } = context.switchToHttp().getRequest<IRequestApp>();
        const { type } = user;

        const isDelegateOptional = !!this.reflector.get<string[]>(PERMISSION_META_DELEGATE_OPTIONAL_KEY, context.getHandler());

        if (type == ENUM_ROLE_TYPE.ORGANIZATION || type == ENUM_ROLE_TYPE.SUPER_ADMIN) {
            req.__sessionId = "";
            req.__isSessionActive = true;
            req.__delegateSessionId = "";
            req.__isDelegateSessionActive = true;
            req.__delegateUser = req.__user;

            return true;
        }
        const isOrganizationSettingEnabled = await this.organizationSettingService.isEnabled(user.organization as any, "settings_pin");

        if (!isOrganizationSettingEnabled) {
            req.__sessionId = "";
            req.__isSessionActive = true;
            req.__delegateSessionId = "";
            req.__isDelegateSessionActive = true;
            req.__delegateUser = req.__user;

            return true;
        }

        if (!__isDelegateSessionActive) {
            if (isDelegateOptional) {
                // Fallback to user if delegate session is optional
                req.__sessionId = "";
                req.__isSessionActive = true;
                req.__delegateSessionId = "";
                req.__isDelegateSessionActive = true;
                req.__delegateUser = req.__user;
            } else {
                // Delegate session is required but not active
                throw new ForbiddenException({
                    statusCode: ENUM_SESSION_STATUS_CODE_ERROR.SESSION_EXPIRED,
                    message: 'session.error.notFound',
                });
            }
        }

        return true
    }
}
