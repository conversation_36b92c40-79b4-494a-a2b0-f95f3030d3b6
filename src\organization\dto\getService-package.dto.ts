import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsInt, <PERSON>, <PERSON>, IsO<PERSON>al, IsMongoId } from "class-validator";

export class GetServicesByPackageDTO {

    @ApiProperty({
        description: "Pricing ID of the staff. Must be a valid MongoDB ObjectId.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Pricing ID is required." })
    @IsMongoId({ message: "Pricing ID must be a valid MongoDB ObjectId." })
    pricingId: string;


    @ApiProperty({
        description: "Search keyword (optional). Must be a string.",
        example: "Knox",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a valid string." })
    search?: string;

}