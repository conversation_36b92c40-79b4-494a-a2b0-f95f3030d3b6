import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes, Types } from 'mongoose';

@Schema({ timestamps: true })
export class DocumentLocker extends Document {
    @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true })
    userId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true })
    facilityId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true })
    organizationId: Types.ObjectId;

    @Prop({ required: true })
    fileUrl: string;

    @Prop({ required: true })
    documentName: string;

    @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true })
    uploadedBy: Types.ObjectId;

    @Prop({ default: Date.now })
    uploadedAt: Date;
}

export const DocumentLockerSchema = SchemaFactory.createForClass(DocumentLocker);