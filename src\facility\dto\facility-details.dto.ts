import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsOptional } from "class-validator";

export class FacilityDetailsDto {
    @ApiProperty({
        description: "The id of the facility.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "Facility details is Invalid" })
    facilityId?: string;

    @ApiProperty({
        description: "Organizatioon Id",
        example: "66cecb432351713ae4447a6b ",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid organization id" })
    organizationId?: string;
}
