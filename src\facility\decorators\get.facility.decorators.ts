
import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { Facility, FacilityDocument } from "../schemas/facility.schema";

/**
 * Decorator to get facilities from the request
 * @param propertyPath Optional property path to extract from each facility
 * Example: @GetFacilities() - returns full facility objects
 * Example: @GetFacilities('_id') - returns array of facility IDs
 * Example: @GetFacilities('address.city') - returns array of city values
 */
export const GetFacilities = createParamDecorator(
    (data: keyof FacilityDocument | undefined, ctx: ExecutionContext): any => {
        const request = ctx.switchToHttp().getRequest();
        const facilities: Facility[] = request.__facilities || [];

        // If no property path is specified, return the full facilities array
        if (!data) {
            return facilities;
        }

        // If property path is specified, extract that property from each facility
        return facilities.map(facility => {
            // Handle nested properties (e.g., 'address.city')
            if (data.includes('.')) {
                const parts = data.split('.');
                let value = facility;

                for (const part of parts) {
                    if (value && typeof value === 'object' && part in value) {
                        value = value[part];
                    } else {
                        return undefined; // Property path doesn't exist
                    }
                }

                return value;
            }

            // Handle direct properties
            return facility[data];
        });
    },
);

