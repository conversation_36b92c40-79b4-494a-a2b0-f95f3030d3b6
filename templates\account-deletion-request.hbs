<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Account Deletion Request</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f9f9f9;
        }

        .container {
            width: 90%;
            max-width: 700px;
            margin: 30px auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #B095FC;
            color: #fff;
            padding: 20px;
            text-align: center;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 26px;
            font-weight: bold;
        }

        .message p {
            font-size: 16px;
            color: #333;
            line-height: 1.5;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        ul li {
            font-size: 16px;
            margin: 8px 0;
            color: #555;
        }

        ul li strong {
            color: #333;
        }

        .footer {
            font-size: 14px;
            color: #aaaaaa;
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        @media (max-width: 600px) {
            .container {
                width: 95%;
                padding: 20px;
            }

            .header {
                font-size: 22px;
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Account Deletion Request</h1>
        </div>

        <div class="message">
            <p>Hello <strong>{{organizationName}}</strong>,</p>
            <p>A request has been made to delete an account associated with your organization.</p>

            <ul>
                {{#if (eq roleType "trainer")}}
                <li><strong>Trainer Name:</strong> {{userName}}</li>
                {{else}}
                <li><strong>User Name:</strong> {{userName}}</li>
                {{/if}}

                <li><strong>Role:</strong> {{role}}</li>
                <li><strong>Facility:</strong> {{facilityName}}</li>
                <li><strong>Organization:</strong> {{organizationName}}</li>
            </ul>

            <p>Please review and process the request accordingly. If this action was not intended, please contact support.</p>

            <p style="margin-top: 30px;">Regards,<br />The Admin Team</p>
        </div>

        <div class="footer">
            © 2024 {{organizationName}}. All rights reserved.
        </div>
    </div>
</body>

</html>
