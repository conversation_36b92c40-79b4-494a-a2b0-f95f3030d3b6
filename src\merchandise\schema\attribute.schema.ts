import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { Attributes } from 'src/utils/enums/attribute.enum';
export type AttributeValueDocument = HydratedDocument<AttributeValue>;

export enum AttributeType {
  Mandatory = "mandatory",
  Optional = "optional",
  Variant = "variant",
}

export enum AttributeValueStatus {
  Active = "active",
  Inactive = "inactive",
}
@Schema({ timestamps: true })
export class AttributeValue {

  @Prop({ required: true, type: String, index: true })
  attribute: string;
  @Prop({ required: true, type: String })
  value: string;
  @Prop({ type: String, unique: true })
  slug: string;
  @Prop({ type: String })
  hexCode: string;
  @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
  createdBy: string;
  @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
  organizationId: string;
}
export type AttributeDocument = AttributeValue & Document;
export const AttributeValueSchema = SchemaFactory.createForClass(AttributeValue);
