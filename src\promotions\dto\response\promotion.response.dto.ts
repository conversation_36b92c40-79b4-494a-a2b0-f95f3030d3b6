import { ApiProperty } from '@nestjs/swagger';
import { DatabaseDto } from 'src/common/database/dtos/database.dto';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { ENUM_PROMOTION_TARGET } from 'src/promotions/enums/promotion-target.enum';

export class TimeWindowResponseDto {
    @ApiProperty({
        description: 'Start time for promotion availability (HH:mm)',
        example: '08:00',
    })
    startTime: string;

    @ApiProperty({
        description: 'End time for promotion availability (HH:mm)',
        example: '17:00',
    })
    endTime: string;
}

export class PromotionResponseDto extends DatabaseDto {
    @ApiProperty({
        description: 'Name of the promotion',
        example: 'Student Discount',
    })
    name: string;

    @ApiProperty({
        description: 'Description of the promotion',
        example: 'Special discount for students',
        required: false,
    })
    description?: string;

    @ApiProperty({
        description: 'Type of promotion',
        enum: DiscountType,
        example: DiscountType.PERCENTAGE,
    })
    type: DiscountType;

    @ApiProperty({
        description: 'Value of the promotion (percentage or flat amount)',
        example: 15,
    })
    value: number;

    @ApiProperty({
        description: 'Target audience for the promotion',
        enum: ENUM_PROMOTION_TARGET,
        example: ENUM_PROMOTION_TARGET.ALL_USERS,
    })
    target: ENUM_PROMOTION_TARGET;

    @ApiProperty({
        description: 'Status of the promotion',
        example: true,
    })
    status: boolean;

    @ApiProperty({
        description: 'Start date of the promotion',
        example: '2023-01-01T00:00:00.000Z',
        required: false,
    })
    startDate?: Date;

    @ApiProperty({
        description: 'End date of the promotion',
        example: '2023-12-31T23:59:59.999Z',
        required: false,
    })
    endDate?: Date;

    @ApiProperty({
        description: 'Time window for the promotion',
        required: false,
    })
    timeWindow?: TimeWindowResponseDto;

    @ApiProperty({
        description: 'Facility IDs where this promotion is applicable (empty means all facilities)',
        example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
        required: false,
        type: [String],
    })
    facilityIds?: string[];
}
