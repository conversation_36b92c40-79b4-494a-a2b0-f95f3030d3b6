import { Global, Module } from '@nestjs/common';
import { CacheOptions, CacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CachingService } from './services/caching.service';
import KeyvRedis from '@keyv/redis';

@Global()
@Module({
    imports: [
        CacheModule.registerAsync({
            isGlobal: true,
            imports: [ConfigModule],
            useFactory: async (
                configService: ConfigService
            ): Promise<CacheOptions> => ({
                max: configService.get<number>('redis.cached.max'),
                ttl: configService.get<number>('redis.cached.ttl'),
                stores: [
                    new KeyvRedis({
                        socket: {
                            host: configService.get<string>(
                                'redis.cached.host'
                            ),
                            port: configService.get<number>(
                                'redis.cached.port'
                            ),
                            tls: configService.get<boolean>('redis.cached.tls'),
                        },
                        username: configService.get<string>(
                            'redis.cached.username'
                        ),
                        password: configService.get<string>(
                            'redis.cached.password'
                        ),
                        database : configService.get<number>(
                            'redis.cached.database'
                        ),
                    }),
                ],
            }),
            inject: [ConfigService],
        }),
    ],
    providers: [CachingService],
    exports: [CachingService],
})
export class CachingModule { }