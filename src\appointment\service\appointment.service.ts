import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, startSession, Types } from "mongoose";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { User } from "src/users/schemas/user.schema";
import { Appointment } from "../schema/appointment.schema";
import { CreateAppointmentDto } from "../dto/book-appointment.dto";
import { Organizations } from "src/organization/schemas/organization.schema";
import { DateRange } from "src/utils/enums/date-range-enum";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { AvailabilityType } from "src/facility/enums/availability-type.enum";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import { SessionType } from "src/utils/enums/session-type.enum";

@Injectable()
export class AppointmentService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(StaffProfileDetails.name) private staffModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailableModel: Model<FacilityAvailability>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(StaffAvailability.name) private staffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Appointment.name) private appointmentModel: Model<Appointment>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
    ) {}

    private validateNumberOfSessions(noOfSessions: number, timeSlots: any[]): void {
        if (noOfSessions < timeSlots.length) {
            throw new BadRequestException("No. of appointments cannot exceed the remaining number of sessions");
        }
    }

    private validateTimeSlots(timeSlots: any[]): void {
        for (const slot of timeSlots) {
            if (!this.isFromLessThanTo(slot)) {
                throw new BadRequestException(`Invalid time range: 'from' time (${slot.from}) must be less than 'to' time (${slot.to})`);
            }
        }

        const isOverlapping = (slot1, slot2) => {
            const date1 = new Date(slot1.date).toISOString().split("T")[0];
            const date2 = new Date(slot2.date).toISOString().split("T")[0];

            return slot1.from < slot2.to && slot2.from < slot1.to && date1 === date2;
        };

        for (let i = 0; i < timeSlots.length; i++) {
            for (let j = i + 1; j < timeSlots.length; j++) {
                if (isOverlapping(timeSlots[i], timeSlots[j])) {
                    throw new BadRequestException("Time slots overlap!");
                }
            }
        }
    }

    private async bookAppointments(body: CreateAppointmentDto, user: any): Promise<any> {
        const { facilityId, trainerId, clientId, packageId, serviceId, sendConfirmation, duration, timeSlots, organizationId, appointmentId, notes } = body;

        const appointments = timeSlots.map((slot) => ({
            bookedBy: user._id,
            facilityId: facilityId,
            trainerId: trainerId,
            organizationId: organizationId,
            appointmentId: appointmentId,
            clientId: clientId,
            packageId: packageId,
            serviceId: serviceId,
            sendConfirmation: sendConfirmation,
            dateRange: DateRange.SINGLE,
            duration: duration,
            date: slot.date,
            from: slot.from,
            to: slot.to,
            notes: notes,
        }));

        const insertedAppointments = await this.appointmentModel.insertMany(appointments);

        const populatedAppointments = await this.appointmentModel
            .find({ _id: { $in: insertedAppointments.map((appointment) => appointment._id) } })
            .populate({ path: "facilityId", select: "facilityName" })
            .populate({ path: "trainerId", select: "firstName lastName" })
            .populate({ path: "organizationId", select: "name" })
            .populate({ path: "clientId", select: "firstName lastName" });

        const formattedAppointments = populatedAppointments.map((appointment) => {
            const appointmentObject = appointment.toObject();

            return {
                ...appointmentObject,
                facilityName: appointmentObject.facilityId?.["facilityName"] || null,
                trainerName: appointmentObject.trainerId ? `${appointmentObject.trainerId?.["firstName"].trim()} ${appointmentObject.trainerId?.["lastName"].trim()}` : null,
                organizationName: appointmentObject.organizationId?.["name"] || null,
                clientName: appointmentObject.clientId ? `${appointmentObject.clientId?.["firstName"].trim()} ${appointmentObject.clientId?.["lastName"].trim()}` : null,
                paymentStatus: "paid",
                planType: "PT",
                tier: "Expert",
                capacity: "50",
                class_type: "Animal Flow",
                facilityId: undefined,
                trainerId: undefined,
                organizationId: undefined,
                clientId: undefined,
            };
        });

        return formattedAppointments;
    }
    isFromLessThanTo = (slot) => {
        const fromDate = new Date(`1970-01-01T${slot.from}:00`);
        const toDate = new Date(`1970-01-01T${slot.to}:00`);
        return fromDate < toDate;
    };

    async bookingByOrganization(body: CreateAppointmentDto, user: any): Promise<any> {
        try {
            const { facilityId } = body;

            const checkFacility = await this.FacilityModel.findById(new Types.ObjectId(facilityId));

            if (checkFacility?.organizationId.toString() !== user?._id.toString()) {
                throw new BadRequestException("Access Denied as the Facility is not a part of this Organization");
            }

            this.validateNumberOfSessions(body.noOfSessions, body.timeSlots);
            this.validateTimeSlots(body.timeSlots);

            return await this.bookAppointments(body, user);
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }
    async bookingByWebMaster(body: CreateAppointmentDto, user: any): Promise<any> {
        try {
            this.validateNumberOfSessions(body.noOfSessions, body.timeSlots);
            this.validateTimeSlots(body.timeSlots);

            return await this.bookAppointments(body, user);
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }
    async bookingByUser(body: CreateAppointmentDto, user: any): Promise<any> {
        try {
            this.validateNumberOfSessions(body.noOfSessions, body.timeSlots);
            this.validateTimeSlots(body.timeSlots);

            return await this.bookAppointments(body, user);
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }
    async bookingByTrainer(body: CreateAppointmentDto, user: any): Promise<any> {
        try {
            this.validateNumberOfSessions(body.noOfSessions, body.timeSlots);
            this.validateTimeSlots(body.timeSlots);

            return await this.bookAppointments(body, user);
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async updateBookingByOrganization(id: string, body: CreateAppointmentDto, user: any): Promise<any> {
        const appointment = await this.appointmentModel.findById(id);
        if (!appointment) {
            throw new BadRequestException("Appointment not found");
        }

        (body["bookedBy"] = user._id), (body["date"] = body.timeSlots[0].date);
        body["from"] = body.timeSlots[0].from;
        body["to"] = body.timeSlots[0].to;

        Object.assign(appointment, body);
        return await appointment.save();
    }

    async updateBookingByWebMaster(id: string, body: CreateAppointmentDto, user: any): Promise<any> {
        const appointment = await this.appointmentModel.findById(id);
        if (!appointment) {
            throw new BadRequestException("Appointment not found");
        }
        (body["bookedBy"] = user._id), (body["date"] = body.timeSlots[0].date);
        body["from"] = body.timeSlots[0].from;
        body["to"] = body.timeSlots[0].to;
        Object.assign(appointment, body);
        return await appointment.save();
    }

    async updateBookingByUser(id: string, body: CreateAppointmentDto, user: any): Promise<any> {
        const appointment = await this.appointmentModel.findById(id);
        if (!appointment) {
            throw new BadRequestException("Appointment not found");
        }
        (body["bookedBy"] = user._id), (body["date"] = body.timeSlots[0].date);
        body["from"] = body.timeSlots[0].from;
        body["to"] = body.timeSlots[0].to;
        Object.assign(appointment, body);
        return await appointment.save();
    }

    async updateBookingByTrainer(id: string, body: CreateAppointmentDto, user: any): Promise<any> {
        const appointment = await this.appointmentModel.findById(id);
        if (!appointment) {
            throw new BadRequestException("Appointment not found");
        }
        (body["bookedBy"] = user._id), (body["date"] = body.timeSlots[0].date);
        body["from"] = body.timeSlots[0].from;
        body["to"] = body.timeSlots[0].to;
        body["updateAt"] = Date.now();
        Object.assign(appointment, body);
        return await appointment.save();
    }

    async getBookingByOrganization(id: string, user: any): Promise<any> {
        try {
            let pipeline = [
                {
                    $match: {
                        _id: Types.ObjectId.createFromHexString(id),
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "clientId",
                        foreignField: "_id",
                        as: "clientDetails",
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceId",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerDetails",
                    },
                },
                {
                    $project: {
                        title: {
                            $arrayElemAt: ["$serviceDetails.name", 0],
                        },
                        trainerName: {
                            $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                        },
                        clientName: {
                            $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                        },
                        clientEmail: {
                            $arrayElemAt: ["$clientDetails.email", 0],
                        },
                        clientMobile: {
                            $arrayElemAt: ["$clientDetails.mobile", 0],
                        },
                        serviceId: 1,
                        trainerId: 1,
                        clientId: 1,
                        appointmentId: 1,
                        packageId: 1,
                        date: 1,
                        from: 1,
                        to: 1,
                        facilityId: 1,
                        notes: 1,
                        organizationId: 1,
                    },
                },
            ];
            const result = await this.appointmentModel.aggregate(pipeline);
            if (result.length === 0) {
                throw new BadRequestException("Appointment not found");
            }
            let sessionCount = await this.PricingModel.aggregate([
                {
                    $match: {
                        _id: new Types.ObjectId(result[0].packageId),
                    },
                },
                {
                    $unwind: "$services", // Unwind the services array
                },
                {
                    $match: {
                        "services.appointmentType": new Types.ObjectId(result[0].appointmentId), // Match appointmentType
                        // Uncomment below if additional filtering is needed
                        // "services.type": ClassType.PERSONAL_APPOINTMENT,
                        // "services.serviceCategory": result[0].serviceId
                    },
                },
                {
                    $project: {
                        sessionCount: "$services.sessionCount", // Extract sessionCount
                        sessionType: "$services.sessionType",
                    },
                },
            ]);
            if (sessionCount.length > 0) {
                if (sessionCount[0]?.sessionType == SessionType.SINGLE) {
                    result[0].noOfSessions = 1;
                }
                if (sessionCount[0]?.sessionType == SessionType.MULTIPLE) {
                    result[0].noOfSessions = sessionCount[0]?.sessionCount ? sessionCount[0]?.sessionCount : 0;
                }
            }

            return result[0];
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getBookingByWebMaster(id: string, user: any): Promise<any> {
        try {
            // const appointment = await this.appointmentModel.findOne({
            //     _id: new Types.ObjectId(id),
            //     facilityId: new Types.ObjectId(user._id),
            // });
            let pipeline = [
                {
                    $match: {
                        _id: Types.ObjectId.createFromHexString(id),
                        facilityId: Types.ObjectId.createFromHexString(user._id),
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "clientId",
                        foreignField: "_id",
                        as: "clientDetails",
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceId",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerDetails",
                    },
                },
                {
                    $project: {
                        title: {
                            $arrayElemAt: ["$serviceDetails.name", 0],
                        },
                        trainerName: {
                            $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                        },
                        clientName: {
                            $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                        },
                        clientEmail: {
                            $arrayElemAt: ["$clientDetails.email", 0],
                        },
                        clientMobile: {
                            $arrayElemAt: ["$clientDetails.mobile", 0],
                        },
                        serviceId: 1,
                        trainerId: 1,
                        clientId: 1,
                        appointmentId: 1,
                        packageId: 1,
                        date: 1,
                        from: 1,
                        to: 1,
                        facilityId: 1,
                        notes: 1,
                    },
                },
            ];

            const result = await this.appointmentModel.aggregate(pipeline);

            if (result.length === 0) {
                throw new BadRequestException("Appointment not found");
            }

            return result[0];
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getBookingByUser(id: string, user: any): Promise<any> {
        try {
            // const appointment = await this.appointmentModel.findOne({
            //     _id: new Types.ObjectId(id),
            //     clientId: new Types.ObjectId(user._id),
            // });

            // if (!appointment) {
            //     throw new BadRequestException("Appointment not found");
            // }

            // return appointment;
            let pipeline = [
                {
                    $match: {
                        _id: Types.ObjectId.createFromHexString(id),
                        clientId: Types.ObjectId.createFromHexString(user._id),
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "clientId",
                        foreignField: "_id",
                        as: "clientDetails",
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceId",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerDetails",
                    },
                },
                {
                    $project: {
                        title: {
                            $arrayElemAt: ["$serviceDetails.name", 0],
                        },
                        trainerName: {
                            $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                        },
                        clientName: {
                            $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                        },
                        clientEmail: {
                            $arrayElemAt: ["$clientDetails.email", 0],
                        },
                        clientMobile: {
                            $arrayElemAt: ["$clientDetails.mobile", 0],
                        },
                        serviceId: 1,
                        trainerId: 1,
                        clientId: 1,
                        appointmentId: 1,
                        packageId: 1,
                        date: 1,
                        from: 1,
                        to: 1,
                        facilityId: 1,
                        notes: 1,
                    },
                },
            ];

            const result = await this.appointmentModel.aggregate(pipeline);

            if (result.length === 0) {
                throw new BadRequestException("Appointment not found");
            }

            return result[0];
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getBookingByTrainer(id: string, user: any): Promise<any> {
        try {
            // const appointment = await this.appointmentModel.findOne({
            //     _id: new Types.ObjectId(id),
            //     trainerId: new Types.ObjectId(user._id),
            // });

            // if (!appointment) {
            //     throw new BadRequestException("Appointment not found");
            // }

            // return appointment;
            let pipeline = [
                {
                    $match: {
                        _id: Types.ObjectId.createFromHexString(id),
                        trainerId: Types.ObjectId.createFromHexString(user._id),
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "clientId",
                        foreignField: "_id",
                        as: "clientDetails",
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceId",
                        foreignField: "_id",
                        as: "serviceDetails",
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerDetails",
                    },
                },
                {
                    $project: {
                        title: {
                            $arrayElemAt: ["$serviceDetails.name", 0],
                        },
                        trainerName: {
                            $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                        },
                        clientName: {
                            $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                        },
                        clientEmail: {
                            $arrayElemAt: ["$clientDetails.email", 0],
                        },
                        clientMobile: {
                            $arrayElemAt: ["$clientDetails.mobile", 0],
                        },
                        serviceId: 1,
                        trainerId: 1,
                        clientId: 1,
                        appointmentId: 1,
                        packageId: 1,
                        date: 1,
                        from: 1,
                        to: 1,
                        facilityId: 1,
                        notes: 1,
                    },
                },
            ];

            const result = await this.appointmentModel.aggregate(pipeline);

            if (result.length === 0) {
                throw new BadRequestException("Appointment not found");
            }

            return result[0];
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getAllBookingsByOrganization(user: any): Promise<any> {
        // return await this.appointmentModel.find({ organizationId: user._id });
        let pipeline = [
            {
                $match: {
                    organizationId: user._id,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientDetails",
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceId",
                    foreignField: "_id",
                    as: "serviceDetails",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "trainerId",
                    foreignField: "_id",
                    as: "trainerDetails",
                },
            },
            {
                $project: {
                    title: {
                        $arrayElemAt: ["$serviceDetails.name", 0],
                    },
                    trainerName: {
                        $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                    },
                    clientName: {
                        $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                    },
                    clientEmail: {
                        $arrayElemAt: ["$clientDetails.email", 0],
                    },
                    clientMobile: {
                        $arrayElemAt: ["$clientDetails.mobile", 0],
                    },
                    serviceId: 1,
                    trainerId: 1,
                    clientId: 1,
                    appointmentId: 1,
                    packageId: 1,
                    date: 1,
                    from: 1,
                    to: 1,
                    facilityId: 1,
                    notes: 1,
                },
            },
        ];

        const result = await this.appointmentModel.aggregate(pipeline);

        if (result.length === 0) {
            throw new BadRequestException("Appointment not found");
        }

        return result;
    }

    async getAllBookingsByWebMaster(user: any): Promise<any> {
        // return await this.appointmentModel.find({ facilityId: new Types.ObjectId(user._id) });
        let pipeline = [
            {
                $match: {
                    facilityId: user._id,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientDetails",
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceId",
                    foreignField: "_id",
                    as: "serviceDetails",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "trainerId",
                    foreignField: "_id",
                    as: "trainerDetails",
                },
            },
            {
                $project: {
                    title: {
                        $arrayElemAt: ["$serviceDetails.name", 0],
                    },
                    trainerName: {
                        $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                    },
                    clientName: {
                        $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                    },
                    clientEmail: {
                        $arrayElemAt: ["$clientDetails.email", 0],
                    },
                    clientMobile: {
                        $arrayElemAt: ["$clientDetails.mobile", 0],
                    },
                    serviceId: 1,
                    trainerId: 1,
                    clientId: 1,
                    appointmentId: 1,
                    packageId: 1,
                    date: 1,
                    from: 1,
                    to: 1,
                    facilityId: 1,
                    notes: 1,
                },
            },
        ];

        const result = await this.appointmentModel.aggregate(pipeline);

        if (result.length === 0) {
            throw new BadRequestException("Appointment not found");
        }

        return result;
    }
    async getAllBookingsByUser(user: any): Promise<any> {
        // return await this.appointmentModel.find({ clientId: user._id });
        let pipeline = [
            {
                $match: {
                    clientId: user._id,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientDetails",
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceId",
                    foreignField: "_id",
                    as: "serviceDetails",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "trainerId",
                    foreignField: "_id",
                    as: "trainerDetails",
                },
            },
            {
                $project: {
                    title: {
                        $arrayElemAt: ["$serviceDetails.name", 0],
                    },
                    trainerName: {
                        $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                    },
                    clientName: {
                        $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                    },
                    clientEmail: {
                        $arrayElemAt: ["$clientDetails.email", 0],
                    },
                    clientMobile: {
                        $arrayElemAt: ["$clientDetails.mobile", 0],
                    },
                    serviceId: 1,
                    trainerId: 1,
                    clientId: 1,
                    appointmentId: 1,
                    packageId: 1,
                    date: 1,
                    from: 1,
                    to: 1,
                    facilityId: 1,
                    notes: 1,
                },
            },
        ];

        const result = await this.appointmentModel.aggregate(pipeline);

        if (result.length === 0) {
            throw new BadRequestException("Appointment not found");
        }

        return result;
    }
    async getAllBookingsByTrainer(user: any): Promise<any> {
        // return await this.appointmentModel.find({ trainerId: user._id });
        let pipeline = [
            {
                $match: {
                    trainerId: user._id,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientDetails",
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceId",
                    foreignField: "_id",
                    as: "serviceDetails",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "trainerId",
                    foreignField: "_id",
                    as: "trainerDetails",
                },
            },
            {
                $project: {
                    title: {
                        $arrayElemAt: ["$serviceDetails.name", 0],
                    },
                    trainerName: {
                        $concat: [{ $arrayElemAt: ["$trainerDetails.firstName", 0] }, " ", { $arrayElemAt: ["$trainerDetails.lastName", 0] }],
                    },
                    clientName: {
                        $concat: [{ $arrayElemAt: ["$clientDetails.firstName", 0] }, " ", { $arrayElemAt: ["$clientDetails.lastName", 0] }],
                    },
                    clientEmail: {
                        $arrayElemAt: ["$clientDetails.email", 0],
                    },
                    clientMobile: {
                        $arrayElemAt: ["$clientDetails.mobile", 0],
                    },
                    serviceId: 1,
                    trainerId: 1,
                    clientId: 1,
                    appointmentId: 1,
                    packageId: 1,
                    date: 1,
                    from: 1,
                    to: 1,
                    facilityId: 1,
                    notes: 1,
                },
            },
        ];

        const result = await this.appointmentModel.aggregate(pipeline);

        if (result.length === 0) {
            throw new BadRequestException("Appointment not found");
        }

        return result;
    }

    private async findAppointmentById(id: string): Promise<any> {
        const appointment = await this.appointmentModel.findById(new Types.ObjectId(id));
        if (!appointment) {
            throw new BadRequestException("Appointment not found");
        }
        return appointment;
    }

    private async checkAccessByOrganization(appointment: any, userId: string): Promise<void> {
        const checkSameOrganization = await this.staffModel.findOne({
            userId: new Types.ObjectId(appointment.trainerId),
            organizationId: new Types.ObjectId(userId),
        });
        if (!checkSameOrganization) {
            throw new BadRequestException("Access Denied");
        }
    }

    private async checkAccessByWebMaster(appointment: any, userId: string): Promise<void> {
        const checkSameFacility = await this.staffModel.findOne({
            userId: new Types.ObjectId(userId),
            facilityId: new Types.ObjectId(appointment.facilityId),
        });
        if (!checkSameFacility) {
            throw new BadRequestException("Access Denied");
        }
    }

    async deleteBookingByOrganization(id: string, user: any): Promise<any> {
        try {
            const appointment = await this.findAppointmentById(id);
            await this.checkAccessByOrganization(appointment, user._id);
            await this.appointmentModel.deleteOne({ _id: new Types.ObjectId(id) });
            return { message: "Appointment deleted successfully" };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteBookingByWebMaster(id: string, user: any): Promise<any> {
        try {
            const appointment = await this.findAppointmentById(id);
            await this.checkAccessByWebMaster(appointment, user._id);
            await this.appointmentModel.deleteOne({ _id: new Types.ObjectId(id) });
            return { message: "Appointment deleted successfully" };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteBookingByUser(id: string, user: any): Promise<any> {
        try {
            const appointment = await this.appointmentModel.findOne({
                _id: new Types.ObjectId(id),
                clientId: new Types.ObjectId(user._id),
            });
            if (!appointment) {
                throw new BadRequestException("Appointment not found");
            }
            await this.appointmentModel.deleteOne({ _id: new Types.ObjectId(id), clientId: new Types.ObjectId(user._id) });
            return { message: "Appointment deleted successfully" };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteBookingByTrainer(id: string, user: any): Promise<any> {
        try {
            const appointment = await this.appointmentModel.findOne({
                _id: new Types.ObjectId(id),
                trainerId: new Types.ObjectId(user._id),
            });
            if (!appointment) {
                throw new BadRequestException("Appointment not found");
            }
            await this.appointmentModel.deleteOne({ _id: new Types.ObjectId(id), trainerId: new Types.ObjectId(user._id) });
            return { message: "Appointment deleted successfully" };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async checkFacilityAvailability(createAppointmentDto: CreateAppointmentDto, user: Object): Promise<Boolean> {
        let facilityAvailable = false;
        let unavailableExist = await this.FacilityAvailableModel.findOne(
            {
                facilityId: createAppointmentDto.facilityId,
                type: AvailabilityType.UNAVAILABLE,
                fromDate: new Date(createAppointmentDto.timeSlots["date"]).setHours(0, 0, 0, 0),
            },
            {},
        );
        return facilityAvailable;
    }

    async checkTrainerAvailability(createAppointmentDto: CreateAppointmentDto, user: Object): Promise<Boolean> {
        return false;
    }
}
