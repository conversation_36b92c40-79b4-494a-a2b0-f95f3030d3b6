import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId } from "class-validator";

export class ResetPinDto {
    @ApiProperty({
        description: "The Id of the organization",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of organization" })
    organizationId: string;

    @ApiProperty({
        description: "User whose pin want to update",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid user details" })
    userId: string;
}
