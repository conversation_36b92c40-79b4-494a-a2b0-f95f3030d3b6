import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

@Schema({ timestamps: true })
export class webHookAPIkey extends Document {
    @Prop({ required: true, unique: true })
    hashedKey: string;
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User", required: true })
    organizationId: string;
    @Prop({ type: String, required: true })
    platform: string;
    @Prop({ default: 'active', enum: ['active', 'revoked'] })
    status: 'active' | 'revoked';

}
export const ApiKeySchema = SchemaFactory.createForClass(webHookAPIkey);