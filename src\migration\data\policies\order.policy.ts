
import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const orderPolicies: IDefaultPolicies[] = [
    // Export
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.ORDERS_ORDER_EXPORT],
        description: 'Grants full access to orders export',
        isActive: true,
        module: ENUM_POLICY_MODULE.ORDERS,
        type: ENUM_POLICY_TYPE.ORDERS_ORDER_EXPORT,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },

    // Order
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.ORDERS_ORDER],
        description: 'Grants view-only access to orders',
        isActive: true,
        module: ENUM_POLICY_MODULE.ORDERS,
        type: ENUM_POLICY_TYPE.ORDERS_ORDER,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.ORDERS_ORDER],
        description: 'Grants full access to orders',
        isActive: true,
        module: ENUM_POLICY_MODULE.ORDERS,
        type: ENUM_POLICY_TYPE.ORDERS_ORDER,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];