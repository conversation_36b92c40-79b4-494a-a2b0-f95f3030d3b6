import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';


@Schema({ timestamps: true })
export class PaymentHistory {
    @Prop({ required: false })
    paymentId: string;

    @Prop({ required: false })
    amount: number;

    @Prop({ required: false })
    shortUrl: string;

    @Prop({ default: 'pending' })
    status: string;

    @Prop({ type: Object, required: false })
    paymentmetadata: Record<string, any>;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string

}

export const PaymentHistorySchema = SchemaFactory.createForClass(PaymentHistory);
