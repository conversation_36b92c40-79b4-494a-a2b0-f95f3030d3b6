import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, Max, MaxLength, Min, ValidateNested } from "class-validator";
import { ActivityLevel } from "src/utils/enums/activity-level.enum";
import { Gender } from "src/utils/enums/gender.enum";

class AddressDto {
    @ApiProperty({
        description: "The address line 1 of the Client.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Address line 1 must be between 2 and 1000 characters" })
    addressLine1: string;

    @ApiProperty({
        description: "The address line 2 of the Client.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Address line 1 must be between 2 and 1000 characters" })
    addressLine2: string;

    @ApiProperty({
        description: "Please specify postal code of the city. || Must be a number",
        example: 122016,
        minLength: 6,
        maxLength: 6,
        required: true,
    })
    @Min(100000, { message: "Postal code must be a number" })
    @Max(999999, { message: "Invalid postal code" })
    postalCode: number;

    @ApiProperty({
        description: "The ID of the city where client lives.",
        example: "659e8032293375c3166a99a0",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of city" })
    @IsNotEmpty({ message: "City is required" })
    city: string;

    @ApiProperty({
        description: "The ID of the state where client lives.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of state" })
    @IsNotEmpty({ message: "State is required" })
    state: string;

    @ApiProperty({
        description: "Country Name",
        example: "India",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Invalid country" })
    country: string;
}

class PoliciesDto {
    @ApiProperty({
        description: "Type of Policy",
        example: "Facility Waiver",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Policy must be between 2 and 50 characters" })
    policyType: string;

    @ApiProperty({
        description: "Url of uploaded document",
        example: "https://document.com",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Required valid document url" })
    documentUrl: string;

    @ApiProperty({
        description: "Policy is enabled or not || Boolean",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of Policy" })
    isEnabled: boolean;
}

export class UpdateClientsMobileDto {
    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of facility" })
    organizationId: string;

    @ApiProperty({
        description: "First name of the client.",
        example: "LND",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "First Name must be between 2 and 50 characters" })
    firstName: string;

    @ApiProperty({
        description: "Last name of the client.",
        example: "Abhay",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Last Name must be between 2 and 50 characters" })
    lastName: string;

    @ApiProperty({
        description: "DOB of the client.",
        example: "Date of the birth in ISO format",
        required: true,
    })
    @IsDate({ message: "Required valid DOB" })
    @Type(() => Date)
    @IsNotEmpty({ message: "DOB is required" })
    dob: Date;

    @ApiProperty({ description: "Gender of the client", enum: Gender, example: Gender.MALE })
    @IsEnum(Gender, { message: "Invalid gender value" })
    gender: Gender;

    @ApiProperty({ description: "Activity level of the client", enum: ActivityLevel, example: ActivityLevel.ADVANCE })
    @IsOptional()
    activityLevel: ActivityLevel;

    @ApiProperty({
        description: "The mobile number of the client.",
        example: "9876543212",
        //minLength: 10,
        //maxLength: 10,
        required: true,
    })
    @IsOptional()
    //@Length(10, 10, { message: "Mobile No. must be exactly 10 digits" })
    mobile: string;

    @ApiProperty({
        description: "The email address for the Organization.",
        example: "<EMAIL>",
        maxLength: 255,
        required: true,
    })
    @Transform(({ value }) => value.toLowerCase())
    @IsEmail({}, { message: "Invalid Email Format" })
    @IsNotEmpty({ message: "Email is required" })
    @MaxLength(255, { message: "Email must be less than 255 characters" })
    email: string;

    @ApiProperty({
        description: "The address of the client.",
        example: AddressDto,
        required: true,
    })
    @ValidateNested({ message: "Address is invalid" })
    @Type(() => AddressDto)
    @IsNotEmpty({ message: "Address is required" })
    address: AddressDto;

    @ApiProperty({
        description: "Emergency Contact person name.",
        example: "Abhay Gupta",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Required valid contacted person name" })
    emergencyContactPerson: string;

    @ApiProperty({
        description: "Emergency Contact person phone.",
        example: "9876543212",
        // minLength: 10,
        // maxLength: 10,
        required: true,
    })
    @IsOptional()
    //@Length(10, 10, { message: "Please enter valid Emergency contact number" })
    emergencyContactPhone: string;

    @ApiProperty({
        description: "The Policies.",
        example: PoliciesDto,
        required: true,
    })
    @ValidateNested({ message: "Policies are invalid" })
    @Type(() => PoliciesDto)
    policies: PoliciesDto[];

    @ApiProperty({
        description: "Photo of the client.",
        example: "https://dnfkjsdfdlf.png",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Image must be a valid string" })
    @IsNotEmpty({ message: "Image cannot be empty" })
    photo: string;
}
