import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
} from '@nestjs/common';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { AuthService } from 'src/auth/services/auth.service';
import { UserService } from 'src/users/services/user.service';
import { IUserDocument } from 'src/users/interfaces/user.interface';
// import { ENUM_USER_STATUS } from 'src/users/enums/user.enum';
import { ENUM_USER_STATUS_CODE_ERROR } from 'src/users/enums/user.status-code.enum';
import { ENUM_ROLE_STATUS_CODE_ERROR } from 'src/role/enums/role.status-code.enum';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UserGuard implements CanActivate {
    private readonly jwtPrefix: string;
    constructor(
        private readonly userService: UserService,
        private readonly authService: AuthService,
        private readonly configService: ConfigService
    ) {
        this.jwtPrefix = this.configService.get<string>('auth.jwt.prefix');
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const response = context.switchToHttp().getResponse();
        const request = context.switchToHttp().getRequest<IRequestApp>();
        const token = request.headers.authorization?.replace(`${this.jwtPrefix} `, '');
        const tokenPayload = request.user;
        const { user, organization } = tokenPayload;
        const { __user } = request;

        const userWithRole: IUserDocument = __user ||
            await this.userService.findOneWithRoleAndPermissions({
                _id: user,
            });

        if (!userWithRole?.isActive) {
            throw new ForbiddenException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
                message: 'user.error.inactive',
            });
        }
        if (!userWithRole?.role?.isActive) {
            throw new ForbiddenException({
                statusCode: ENUM_ROLE_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
                message: 'role.error.inactive',
            });
        }

        // const checkPasswordExpired: boolean =
        //     await this.authService.checkPasswordExpired(
        //         userWithRole.passwordExpired
        //     );
        // if (checkPasswordExpired) {
        //     throw new ForbiddenException({
        //         statusCode: ENUM_USER_STATUS_CODE_ERROR.PASSWORD_EXPIRED,
        //         message: 'auth.error.passwordExpired',
        //     });
        // }
        if (!__user) {
            const newSessionId = await this.authService.startNewSession(request, userWithRole);
            await this.authService.setCookieData(
                response,
                userWithRole,
                newSessionId.toString(),
                {
                    roleType: tokenPayload.type,
                    accessToken: token,
                    tokenType: this.jwtPrefix,
                },
                new Date(tokenPayload.loginDate),
                organization
            );
            // request.session.userId = userWithRole._id.toString();
            // request.session.userSessionId = newSessionId.toString();
            // request.__sessionId = newSessionId.toString();
        }

        request.__user = userWithRole;
        request.__organizationId = organization;

        return true;
    }
}

@Injectable()
export class UserOptionalGuard implements CanActivate {
    constructor(private readonly userService: UserService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<IRequestApp>();
        const { user, organization } = request.user || {};
        const { __user } = request;

        let userWithRole: IUserDocument | null = __user || null;

        if (!userWithRole && user) {
            try {
                userWithRole = await this.userService.findOneWithRoleAndPermissions({
                    _id: user,
                });
            } catch (err) {
                // Log if needed, but don't block request
                userWithRole = null;
            }
        }

        if (
            !userWithRole ||
            !userWithRole.isActive ||
            !userWithRole.role?.isActive
        ) {
            // Silently skip – user will be considered "unauthenticated" downstream
            request.__user = null;
            request.__organizationId = null;
            return true;
        }

        request.__user = userWithRole;
        request.__organizationId = organization;

        return true;
    }
}