// src/modules/widget-payment/service/widget-payment.service.ts
import { Injectable, HttpException, HttpStatus, BadRequestException, NotFoundException } from '@nestjs/common';
import Razorpay from 'razorpay';
import * as crypto from 'crypto';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { PaymentCredential, PaymentCredentialDocument } from 'src/payment/schema/paymentCredential.schema';
import { Clients } from "src/users/schemas/clients.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { CreateInvoicePurchaseDto, PaymentDetailsDTO } from 'src/users/dto/packages-purchasing.dto';
import { PurchaseRequestDto } from 'src/users/dto/purchase-request.dto';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { PurchaseService } from 'src/users/services/purchase.service';
import { User } from 'src/users/schemas/user.schema';

@Injectable()
export class WidgetPaymentService {
    private razorpay: Razorpay;

    constructor(
        @InjectModel(PaymentCredential.name) private paymentCredentialModel: Model<PaymentCredentialDocument>,
        @InjectModel(Clients.name) private clientModel: Model<Clients>,
        @InjectModel(Facility.name) private facilityModel: Model<Facility>,
        @InjectModel(User.name) private userModel: Model<User>,
        private readonly purchaseService: PurchaseService
    ) { }

    async createPaymentOrder(payload: {
        amount: number;
        packageId: string;
        userId: string;
        widgetId: string;
    }) {
        const razorpayCredentials = await this.fetchRazorPayApiKey(payload.widgetId);
        if (!razorpayCredentials.keyId) {
            throw new BadRequestException('Razorpay key not found for this widget');
        }

        this.razorpay = new Razorpay({
            key_id: razorpayCredentials.keyId,
            key_secret: razorpayCredentials.keySecret
        });

        try {
            const order = await this.razorpay.orders.create({
                amount: payload.amount * 100,
                currency: 'INR',
                receipt: `receipt_${Date.now()}`,
                notes: {
                    packageId: payload.packageId,
                    userId: payload.userId,
                    widgetId: payload.widgetId
                }
            });

            return {
                orderId: order.id,
                razorpayKey: razorpayCredentials.keyId,
            };
        } catch (error) {
            throw new BadRequestException(`Failed to create payment order: ${error.message}`);
        }
    }

    async verifyPaymentSignature(payload: {
        razorpay_order_id: string;
        razorpay_payment_id: string;
        razorpay_signature: string;
        packageId: string;
        userId: string;
        widgetId: string;
        amount: number;
        date: string;
    }) {
        const clientDetails = await this.getClientDetailsByUserId(payload.userId);
        if (!clientDetails) {
            throw new NotFoundException(`Client with userId ${payload.userId} not found`);
        }

        const facilityId = clientDetails.facilityId;
        if (!facilityId) {
            throw new BadRequestException('Client does not have an associated facility');
        }

        const facility = await this.facilityModel.findById(facilityId);
        if (!facility) {
            throw new NotFoundException(`Facility with id ${facilityId} not found`);
        }
        const razorpayPaymentMethod = facility.paymentMethods.find(
            method => method.shortId === 'razorPay'
        );

        if (!razorpayPaymentMethod) {
            throw new BadRequestException('Razorpay payment method not configured for this facility');
        }

        const { razorpay_order_id, razorpay_payment_id, razorpay_signature, widgetId, packageId, amount, userId, date } = payload;

        try {
            const razorpayCredentials = await this.fetchRazorPayApiKey(widgetId);
            if (!razorpayCredentials.keySecret) {
                throw new BadRequestException('Razorpay secret key not found for this widget');
            }

            // Verify payment signature
            const generatedSignature = crypto
                .createHmac('sha256', razorpayCredentials.keySecret)
                .update(`${razorpay_order_id}|${razorpay_payment_id}`)
                .digest('hex');

            if (generatedSignature !== razorpay_signature) {
                throw new HttpException(
                    'Payment verification failed',
                    HttpStatus.BAD_REQUEST,
                );
            }

            // Get the user details for the purchase
            const userDetail = await this.userModel.findOne({ _id: userId });
            if (!userDetail) {
                throw new NotFoundException(`User with id ${userId} not found`);
            }

            // Create the purchase request payload in the format expected by processPurchase
            const purchaseRequest: PurchaseRequestDto = {
                cart: {
                    facilityId: facilityId.toString(),
                    userId: userId,
                    organizationId: facility.organizationId.toString(),
                    items: [
                        {
                            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
                            itemId: packageId,
                            quantity: 1,
                            promotionId: null,
                            discount: null
                        }
                    ],
                    discount: null
                },
                paymentDetails: [
                    {
                        paymentMethodId: "67ca84f47a9f67e25e9ef92c",
                        paymentMethod: "razorPay",
                        amount: amount,
                        paymentDate: new Date(),
                        paymentStatus: PaymentStatus.COMPLETED,
                        paymentGateway: 'razorpay',
                        description: `Widget payment for package ${packageId}`,
                        transactionId: razorpay_payment_id
                    }
                ],
                isSplittedPayment: false,
                amountPaid: amount,
                platform: 'WIDGET',
                billingAddressId: clientDetails.billingAddressId || clientDetails.address._id || '',
                paymentBy: userId,
                date: date

            };
            // Process the purchase
            const purchaseResult = await this.purchaseService.processPurchase(purchaseRequest, userDetail);

            return {
                success: true,
                paymentId: razorpay_payment_id,
                orderId: razorpay_order_id,
                purchaseResult: purchaseResult
            };
        } catch (error) {
            console.error('Payment verification error:', error);
            throw new HttpException(
                `Payment verification failed: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async fetchRazorPayApiKey(organizationId: any): Promise<any> {
        const paymentCredential = await this.paymentCredentialModel.findOne({
            organizationId: organizationId,
            paymentGateway: 'razorpay',
        });

        if (!paymentCredential) {
            throw new BadRequestException('Payment credential not found for this organization and payment gateway');
        }

        const decryptedKeyId = this.decrypt(paymentCredential.keyId);
        const decryptedKeySecret = this.decrypt(paymentCredential.keySecret);

        return {
            keyId: decryptedKeyId,
            keySecret: decryptedKeySecret,
        };
    }

    private decrypt(encrypted: string): string {
        const [keyHex, ivHex, encryptedText] = encrypted.split(':');
        const key = Buffer.from(keyHex, 'hex');
        const iv = Buffer.from(ivHex, 'hex');
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }

    async getClientDetailsByUserId(userId: string): Promise<any> {
        try {
            const client = await this.clientModel.findOne({ userId: new Types.ObjectId(userId) })
                .populate('userId', 'name firstName lastName email mobile')
                .exec();

            if (!client) {
                return null;
            }

            return client;
        } catch (error) {
            throw new BadRequestException(`Failed to fetch client details: ${error.message}`);
        }
    }

    async getPaymentMethodByShortId(facilityId: string, shortId: string): Promise<any> {
        try {
            const facility = await this.facilityModel.findById(facilityId);
            if (!facility) {
                throw new NotFoundException(`Facility with id ${facilityId} not found`);
            }

            const paymentMethod = facility.paymentMethods.find(
                method => method.shortId === shortId
            );

            if (!paymentMethod) {
                return null;
            }

            return paymentMethod;
        } catch (error) {
            throw new BadRequestException(`Failed to fetch payment method: ${error.message}`);
        }
    }


}
