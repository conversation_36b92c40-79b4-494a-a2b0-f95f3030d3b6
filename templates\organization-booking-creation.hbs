<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Welcome to </title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f9f9f9;
        }

        .container {
            width: 90%;
            max-width: 700px;
            margin: 30px auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #B095FC;
            color: #fff;
            padding: 20px;
            text-align: center;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 26px;
            font-weight: bold;
        }

        .message p {
            font-size: 16px;
            color: #333;
            line-height: 1.5;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        ul li {
            font-size: 16px;
            margin: 8px 0;
            color: #555;
        }

        ul li strong {
            color: #333;
        }

        .button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px;
            background-color: #007bff;
            color: white;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
        }

        .button:hover {
            background-color: #0056b3;
        }

        .footer {
            font-size: 14px;
            color: #aaaaaa;
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        /* Responsive Styles */
        @media (max-width: 600px) {
            .container {
                width: 95%;
                padding: 20px;
            }

            .header {
                font-size: 22px;
                padding: 15px;
            }

            .button {
                width: 100%;
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
   <div class="header">
    <h1>
        {{#if isEditEmail}}
        {{#if (eq classType "bookings")}}
        Booking Edited
        {{else if (eq classType "personalAppointment")}}
        Appointment Edited
        {{else}}
        Details Edited
        {{/if}}
        {{else}}
        {{#if (eq classType "bookings")}}
        Booking Confirmed
        {{else if (eq classType "personalAppointment")}}
        Appointment Confirmed
        {{else}}
        Confirmation
        {{/if}}
        {{/if}}
    </h1>
</div>

        </div>
        <div class="message">
             {{#if (eq classType "bookings")}}
            <p>Below are  Booking details</p>
            {{else if (eq classType "personalAppointment")}}
            <p>Below are  Appointment details</p>
            {{else}}
            <p>Below are  {{classType}} details</p>
            {{/if}}

            <ul>
                <li><strong>Client Name: </strong> {{clientName}}</li>
                <li><strong>Package Name:</strong> {{packageName}}</li>
                {{#if trainerName}}
                <li><strong>Trainer Name:</strong> {{trainerName}}</li>
                {{/if}}
                {{#if roomName}}
                <li><strong>Room Name:</strong> {{roomName}}</li>
                {{/if}}
                <li>
                    <strong>
                        Date: 
                    </strong> {{date}}
                </li>
                <li><strong>Start Time:</strong> {{from}}</li>
                <li><strong>End Time:</strong> {{to}}</li>
                <li><Strong>Facility Location:</Strong> {{facilityName}}</li>
            </ul>
        </div>
        <div class="footer">
            © 2024 {{facilityName}}. All rights reserved.
        </div>
    </div>
</body>

</html>