import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { AttributeValue, AttributeDocument } from "src/merchandise/schema/attribute.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { CreateAttributeValueDto } from "../dto/createAttributeValue.dto";
import { FilterAttributeValueDto } from "../dto/filterAttributeValue.tro";
import { Types } from "mongoose";
import { Product, ProductSchema, ProductType } from "src/merchandise/schema/product.schema";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";


@Injectable()
export class AttributeService {
    constructor(
        @InjectModel(AttributeValue.name) private attributeValueModel: Model<AttributeDocument>,
        @InjectModel(Product.name) private productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private readonly productVariantModel: Model<ProductVariant>,
        private readonly transactionService: TransactionService,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
    ) { }

    async getOrganizationId(user: IUserDocument) {
        const roleType = user.role.type;
        if (!user._id) {
            throw new BadRequestException("User not found");
        }
        if (!user.role) {
            throw new BadRequestException("User not found");
        }
        if (roleType === ENUM_ROLE_TYPE.WEB_MASTER || roleType === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || roleType === ENUM_ROLE_TYPE.TRAINER) {
            const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
            if (!staffDetails) throw new BadRequestException("Staff not found");
            return staffDetails.organizationId;
        }
        if (roleType === ENUM_ROLE_TYPE.ORGANIZATION) {
            return user._id;
        }
    }

    async createAttributeValue(createAttributeValueDto: CreateAttributeValueDto, user): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const organizationId = await this.getOrganizationId(user) as string;
        try {
            let data: {
                attribute: string;
                value: string;
                hexCode?: string;
                slug: string;
                organizationId: string
            } = { ...createAttributeValueDto, slug: this.generateSlug(createAttributeValueDto.value), organizationId: organizationId };
            const isAttributeExist = await this.attributeValueModel.findOne({ attribute: data.attribute, value: data.value, organizationId: data.organizationId })
            if (isAttributeExist) {
                throw new BadRequestException("Attribute Already Exist");
            }
            data["attribute"] = data.attribute.toLowerCase();
            const newAttributeValue = new this.attributeValueModel(data);
            const createSubAttribute = await newAttributeValue.save();
            await this.transactionService.commitTransaction(session);
            return createSubAttribute;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }


    async getAttributeValues(filterAttributeValueDto: FilterAttributeValueDto, user: any): Promise<any> {
        try {
            const organizationId = await this.getOrganizationId(user)
            const pageSize = filterAttributeValueDto.pageSize ?? 10;
            const page = filterAttributeValueDto.page ?? 1;
            const skip = pageSize * (page - 1);

            let query: any = { attribute: filterAttributeValueDto.attribute.toLowerCase(), organizationId: organizationId };

            if (filterAttributeValueDto.search) {
                const titleQueryString = filterAttributeValueDto.search.trim().split(" ").join("|");
                query["$or"] = [{ value: { $regex: titleQueryString, $options: "i" } }];
            }

            // Allow sorting customization
            const sortField = "createdAt";
            const sortOrder = -1

            const countProm = this.attributeValueModel.countDocuments(query);
            const dataProm = this.attributeValueModel
                .find(query, "_id value image hexCode isTopBrand slug")
                .skip(skip)
                .limit(pageSize)
                .sort({ [sortField]: sortOrder })
                .exec();

            const [count, list] = await Promise.all([countProm, dataProm]);

            return { count, list };
        } catch (error) {
            console.error("Error fetching attribute values:", error);
            throw new Error("Failed to fetch attribute values.");
        }
    }
    async getAttributeValueDetails(id: string): Promise<any> {
        if (!id) {
            throw new BadRequestException("No Sub Attribute find")
        }
        const subAttributeDetail = await this.attributeValueModel.findById(id);
        if (!subAttributeDetail) {
            throw new BadRequestException("No Sub Attribute find")
        }
        return subAttributeDetail;
    }
    async updateAttributeValue(id: string, createAttributeValueDto: CreateAttributeValueDto): Promise<any> {
        const attribute = await this.getAttributeValueDetails(id);
        if (!attribute) {
            throw new BadRequestException("Attribute value not found");
        }
        let data: {
            attribute?: string;
            value?: string;
            hexCode?: string;
            slug?: string;
            organizationId?: string
        } = { ...createAttributeValueDto };
        data["attribute"] = data.attribute.toLowerCase();   
        return await this.attributeValueModel.findByIdAndUpdate(id, data, { new: true });
    }

    async getBrandDetails(user: any) {
        const organizationId = await this.getOrganizationId(user)
        const brandData = await this.attributeValueModel.find({ attribute: "brand", organizationId: organizationId });
        return brandData;
    }



    private generateSlug(name: string): string {
        const shortId = Math.random().toString(36).substring(2, 6);
        return `${name.toLowerCase().split(" ").join("-")}-${shortId}`;
    }
    async deleteAttributeValue(id: string): Promise<any> {
        const query = { "attributes.value": Types.ObjectId.createFromHexString(id) };
        const [product, productVariant] = await Promise.all([this.productModel.findOne(query).exec(), this.productVariantModel.findOne(query).exec()]);

        // If the attribute value is found in any product or product variant, throw an error
        if (product || productVariant) {
            throw new BadRequestException("Attribute value is used in a product or product variant");
        }

        return await this.attributeValueModel.findByIdAndDelete(id);
    }

}