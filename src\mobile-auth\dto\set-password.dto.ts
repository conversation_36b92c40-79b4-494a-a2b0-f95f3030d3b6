import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class SetPasswordDto {
    @ApiProperty({
        description: "passwrod must be a string",
        example: "Test@123",
        required:true
    })
    @IsString({ message: "passwrod must be a string" })
    @IsNotEmpty({ message: "passwrod must not be empty" })
    newPassword: string;

    @ApiProperty({
        description: "passwrod must be a string",
        example: "Test@123",
        required:true
    })
    @IsString({ message: "passwrod must be a string" })
    @IsNotEmpty({ message: "passwrod must not be empty" })
    confirmPassword: string;
}
