name: Deploy Application

on:
  push:
    branches: [ production ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    
    steps:
    - uses: actions/checkout@v3

    - name: Git Ref name
      run: echo ${{ github.ref_name }}
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '22'
        cache: 'npm'

    - name: Create env file
      run: |
        cat << EOF > .env
        # Application Configuration
        NODE_ENV=${{ vars.NODE_ENV }}
        PORT=${{ vars.PORT }}
        HOST_URL=${{ vars.HOST_URL }}
        ADMIN_FRONTEND_APP_URL=${{ vars.ADMIN_FRONTEND_APP_URL }}

        # Database Configuration
        MONGODB_URI=${{ secrets.MONGODB_URI }}
        DATABASE_DEBUG=${{ vars.MONGODB_URI }}

        # Redis Configuration
        REDIS_HOST=${{ vars.REDIS_HOST }}
        REDIS_PORT=${{ vars.REDIS_PORT }}
        REDIS_USERNAME=${{ vars.REDIS_USERNAME }}
        REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}
        REDIS_DATABASE=${{ vars.REDIS_DATABASE }}

        # AWS Configuration
        AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_S3_BUCKET=${{ vars.AWS_S3_BUCKET }}
        AWS_S3_REGION=${{ vars.AWS_S3_REGION }}

        # JWT Configuration
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        JWT_TOKEN_EXPIRED=${{ vars.JWT_TOKEN_EXPIRED }}
        JWT_ISSUER=${{ vars.JWT_ISSUER }}
        JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}
        SESSION_EXPIRED=${{ vars.SESSION_EXPIRED }}
        SESSION_SECRET=${{ secrets.SESSION_SECRET }}


        # Mail Configuration
        MAIL_MAILER=${{ vars.MAIL_MAILER }}
        MAIL_HOST=${{ vars.MAIL_HOST }}
        MAIL_PORT=${{ vars.MAIL_PORT }}
        MAIL_USERNAME=${{ secrets.MAIL_USERNAME }}
        MAIL_PASSWORD=${{ secrets.MAIL_PASSWORD }}
        MAIL_ENCRYPTION=${{ secrets.MAIL_ENCRYPTION }}
        MAIL_FROM_ADDRESS=${{ vars.MAIL_FROM_ADDRESS }}
        CONTACT_US_MAIL=${{ vars.CONTACT_US_MAIL }}
        MAIL_AWS_SES_REGION=${{ vars.MAIL_AWS_SES_REGION }}
        MAIL_AWS_ACCESS_KEY_ID=${{ secrets.MAIL_AWS_ACCESS_KEY_ID }}
        MAIL_AWS_SECRET_ACCESS_KEY=${{ secrets.MAIL_AWS_SECRET_ACCESS_KEY }}
        MAIL_AWS_FROM_ADDRESS=${{ vars.MAIL_AWS_FROM_ADDRESS }}
        MSG91_AUTH_KEY=${{ secrets.MSG91_AUTH_KEY }}
        MSG91_OTP_TEMPLATE_ID=${{ secrets.MSG91_OTP_TEMPLATE_ID }}
        MSG91_BASE_URL=${{ secrets.MSG91_BASE_URL }}
        MSG91_INTEGRATED_NO=${{ secrets.MSG91_INTEGRATED_NO }}
        MSG91_URL=${{ secrets.MSG91_URL }}
        MSG91_NAMESPACE=${{ secrets.MSG91_NAMESPACE }}
        EOF

    - name: Install yarn
      run: npm install -g yarn

    - name: Install dependencies
      run: yarn

    - name: Build
      run: yarn build
      
    - name: Create SSH directory
      run: mkdir -p ~/.ssh/

    - name: Add SSH private key
      run: |
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa

    - name: Add host to known hosts
      run: ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy to server
      run: |
        rsync -avz --delete \
          --exclude='node_modules' \
          ./ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:${{ vars.DEPLOY_PATH }}

    - name: Install dependencies and restart PM2
      id: pm2_restart
      run: |
        ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} '
          source ~/.nvm/nvm.sh && \
          cd ${{ vars.DEPLOY_PATH }} && \
          yarn && \
          pm2 reload ecosystem.prod.config.js --env production || \
          pm2 start ecosystem.prod.config.js --env production
        '

    # - name: Wait for application to start
    #   if: steps.pm2_restart.outcome == 'success'
    #   run: sleep 30 

    # - name: Check application health
    #   id: health_check
    #   if: steps.pm2_restart.outcome == 'success'
    #   run: |
    #     for i in {1..3}; do
    #       response=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.HOST_URL }}/health/status)
    #       if [ $response -eq 200 ]; then
    #         echo "Application is healthy!"
    #         exit 0
    #       fi
    #       echo "Waiting for application to become healthy... (Attempt $i/3)"
    #       sleep 10
    #     done
    #     echo "Application failed to become healthy"
    #     exit 1
    
    # - name: Rollback on failure
    #   if: steps.health_check.outcome == 'failure'
    #   run: |
    #     ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} '
    #       source ~/.nvm/nvm.sh && \
    #       cd ${{ vars.DEPLOY_PATH }} && \
    #       pm2 deploy ecosystem.prod.config.js revert
    #     '
