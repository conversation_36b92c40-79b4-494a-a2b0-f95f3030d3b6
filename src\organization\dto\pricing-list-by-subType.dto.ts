import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsMongoId, IsNotEmpty, IsOptional } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";

export class PricingListBySubTypeDto extends PaginationDTO {
    @ApiProperty({
        description: "OgranizationId.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Organization details are required" })
    @IsMongoId({ message: "Organization details are required" })
    organizationId: string;

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required valid service category details" })
    @IsMongoId({ message: "Required valid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The Id of the sub type of particular service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required valid subType type details" })
    @IsMongoId({ message: "Required valid subType type details" })
    subTypeId: string;


    @ApiProperty({
        description: "The class type of the service category.",
        example: ClassType.BOOKINGS,
        required: true,
    })
    @IsNotEmpty({ message: "Class type cannot be empty" })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;
}
