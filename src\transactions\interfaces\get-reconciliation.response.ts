import { Types } from "mongoose";
import { Denomination } from "../schema/reconciliation.schema";

export interface GetReconciliationResponse {
    _id: Types.ObjectId | string;
    organizationId: Types.ObjectId | string;
    facilityId: Types.ObjectId | string;
    staffId: Types.ObjectId | string;
    leaveAmount: number;
    startingAmount: number;
    pettyAmount: number;
    drawerAmount: number;
    depositAmount: number;
    denominations: Denomination[];
    overUnder: number;
    onlineOverUnder: number;
    cashSales: number;
    otherPayments: Array<{ method: string; total: number; collected: number; overUnder: number }>;
    otherSales: Array<{ method: string; total: number }>;
    createdAt: Date;
    updatedAt: Date;
}