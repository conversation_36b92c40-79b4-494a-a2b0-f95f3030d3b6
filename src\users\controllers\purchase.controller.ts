import { Controller, Post, Body, Get, Param, Delete, ForbiddenException, Query, HttpCode, Patch, StreamableFile, Headers, DefaultValuePipe, ParseIntPipe, Request, Res } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from "@nestjs/swagger";
import { PurchaseService } from "../services/purchase.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GerUserRole, GetDelegatedUser, GetUser } from "src/auth/decorators/get-user.decorator";
import { GetPackagesDto } from "../dto/get-packages.dto";
import { PricingListDTO } from "../dto/pricing-lis.dto";
import { GetInvoicesDto } from "../dto/get-invoices.dto";
import { UpdatePaymentStatusDto } from "src/staff/dto/update-payment-status.dto";
import { CancelOrder } from "src/staff/dto/cancel-order.dto";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ExportInvoicesDto } from "../dto/export-invoices.dto";
import { SuspendMembershipDto } from "../dto/suspend-membership.dto";
import { ResumeMembershipDto } from "../dto/resume-suspended-membership.dto";
import { IUserDocument } from "../interfaces/user.interface";
import { PurchaseRequestDto } from "../dto/purchase-request.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityDelegateProtected, PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { CreateInvoicePurchaseDto } from "../dto/packages-purchasing.dto";
import { Purchase } from "../schemas/purchased-packages.schema";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { ExportZOutReportDto } from "../dto/z-out-report.dto";
import { ExportScheduleReportDto } from "../dto/schedule-report.dto";
import { ExportSalesReportDto } from "../dto/sales-report.dto";
import { ExportSalesByEmpDto } from "../dto/sales-by-emp.dto";
import { UpdatePurchaseSessionDto } from "../dto/update-purchase-session.dto";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { FamilyShareClientDto, SharePackageDto } from "../dto/share-package.dto";
import { PaginationQuery } from "src/common/pagination/decorators/pagination.decorator";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from "src/utils/decorators/pagination-query.decorator";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { ENUM_POLICY_STATUS_CODE_ERROR } from "src/policy/enums/policy.status-code.enum";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { ExportMultipleZOutReportDto } from "../dto/zout-export-multiple.dto";
import { Response as Responses } from 'express';


@ApiTags("modules.purchase")
@ApiBearerAuth()
@Controller("purchase")
export class PurchaseController {
    constructor(
        private readonly purchaseService: PurchaseService,
        private readonly paginationService: PaginationService
    ) { }

    /**
     * @deprecated This api is deprecated 
     * @param createPurchaseDto 
     * @param user 
     * @returns 
     */
    @Post('/')
    @ApiOperation({ summary: "Create a new purchase", deprecated: true, description: "This api is deprecated. Use /v2 instead" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiResponse({ status: 201, description: 'Purchase successfully created', type: Purchase })
    async createPurchase(
        @Body() createPurchaseDto: CreateInvoicePurchaseDto,
        @GetDelegatedUser() user: IUserDocument,
    ): Promise<any> {
        return this.purchaseService.createPurchase(createPurchaseDto, user);
    }

    @ApiOperation({ summary: "Purchase packages, products or custom packages v2" })
    @Response("purchase.success")
    @Post("/v2")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
    @AuthJwtAccessProtected()
    async createPurchaseV2(
        @Body() purchaseDto: PurchaseRequestDto,
        @GetUser() user: IUserDocument,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        purchaseDto.cart.organizationId = organizationId.toString();
        const data = await this.purchaseService.processPurchase(purchaseDto, user);

        return {
            data
        }
    }

    @Get('qr-codes')
    @ApiOperation({ summary: 'Fetch purchase QR codes with package details' })
    @AuthJwtAccessProtected()
    async getQrCodes(
        @GetUser() user,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    ) {
        const data = await this.purchaseService.getQrCodesForUser(user._id, page, limit);

        return {
            message: 'QR codes fetched successfully',
            data: {
                ...data,
                page,
                limit,
            },
        };
    }

    @Post('/fetch-data-from-qrcode/:purchaseId')
    @ApiOperation({ summary: 'Fetch data to populate into booking form' })
    @AuthJwtAccessProtected()
    async fetchQrBookingData(@GetUser() user: any, @Param("purchaseId") purchaseId: string): Promise<any> {
        const data = await this.purchaseService.fetchQrBookingData(user, purchaseId);

        return {
            message: 'Client list Fetched successfully',
            data: data,
        };
    }

    @Post("/list")
    @ApiOperation({ summary: "Get all purchases" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getAllPurchases(
        @GerUserRole("type") roleType: string,
        @GetUser("_id") userId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() getPackageDto: GetPackagesDto,
    ): Promise<any> {
        const filter = {
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            organizationId: organizationId
        };
        if (getPackageDto.clientId) {
            filter["consumers"] = getPackageDto.clientId;
        }
        if (roleType == ENUM_ROLE_TYPE.USER) {
            filter["userId"] = userId;
        }

        const data = await this.purchaseService.getAllPurchases(filter, getPackageDto.classType);
        return {
            message: "Package fetched successfully",
            data
        }
    }

    @Get(":id")
    @ApiOperation({ summary: "Get purchase by ID" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getPurchaseById(@Param("id") id: string, @GetUser() user: IUserDocument): Promise<any> {
        return this.purchaseService.getPurchaseById(id, user);
    }

    // @Delete(":id")
    // @ApiOperation({ summary: "Delete purchase by ID" })
    // @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
    // @AuthJwtAccessProtected()
    // async deletePurchaseById(@Param("id") id: string, @GetUser() user: IUserDocument): Promise<any> {
    //     return this.purchaseService.deletePurchaseById(id, user);
    // }

    @Post("/list/:classType")
    @ApiOperation({ summary: "List purchases by class type" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async purchaseListByClassType(@Param("classType") classType: string, @GetUser() user: IUserDocument, @Body() pricingListDTO: PricingListDTO): Promise<any> {
        return this.purchaseService.purchaseListByClassType(classType, user, pricingListDTO);
    }

    @Post("/all-listType")
    @ApiOperation({ summary: "List purchases of all class type" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async allTypeOfPurchaseList(@GetUser() user: IUserDocument, @Body() pricingListDTO: PricingListDTO): Promise<any> {
        return this.purchaseService.allTypeOfPurchaseList(user, pricingListDTO);
    }

    @Post("/active-pricing/:userId")
    @ApiOperation({ summary: "List active pricing by userId" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async activePricingByUserId(@Param("userId") userId: string, @GetUser() user: IUserDocument, @Body() body: PaginationDto): Promise<any> {
        const packages = await this.purchaseService.activePricingByUserId(userId, body, user);
        const totalPages = Math.ceil(packages.count / body.pageSize)
        return {
            message: "Purchase history fetched successfully",
            totalCount: packages.count,
            totalPages: totalPages ? totalPages : 0,
            page: body.page,
            perPage: body.pageSize,
            data: packages.data
        };
    }

    @Post("/in-active-pricing/:userId")
    @ApiOperation({ summary: "List active pricing by userId" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async inActivePricingByUserId(
        @Param("userId") userId: string,
        @Body() body: PaginationDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const packages = await this.purchaseService.inactivePricingByUserId(userId, body, organizationId);
        const totalPages = Math.ceil(packages.count / body.pageSize);
        return {
            message: "Purchase history fetched successfully",
            totalCount: packages.count,
            totalPages: totalPages ? totalPages : 0,
            page: body.page,
            perPage: body.pageSize,
            data: packages.data
        };
    }

    @Post("/invoice/list")
    @HttpCode(200)
    @ApiOperation({ summary: "Invoice list" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_READ)
    @AuthJwtAccessProtected()
    async getInvoiceList(
        @Body() body: GetInvoicesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const invoices = await this.purchaseService.getInvoiceList(organizationId, body);
        const totalPages = Math.ceil(invoices.count / body.pageSize)
        return {
            message: "Orders fetched successfully",
            totalCount: invoices.count,
            totalPages: totalPages,
            perPage: body.pageSize,
            page: body.page,
            data: invoices.data
        }
    }

    @Post("/invoice/export")
    @HttpCode(200)
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_EXPORT)
    @AuthSessionProtected(true)
    @AuthJwtAccessProtected()
    async InvoiceExport(
        @Headers('X-Timezone') userTimezone,
        @Body() body: ExportInvoicesDto,
        @GetDelegatedUser() user: IUserDocument,
    ): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.exportInvoiceList(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Orders fetched successfully",
            data: invoices.data
        }
    }

    @Get("/invoice/:invoiceId")
    @ApiOperation({ summary: "Invoice list" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_READ)
    @AuthJwtAccessProtected()
    async getInvoiceDetail(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Param('invoiceId') invoiceId
    ): Promise<any> {
        const invoices = await this.purchaseService.getInvoiceDetails(organizationId, invoiceId);
        return {
            message: "Order fetched successfully",
            data: invoices
        }
    }

    @Patch("/invoice/update-payment")
    @ApiOperation({ summary: "Invoice list" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_PAYMENT)
    @AuthJwtAccessProtected()
    async changePaymentStatus(
        @GetUser() user,
        @Body() body: UpdatePaymentStatusDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
    ): Promise<any> {
        const invoices = await this.purchaseService.changePaymentStatus(organizationId, body, user._id);
        return {
            message: "Order's payment updated successfully",
            date: invoices
        }
    }

    @Patch("/invoice/cancel")
    @ApiOperation({ summary: "Invoice list" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_WRITE)
    @AuthJwtAccessProtected()
    async cancelOrder(
        @GetUser() user,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() body: CancelOrder
    ): Promise<any> {
        const invoices = await this.purchaseService.cancelOrder(organizationId, user, body);
        return {
            message: "Order canceled successfully",
            date: invoices,
        };
    }


    @Get("/invoice/:invoiceId/download")
    @ApiOperation({ summary: "Download invoice PDF" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_READ)
    @AuthJwtAccessProtected()
    async getDownloadInvoice(@GetUser() user, @Param('invoiceId') invoiceId): Promise<any> {
        return await this.purchaseService.getDownloadInvoice(user, invoiceId);
    }


    @Get("/get-service/:purchaseId")
    @ApiOperation({ summary: "Get list of services" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getServiceByPurchaseId(@Param("purchaseId") purchaseId: string, @GetUser() user): Promise<any> {
        return this.purchaseService.getServiceByPurchaseId(purchaseId);
    }


    @Post('/membership/suspend')
    @ApiOperation({ summary: "Suspend membership" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async suspendMembership(@GetUser() user, @Body() body: SuspendMembershipDto): Promise<any> {
        const data = await this.purchaseService.suspendMembership(user, body);
        return {
            message: "Membership suspended successfully",
            data
        }
    }

    @Post('/membership/suspend/resume')
    @ApiOperation({ summary: "Resume membership" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
    @AuthJwtAccessProtected()
    async resumeMembership(@GetUser() user, @Body() body: ResumeMembershipDto): Promise<any> {
        const data = await this.purchaseService.resumeMembership(user, body);
        return {
            message: "Membership resumed successfully",
            data,
        };
    }

    @Post("Z-out/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Z-Out Report" })
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async zOutReportByEmp(
        @Headers('X-Timezone') userTimezone: string,
        @GetDelegatedUser() user: any,
        @Body() body: ExportZOutReportDto
    ) {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
        const report = await this.purchaseService.zOutReportByEmp(user, body, userTimezone);
        if (body.responseType === 'stream' || body.responseType === 'pdf') {
            return report;
        }
        return {
            message: 'Z-Out Report fetched successfully',
            data: report
        };
    }

    @Post("/Z-outHistory/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export Multiple Z-Out Reports" })
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async exportMultipleZOutReports(
        @Headers('X-Timezone') userTimezone: string,
        @GetDelegatedUser() user: any,
        @Body() body: ExportMultipleZOutReportDto,
        @Res({ passthrough: true }) res: Responses
    ) {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;

        return await this.purchaseService.exportMultipleZOutReports(
            user,
            body,
            userTimezone,
            res
        );
    }

    @Post("/scheduleAtGlance/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export Schedule At Glance Report" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async scheduleAtGlance(
        @Headers('X-Timezone') userTimezone: string,
        @GetDelegatedUser() user: any,
        @Body() body: ExportScheduleReportDto,
    ): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.scheduleAtGlance(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Schedule At Glance Report fetched successfully",
            data: invoices
        }
    }

    @Post("/salesByEmp/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export Sales By Employee Report" })
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async salesByEmp(@Headers('X-Timezone') userTimezone, @GetDelegatedUser() user, @Body() body: ExportSalesByEmpDto): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.salesByEmp(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Sales By Employee Report fetched successfully",
            data: invoices
        }
    }

    @Post("/salesByCategory/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export Slaes by Service Category Report" })
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async salesByCategory(@Headers('X-Timezone') userTimezone, @GetDelegatedUser() user, @Body() body: ExportSalesReportDto): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.salesByCategory(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Grouped Service Category Sales Report fetched successfully",
            data: invoices
        }
    }

    @Post("/salesReport/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export sales report" })
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async salesReportExport(@Headers('X-Timezone') userTimezone, @GetDelegatedUser() user, @Body() body: ExportSalesReportDto): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.exportSalesList(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Sales Report fetched successfully",
            data: invoices.data
        }
    }

    @Post("/eligible-return-pricing/:userId")
    @ApiOperation({ summary: "List pricing eligible for return by userId" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getEligibleReturnPricingByUserId(
        @Param("userId") userId: string,
        @GetUser() user,
        @Body() body: PaginationDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const packages = await this.purchaseService.getEligibleReturnPricingByUserId(userId, body, organizationId);
        const totalPages = Math.ceil(packages.count / body.pageSize);
        return {
            message: "Eligible return pricing fetched successfully",
            totalCount: packages.count,
            totalPages: totalPages ? totalPages : 0,
            page: body.page,
            perPage: body.pageSize,
            data: packages.data
        };
    }


    @Post("/eligible-return-custom-package/:userId")
    @ApiOperation({ summary: "List pricing eligible for return by userId" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getEligibleReturnCustomPackageByUserId(
        @Param("userId") userId: string,
        @GetUser() user,
        @Body() body: PaginationDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const packages = await this.purchaseService.getEligibleReturnCustomPackageByUserId(userId, body, organizationId);
        const totalPages = Math.ceil(packages.count / body.pageSize);
        return {
            message: "Eligible return custom package fetched successfully",
            totalCount: packages.count,
            totalPages: totalPages ? totalPages : 0,
            page: body.page,
            perPage: body.pageSize,
            data: packages.data
        };
    }

    @Post("/update-session")
    @ApiOperation({ summary: "Update purchase session count and dates" })
    @PolicyAbilityDelegateProtected()
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async updatePurchaseSession(
        @GerUserRole("type") roleType: string,
        @Body() body: UpdatePurchaseSessionDto,
        @GetDelegatedUser() user: any,
        @Request() req: IRequestApp,
    ): Promise<any> {
        const permissionSet = req.__permissionSet;

        let result: any;
        if (roleType === ENUM_ROLE_TYPE.ORGANIZATION || permissionSet?.has(ENUM_PERMISSION_TYPE.PURCHASE_PACKAGE_UPDATE_SESSION)) {
            result = await this.purchaseService.updatePurchaseSession(body, user);
        } else if (permissionSet?.has(ENUM_PERMISSION_TYPE.PURCHASE_PACKAGE_UPDATE_START)) {
            result = await this.purchaseService.updatePurchaseSession({ startDate: body.startDate, purchaseId: body.purchaseId }, user);
        } else {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
                message: "policy.error.abilityForbidden",
            });
        }

        return {
            message: "Purchase session and dates updated successfully",
            data: result,
        };
    }

    @Post("/session-logs/:purchaseId")
    @ApiOperation({ summary: "Get session logs by purchaseId with pagination" })
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_READ)
    @AuthJwtAccessProtected()
    async getSessionLogsByPurchaseId(
        @Param("purchaseId") purchaseId: string,
        @GetUser() user: any,
        @Body() body: PaginationDto,
    ): Promise<any> {
        const result = await this.purchaseService.getSessionLogsByPurchaseId(purchaseId, user, body);
        const totalPages = Math.ceil(result.totalCount / body.pageSize);
        return {
            message: "Session logs fetched successfully",
            purchaseId,
            totalCount: result.totalCount,
            totalPages: totalPages || 0,
            page: body.page,
            perPage: body.pageSize,
            data: result.logs,
        };
    }

    @ApiOperation({ summary: "Share package to multiple users" })
    @Response("purchase.sharePackage")
    @Post("/share-package")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
    @AuthJwtAccessProtected()
    async sharePackage(
        @Body() body: SharePackageDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const result = await this.purchaseService.sharePackage(organizationId, body);
        return {
            data: result,
        };
    }

    @ApiOperation({ summary: "Share package to multiple users" })
    @ResponsePaging("user.list")
    @Get("/share-package/client-list")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    async shareClientList(
        @PaginationQuery({
            defaultOrderBy: 'name',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC,
            availableOrderBy: ['name'],
            availableSearch: ['name']
        }) { _limit, _search, _offset, _order }: FamilyShareClientDto,
        @Query() body: FamilyShareClientDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {

        const filter: Record<string, any> = {
            _id: body.purchaseId,
            userId: body.userId,
            organizationId,
            isActive: true,
            isExpired: false,
            endDate: { $gte: new Date() },
            ..._search
        }

        const result = await this.purchaseService.familyShareClientList(filter, _limit, _offset, _order);
        return {
            _pagination: {
                total: result.total,
                totalPage: this.paginationService.totalPage(result.total, _limit),
                page: body.page,
                pageSize: body.pageSize,
            },
            data: result.data,
        };
    }
    @Post("/salesByRevenueCategory/export")
    @HttpCode(200)
    @ApiOperation({ summary: "Export Slaes by Service Category Report" })
    // @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    async salesByRevenueCategory(@Headers('X-Timezone') userTimezone, @GetDelegatedUser() user, @Body() body: ExportSalesReportDto): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone
        const invoices = await this.purchaseService.salesByRevenueCategory(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(invoices.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=invoices.csv'
            });
        }
        return {
            message: "Grouped Service Category Sales Report fetched successfully",
            data: invoices
        }
    }
}