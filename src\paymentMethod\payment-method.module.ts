import { forwardRef, Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";

import { PaymentMethodService } from "./service/payment-method.service";
import { PaymentMethod, PaymentMethodSchema } from "./schemas/payment-method.schema";
import { PaymentMethodController } from "./controllers/payment-method.controller";
import { Invoice, InvoiceSchema } from "src/users/schemas/invoice.schema";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Module({
    imports: [
        forwardRef(() => WaitTimeModule),
        AuthModule,
        UtilsModule,
        MailModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([
            { name: PaymentMethod.name, schema: PaymentMethodSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: Invoice.name, schema: InvoiceSchema },
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [PaymentMethodController],
    providers: [PaymentMethodService],
    exports: [PaymentMethodService], // Exporting the service for use in other modules if needed
})
export class PaymentMethodModule {}
