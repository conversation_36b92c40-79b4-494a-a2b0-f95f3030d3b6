import { IsBoolean, IsEnum, IsOptional, IsString, ValidateNested,IsInt } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { CategoryLevel, CategoryStatus } from "../schema/category.schema";
import { Type } from "class-transformer";
import { ApiProperty } from '@nestjs/swagger';

export class FilterCategoryDto  {
    @ApiProperty({
        description: 'The content to search',
        type: String,
        required: false,
        example: 'brand ',
    })
    @IsOptional()
    @IsString()
    search: string;


    @ApiProperty({
        description: 'whether the category is of first Level or second level',
        type: String,
        required: false,
        example: 'first',
    })
    @IsOptional()
    @IsEnum(CategoryLevel)
    level: string;

    @ApiProperty({
        description: 'The ID of the Parent Category',
        type: String,
        required: false,
        example: '64f5e8d3b6374d25f0e6f8b2',
    })
    @IsOptional()
    parentId: string;


    @ApiProperty({
        description: 'Is category active or inactive filter',
        type: String,
        required: false,
        example: 'active',
    })
    @IsOptional()
    @IsEnum(CategoryStatus)
    status: string;
    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: false,
    })
    @IsOptional()
    @IsInt()
    pageSize?: number

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: false,
    })
    @IsOptional()
    @IsInt()
    page?: number;
}
