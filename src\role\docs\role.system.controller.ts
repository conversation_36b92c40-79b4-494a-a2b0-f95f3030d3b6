import { applyDecorators } from '@nestjs/common';
import {
    Doc,
    DocAuth,
    DocRequest,
    DocResponsePaging,
} from 'src/common/doc/decorators/doc.decorator';
import { RoleDocQueryType, RoleUserDocQueryType } from 'src/role/constants/role.doc.constant';
import { RoleShortResponseDto } from 'src/role/dtos/response/role.short.response.dto';

export function RoleSystemListDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'get all of roles',
        }),
        DocRequest({
            queries: RoleDocQueryType,
        }),
        DocResponsePaging<RoleShortResponseDto>('role.list', {
            dto: RoleShortResponseDto,
        })
    );
}

export function RoleSystemUserListDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'get all of roles',
        }),
        DocRequest({
            queries: RoleUserDocQueryType,
        }),
        // DocResponsePaging<RoleShortResponseDto>('role.list', {
        //     dto: RoleShortResponseDto,
        // })
    );
}
