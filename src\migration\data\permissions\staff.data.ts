import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const staffPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Staff Write",
        type: ENUM_PERMISSION_TYPE.STAFF_WRITE,
        description: 'Grant staff write access',
        isDelegated: true,
    },
    {
        name: "Staff Read",
        type: ENUM_PERMISSION_TYPE.STAFF_READ,
        description: '<PERSON> staff read access',
        isDelegated: true,
    },
    {
        name: "Staff Update",
        type: ENUM_PERMISSION_TYPE.STAFF_UPDATE,
        description: 'Grant staff update access',
        isDelegated: true,
    },
    {
        name: "Staff Delete",
        type: ENUM_PERMISSION_TYPE.STAFF_DELETE,
        description: 'Grant staff delete access',
        isDelegated: true,
    },
    {
        name: "Staff Availability Write",
        type: ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_WRITE,
        description: 'Grant staff availability write access',
        isDelegated: true,
    },
    {
        name: "Staff Availability Read",
        type: ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ,
        description: 'Grant staff availability read access',
        isDelegated: true,
    },
    {
        name: "Staff Availability Update",
        type: ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_UPDATE,
        description: 'Grant staff availability update access',
        isDelegated: true,
    },
    {
        name: "Staff Availability Delete",
        type: ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_DELETE,
        description: 'Grant staff availability delete access',
        isDelegated: true,
    },
];

