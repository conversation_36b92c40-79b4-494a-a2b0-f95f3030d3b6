import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { AttributeType } from "src/utils/enums/attribute-type.enum";

export class AttributeListFilterDto {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Yoga",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    @IsNotEmpty({ message: "Search must not be empty" })
    search: string;

    @ApiProperty({
        description: "Attribute type is an enum",
        enum: AttributeType,
        example: AttributeType.CATEGORY || AttributeType.PLAN_TYPE || AttributeType.TIER,
    })
    @IsEnum(AttributeType, { message: "Attribute type is not valid" })
    attributeType: AttributeType;
}
