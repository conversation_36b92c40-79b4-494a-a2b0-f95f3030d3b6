import { Module } from "@nestjs/common";
import { StaffController } from "./controllers/staff.controller";
import { StaffService } from "./services/staff.service";
import { AuthModule } from "src/auth/auth.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { StaffProfileDetails, StaffSchema } from "./schemas/staff.schema";
import { MailModule } from "src/mail/mail.module";
import { AdminStaffController } from "./controllers/admin-staff.controller";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { PassportModule } from "@nestjs/passport";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { Organizations, OrganizationSchema } from "src/organization/schemas/organization.schema";
import { StaffAvailability, StaffAvailabilitySchema } from "./schemas/staff-availability";
import { StaffPipe } from "./pipes/staff.pipe";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { FacilityAvailability, FacilityAvailabilitySchema } from "src/facility/schemas/facility-availability.schema";
import { Otp, OtpSchema } from "src/auth/schemas/otp.schema";
import { PayRate, PayRateSchema } from "src/staff/schemas/pay-rate.schema";
import { PayRateService } from "src/staff/services/pay-rate.service";
import { PayRateController } from "src/staff/controllers/pay-rate.controller";
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { Services } from "src/organization/schemas/pricing.schema";
import { ServiceSchema } from "src/organization/schemas/services.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { RoleModule } from "src/role/role.module";
import { Room, RoomSchema } from "src/room/schema/room.schema";

@Module({
    imports: [
        AuthModule,
        UtilsModule,
        MailModule,
        RoleModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([{ name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: Organizations.name, schema: OrganizationSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Otp.name, schema: OtpSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: StaffAvailability.name, schema: StaffAvailabilitySchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: Room.name, schema: RoomSchema }
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [StaffController, AdminStaffController, PayRateController],
    providers: [StaffService, StaffPipe, PayRateService],
})
export class StaffModule {}
