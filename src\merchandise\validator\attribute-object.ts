import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from "class-validator";
import { Types } from "mongoose";
import { AttributeList, VariantAttributes } from "../constant/attributeList.constant";
import { Attributes } from "src/utils/enums/attribute.enum";
import { InputFieldType } from "src/utils/enums/input-field-type.enum";

@ValidatorConstraint({ name: "isAttributeObject", async: false })
export class IsAttributeObject implements ValidatorConstraintInterface {
  errors: string[] = [];

  validate(attributes: object, args: ValidationArguments) {
    if (typeof attributes !== "object" || attributes === null) {
      this.errors.push("Attributes must be an object");
      return false;
    }

    const mandatoryAttributes = Object.entries(AttributeList)
      .filter(([_, definition]) => definition.type === "mandatory")
      .map(([key]) => key);

    for (const attribute of mandatoryAttributes) {
      if (!(attribute in attributes)) {
        this.errors.push(`Missing mandatory attribute: ${attribute}`);
        return false;
      }
    }

    for (const key in attributes) {
      if (args.object["type"] === "variable") {
        if (VariantAttributes.includes(key as any)) {
          this.errors.push(`Invalid attribute (only allowed for variants): ${key}`);
          return false;
        }
      }

      if (!Object.values(Attributes).includes(key as Attributes)) {
        this.errors.push(`Invalid attribute: ${key}`);
        return false;
      }

      const value = attributes[key];
      const attributeDefinition = AttributeList[key as Attributes];

      if (attributeDefinition.multiple) {
        if (!Array.isArray(value) && !value.every(item => Types.ObjectId.isValid(item))) {
          this.errors.push(`Invalid value for attribute ${key}: ${value}`);
          return false;
        }
      } else {
        if (attributeDefinition.inputType !== InputFieldType.TextBox) {
          if (!Types.ObjectId.isValid(value)) {
            this.errors.push(`Invalid value for attribute ${key}: ${value}`);
            return false;
          }
        }
      }
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return this.errors.join(", ");
  }
}
