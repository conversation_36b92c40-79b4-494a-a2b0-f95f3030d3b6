
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const transactionPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Transaction Write",
        type: ENUM_PERMISSION_TYPE.TRANSACTION_WRITE,
        description: 'Grant transaction write access',
        isDelegated: true,
    },
    {
        name: "Transaction Read",
        type: ENUM_PERMISSION_TYPE.TRANSACTION_READ,
        description: 'Grant transaction read access',
        isDelegated: true,
    },
];