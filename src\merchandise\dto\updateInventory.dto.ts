import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsInt,
  IsMongoId,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  Max,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  MinDate,
  ValidateIf,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ProductType } from '../schema/product.schema';

export enum InventoryUpdateAction {
  RESTOCK = 'restock',      // relative add/subtract (delta; can be negative)
  ADJUSTMENT = 'adjustment' // absolute set (target >= 0)
}

export class UpdateInventoryDto {
  // ========== Identity / relations (optional on update) ==========
  @ApiPropertyOptional({ enum: ProductType, description: 'Type of the product' })
  @IsOptional()
  @IsEnum(ProductType)
  @IsString()
  productType?: ProductType;

  @ApiPropertyOptional({ description: 'MongoDB ID of the product' })
  @IsOptional()
  @IsMongoId()
  productId?: string;

  @ApiPropertyOptional({ description: 'MongoDB ID of the product variant (if applicable)' })
  @ValidateIf(o => o.productType === ProductType.VARIABLE)
  @IsMongoId()
  @IsOptional()
  productVariantId?: string;

  @ApiPropertyOptional({ description: 'MongoDB ID of the store' })
  @IsOptional()
  @IsMongoId()
  storeId?: string;

  // ========== Pricing (optional on update) ==========
  @ApiPropertyOptional({ description: 'Sale price of the product', example: 499.99 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  salePrice?: number;

  @ApiPropertyOptional({ description: 'Maximum retail price of the product', example: 599.99 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  mrp?: number;

  @ApiPropertyOptional({ description: 'Discount percentage (0-100)', example: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Max(100, { message: 'Discount should be less than 100' })
  @Min(0, { message: 'Discount should be greater than or equal to 0' })
  discount?: number;

  @ApiPropertyOptional({ description: 'Final price after applying discount', example: 449.99 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  discountPrice?: number;

  @ApiPropertyOptional({ description: 'Expiry date of the product', example: '2025-12-31' })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
  @IsDate()
  @MinDate(new Date(), { message: 'Expiry date must be in the future' })
  expiryDate?: Date | null;

  // ========== Promotions (optional on update) ==========
  @ApiPropertyOptional({ description: 'MongoDB ID of the promotion (if applicable)' })
  @IsOptional()
  @IsMongoId()
  promotionId?: string;

  @ApiPropertyOptional({
    description: 'Apply promotions to item',
    type: [String],
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
  })
  @IsArray()
  @IsOptional()
  applyPromotion?: string[];

  // ========== Inventory action (the new history-aware update) ==========
  @ApiPropertyOptional({
    enum: InventoryUpdateAction,
    description: 'How to update stock: restock (delta) or adjustment (absolute set)',
    example: InventoryUpdateAction.RESTOCK,
  })
  @IsOptional()
  @IsEnum(InventoryUpdateAction)
  inventoryAction?: InventoryUpdateAction;

  @ApiPropertyOptional({
    description:
      'For restock: delta (+/-). For adjustment: absolute target (>= 0).',
    example: 10,
  })
  @ValidateIf(o => o.inventoryAction === InventoryUpdateAction.RESTOCK)
  @Type(() => Number)
  @IsInt({ message: 'adjustQty must be an integer' })
  @IsOptional()
  adjustQty?: number; // delta when RESTOCK

  @ValidateIf(o => o.inventoryAction === InventoryUpdateAction.ADJUSTMENT)
  @Type(() => Number)
  @IsInt({ message: 'adjustQty must be an integer' })
  @Min(0, { message: 'For adjustment, target quantity cannot be negative' })
  @IsOptional()
  adjustQtyTarget?: number; // absolute when ADJUSTMENT (optional alt name; see note below)

  @ApiPropertyOptional({ description: 'Optional notes for ledger', example: 'Damaged items returned' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}
