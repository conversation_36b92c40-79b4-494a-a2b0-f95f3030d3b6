import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { Gender } from "src/utils/enums/gender.enum";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, ValidateNested } from "class-validator";
import { RELATION_ENUM } from "src/utils/enums/relation.enum";
class PoliciesDto {
    @ApiProperty({
        description: "Type of Policy",
        example: "Facility Waiver",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Policy must be between 2 and 50 characters" })
    policyType: string;

    @ApiProperty({
        description: "Url of uploaded document",
        example: "https://document.com",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Required valid document url" })
    documentUrl: string;

    @ApiProperty({
        description: "Policy is enabled or not || Boolean",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of Policy" })
    isEnabled: boolean;
    @ApiProperty({
        description: "Date of the Policy Accepted"
    })
    @IsString()
    @IsOptional()

    date: string
    @ApiProperty({
        description: "Date of the Policy Accepted"
    })
    @IsOptional()
    @IsString()
    expiryDate: string
    @ApiProperty({
        description: "Policy is Required or not || Boolean",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of Policy" })
    required: boolean;

    @ApiProperty({
        description: "Policy Id is Required or not || Boolean",
        example: true,
        required: true,
    })
    @IsOptional()
    @IsMongoId()
    policyId: string;
}
export class MinerRequestDto {
    @ApiProperty({
        description: "First name of the miner.",
        example: "John",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @IsNotEmpty({ message: "First Name is required" })
    @Type(() => String)
    firstName: string;

    @ApiProperty({
        description: "Last name of the miner.",
        example: "Wick",
        minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    @Type(() => String)
    lastName: string = '';

    @ApiProperty({
        description: "DOB of the miner.",
        example: "2025-01-01",
        required: true,
    })
    @IsNotEmpty({ message: "DOB is required" })
    @Type(() => Date)
    dob: Date;

    @ApiProperty({
        description: "Relation of the miner with the client.",
        example: "father",
        required: true,
    })
    @IsNotEmpty({ message: "Relation is required" })
    @Type(() => String)
    @IsEnum(RELATION_ENUM, { message: "Invalid relation" })
    relation: RELATION_ENUM;

    @ApiProperty({
        description: "Gender of the miner.",
        enum: Gender,
        example: Gender.MALE,
        required: true,
    })
    @IsNotEmpty({ message: "Gender is required" })
    @Type(() => String)
    gender: Gender
    @ApiProperty({
        description: "Id of the minor if coming from the wavieer form",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: " Invalid Minor Id" })
    minorId?: string;

    @ApiProperty({
        description: "photo of the miner.",
        required: false,
    })
    @IsOptional()
    @Type(() => String)
    photo: String;
      @ApiProperty({
        description: "The Policies.",
        example: PoliciesDto,
        required: true,
    })
    @ValidateNested({ message: "Policies are invalid" })
    @Type(() => PoliciesDto)
    policies?: PoliciesDto[];
}


export class CreateMinorRequestDto {
    @ApiProperty({
        description: "Id of the client",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Client id is required" })
    @Type(() => String)
    clientId: string;

    @ApiProperty({
        description: "Id of the waiver lead",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid Wavier Source Id" })
    wavierSourceId: string;

    @ApiProperty({
        description: "Minor request data.",
        type: [MinerRequestDto],
        example: MinerRequestDto,
        required: false,
    })
    @IsOptional()
    @ValidateNested({ each: true, message: "Miner request data is invalid" })
    @IsArray({ message: "Miner request data must be an array" })
    @Type(() => MinerRequestDto)
    minor?: MinerRequestDto[] = [];
  

}
