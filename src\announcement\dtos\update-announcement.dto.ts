import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAnnouncementDto } from './create-announcement.dto';

export class UpdateAnnouncementDto extends PartialType(CreateAnnouncementDto) {
    @ApiProperty({ description: 'The id of the announcement', example: '66b54c326b54c326b54c326b' })
    @IsString()
    @IsNotEmpty({ message: 'Please select announcement' })
    _id: string;
}