import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsMongoId, IsOptional, IsString } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class CreateClassesDto {
    @ApiProperty({
        description: "The name of the Class.",
        example: "Knox",
        required: true,
    })
    @IsString({ message: "Invalid Class Name" })
    className: string;

    @ApiProperty({
        description: "The name of the group.",
        example: "The Lab",
        required: true,
    })
    @IsOptional()
    @IsString({ message: "Invalid Group Name" })
    groupName: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Tier for the Class.",
        example: "66b356206b37a50962c56380",
        required: true,
    })
    @IsMongoId({ message: "Invalid class tier" })
    tier: string;

    @ApiProperty({
        description: "Description for the Class.",
        example: "Description",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid description" })
    description: string;

    @ApiProperty({
        description: "Image for the Class.",
        example: "https://abc.com",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid image" })
    image: string;
}
