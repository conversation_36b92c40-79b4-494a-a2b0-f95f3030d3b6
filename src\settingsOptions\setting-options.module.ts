import { Modu<PERSON> } from '@nestjs/common';
import { SettingsController } from './controllers/setting-options.controller';
import { SettingOptionsService } from './services/setting-options.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SettingsOptions, SettingsOptionsSchema } from './schemas/setting-options.schema';
import { AuthModule } from 'src/auth/auth.module';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { OrganizationSubSettings, OrganizationSubSettingsSchema } from 'src/organizationSettings/schemas/organization-sub-settings.schema';

@Module({
  imports: [
    AuthModule,
    MongooseModule.forFeature([
      { name: SettingsOptions.name, schema: SettingsOptionsSchema },
      { name: SettingsOptions.name, schema: SettingsOptionsSchema },
      {name: OrganizationSubSettings.name, schema: OrganizationSubSettingsSchema}
    ], DATABASE_PRIMARY_CONNECTION_NAME)
  ],
  controllers: [SettingsController],
  providers: [SettingOptionsService],
  exports: [SettingOptionsService]
})
export class SettingsOptionsModule {}
