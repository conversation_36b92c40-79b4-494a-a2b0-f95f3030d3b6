import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreatePaymentCredentialDto {
  @ApiProperty({ description: 'Razorpay/Stripe key ID' })
  @IsString()
  @IsNotEmpty()
  keyId: string;

  @ApiProperty({ description: 'Secret key for the payment gateway' })
  @IsString()
  @IsNotEmpty()
  keySecret: string;

  @ApiProperty({ description: 'Webhook secret (optional)', required: false })
  @IsOptional()
  @IsString()
  webhookSecret?: string;

  @ApiProperty({ enum: ['razorpay', 'stripe', 'cashfree'], description: 'Payment gateway being used' })
  @IsString()
  @IsEnum(['razorpay', 'stripe', 'cashfree'])
  paymentGateway: string;

}
