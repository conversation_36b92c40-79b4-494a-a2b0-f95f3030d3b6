import { IsString, IsBoolean, Is<PERSON>otEmpty, <PERSON><PERSON><PERSON>al, Is<PERSON><PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { PaginationDto } from 'src/utils/dto/pagination.dto';

class SettingStatus {
  @ApiProperty({
    description: 'Status',
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  status: boolean;

  @ApiProperty({
    description: 'Settings ID',
    example: "643232323232323232323232"
  })
  @IsString()
  @IsNotEmpty()
  settingKey: string;
}


export class GrantSettingOptionsDto extends PaginationDto {
  @ApiProperty({
    description: 'Organization ID',
    example: "643232323232323232323232"
  })
  @IsString()
  @IsNotEmpty()
  organizationId: string;
}

export class GrantOneSettingOptionsDto {
  // @ApiProperty({
  //   description: 'Organization ID',
  //   example: "643232323232323232323232"
  // })
  // @IsString()
  // @IsNotEmpty()
  // organizationId: string;

  @ApiProperty({
    description: '',
    type: SettingStatus
  })
  @IsNotEmpty()
  settingKey: SettingStatus;

  @ApiProperty({
    description: '',
    type: [SettingStatus]
  })
  @IsOptional()
  subSettingsKey: SettingStatus[];
}
