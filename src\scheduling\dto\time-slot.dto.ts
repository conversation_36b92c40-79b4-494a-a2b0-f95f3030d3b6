import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";


export class RecurringTimeSlotsDTO {
    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from?: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to?: string;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    durationInMinutes?: number;

    @ApiProperty({
        description: "Class Capacity",
        example: 20,
    })
    @IsOptional()
    @Type(() => Number) // Specify the type of the property
    @IsNumber()
    @Min(1, { message: "Class Capacity must be at least 1" })
    classCapacity?: number;
}
