import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";


export class RoleSearchDto {
    @ApiProperty({
        description: "Search query to filter results.",
        example: "Search",
        required: false,
    })
    @IsOptional()
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsOptional()
    limit?: number = 20;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsOptional()
    page?: number = 1;
}
