import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import {
    ThrottlerGuard,
    ThrottlerModule,
    ThrottlerModuleOptions,
} from '@nestjs/throttler';
import {
    AppJsonBodyParserMiddleware,
    AppRawBodyParserMiddleware,
    AppTextBodyParserMiddleware,
    AppUrlencodedBodyParserMiddleware,
} from 'src/app/middlewares/app.body-parser.middleware';
import { AppCorsMiddleware } from 'src/app/middlewares/app.cors.middleware';
import { AppHelmetMiddleware } from 'src/app/middlewares/app.helmet.middleware';
import { AppResponseTimeMiddleware } from 'src/app/middlewares/app.response-time.middleware';
import { AppUrlVersionMiddleware } from 'src/app/middlewares/app.url-version.middleware';
import { AppRequestIdMiddleware } from 'src/app/middlewares/app.request-id.middleware';
import { SessionModule } from 'src/session/session.module';
import { CachingModule } from 'src/common/caching/caching.module';
import { AppCustomLanguageMiddleware } from './middlewares/app.custom-language.middleware';
import { MongoExceptionFilter } from 'src/utils/filters/mongo-exception.filter';
import { SessionActivityMiddleware } from 'src/session/middlewares/session-activity.middleware';

@Module({
    controllers: [],
    exports: [],
    providers: [
        {
            provide: APP_GUARD,
            useClass: ThrottlerGuard,
        },

        {
            provide: APP_FILTER,
            useClass: MongoExceptionFilter,
        },
    ],
    imports: [
        SessionModule,
        CachingModule,
        ThrottlerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (config: ConfigService): ThrottlerModuleOptions => ({
                throttlers: [
                    {
                        ttl: config.get<number>('middleware.throttle.ttl'),
                        limit: config.get<number>('middleware.throttle.limit'),
                    },
                ],
            }),
        }),
    ],
})
export class AppMiddlewareModule implements NestModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer
            .apply(
                SessionActivityMiddleware,
                AppRequestIdMiddleware,
                // AppCorsMiddleware, // CORS should come before Helmet
                AppHelmetMiddleware,
                AppJsonBodyParserMiddleware,
                AppTextBodyParserMiddleware,
                AppRawBodyParserMiddleware,
                AppUrlencodedBodyParserMiddleware,
                AppUrlVersionMiddleware,
                AppResponseTimeMiddleware,
                AppCustomLanguageMiddleware
            )
            // .forRoutes('*wildcard');
            .forRoutes('*');
    }
}
