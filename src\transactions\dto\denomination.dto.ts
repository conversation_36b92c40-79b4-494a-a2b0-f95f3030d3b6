import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty, Min } from 'class-validator';

export class DenominationDto {
    
    @ApiProperty({ 
        description: 'Value of the denomination',
        example: 100,
        required: true,
    })
    @IsNumber()
    @IsNotEmpty()
    @Min(1)
    value: number;

    @ApiProperty({ 
        description: 'Count of the denomination', 
        example: 5,
        required: true,
    })
    @IsNumber()
    @IsNotEmpty()
    @Min(1, { message: 'Count must be greater than 0' })
    count: number;

}