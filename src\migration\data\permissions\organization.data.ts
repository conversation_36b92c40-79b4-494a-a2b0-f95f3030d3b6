
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const organizationPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Organization Service Write",
        type: ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_WRITE,
        description: 'Grant organization service write access',
        isDelegated: true,
    },
    {
        name: "Organization Service Read",
        type: ENUM_PERMISSION_TYPE.OR<PERSON>NIZATION_SERVICE_READ,
        description: 'Grant organization service read access',
        isDelegated: true,
    },
    {
        name: "Organization Service Update",
        type: ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE,
        description: 'Grant organization service update access',
        isDelegated: true,
    },
    {
        name: "Organization Service Delete",
        type: ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_DELETE,
        description: 'Grant organization service delete access',
        isDelegated: true,
    },
];

