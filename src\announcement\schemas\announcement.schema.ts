import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { Document } from 'mongoose';


@Schema({ timestamps: true })
export class Announcement {

    @Prop({ type: Types.ObjectId, ref: 'User', required: true })
    organizationId: string;

    @Prop({ required: true, trim: true })
    title: string;
    
    @Prop({ trim: true, default: "" })
    subtitle?: string;
    
    @Prop({ required: true, trim: true })
    description: string;

    @Prop({ required: true, trim: true, })
    imageUrl: string;

    @Prop({ required: true, default: true })
    isActive: boolean;

    @Prop({ type: Date, index: true, required: false })
    deletedAt: Date;
}

export type AnnouncementDocument = Announcement & Document;
export const AnnouncementSchema = SchemaFactory.createForClass(Announcement);
