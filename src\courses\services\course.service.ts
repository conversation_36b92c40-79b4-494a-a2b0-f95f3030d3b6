import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { ClientSession, Model, Types } from "mongoose";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { Services } from "src/organization/schemas/services.schema";
import { Purchase } from "src/users/schemas/purchased-packages.schema";
import { Enrollment } from "../schemas/enrollment.schema";
import { Room } from "src/room/schema/room.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { MailService } from "src/mail/services/mail.service";
import { Clients } from "src/users/schemas/clients.schema";
import { UpdateCourseStatusDto, CustomerListDto, CustomerListForSchedulingDto, EnrollSchedulingDto, CheckedInDto, CourseListDto } from "src/courses/dto/courses.dto";
import { CreateSchedulingDataDto, SchedulingListDto, UpdateSchedulingDataDto } from "src/courses/dto/scheduling.dto";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import moment from "moment";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { OrganizationSubSettings } from "src/organizationSettings/schemas/organization-sub-settings.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { User } from "src/users/schemas/user.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { ScheduleDayDto } from "src/scheduling/dto/time-slots.schedule.dto";
import { IScheduleDates } from "src/scheduling/interfaces/class.schedule.interface";
import { RecurringTimeSlotsDTO } from "src/scheduling/dto/time-slot.dto";
import { DayIndexToWeekMap } from "src/utils/enums/days-of-week.enum";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { ActiveTimeFrame } from "src/utils/schemas/active-time-frame.schema";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { CreateRecurringSchedulingDataDto } from "src/scheduling/dto/scheduling.dto";
import { DateTime } from "luxon";
import { GetScheduleDetailsDto } from "src/scheduling/dto/get-schedules-recurring.dto";
import { addDays, isAfter } from "date-fns";
import { CancelSchedulesByIdsDto } from "src/scheduling/dto/CancelRecurringScheduleDto";
import { WaitTimeGatewayService } from "src/wait-time/gateway/wait-time-gateway.service";

@Injectable()
export class CourseService {
    constructor(
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(StaffAvailability.name) private StaffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(OrganizationSubSettings.name) private OrganizationSubSettingModel: Model<OrganizationSubSettings>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(User.name) private UserModel: Model<User>,
        private readonly transactionService: TransactionService,
        private readonly mailService: MailService,
        private waitTimeGatewayService: WaitTimeGatewayService,

    ) { }

    private validateSchedule(schedule: ScheduleDayDto, pricingDuration: number) {
        for (const [day, slots] of Object.entries(schedule)) {
            // Sort by 'from' time to make overlap checking reliable
            const sortedSlots = slots.sort((a, b) => a.from.localeCompare(b.from));

            for (let i = 0; i < sortedSlots.length; i++) {
                const slot = sortedSlots[i];

                const fromTime = new Date(`1970-01-01T${slot.from}:00Z`);
                const toTime = new Date(`1970-01-01T${slot.to}:00Z`);
                const diffInMinutes = (toTime.getTime() - fromTime.getTime()) / 60000;

                // 1. Check if time range and duration match
                if (diffInMinutes !== slot.durationInMinutes) {
                    throw new BadRequestException(
                        `Mismatch in time range and duration on ${day.toUpperCase()}: From ${slot.from} to ${slot.to} is ${diffInMinutes} mins, but durationInMinutes is ${slot.durationInMinutes} mins.`
                    );
                }

                // 2. Check duration is at least pricingDuration
                if (slot.durationInMinutes < pricingDuration) {
                    throw new BadRequestException(
                        `On ${day.toUpperCase()}, slot ${slot.from}–${slot.to}: Duration must be at least ${pricingDuration} minutes`
                    );
                }

                // 3. Check duration is a multiple of pricingDuration
                if (slot.durationInMinutes % pricingDuration !== 0) {
                    throw new BadRequestException(
                        `On ${day.toUpperCase()}, slot ${slot.from}–${slot.to}: Duration must be a multiple of ${pricingDuration} minutes`
                    );
                }

                // 4. Check for overlaps with next slot
                if (i < sortedSlots.length - 1) {
                    const next = sortedSlots[i + 1];
                    if (this.isTimeOverlap(slot.from, slot.to, next.from, next.to)) {
                        throw new BadRequestException(
                            `On ${day.toUpperCase()}, overlapping slots between ${slot.from}–${slot.to} and ${next.from}–${next.to}`
                        );
                    }
                }
            }
        }
    }

    private isTimeOverlap(from1: string, to1: string, from2: string, to2: string): boolean {
        const timeToMinutes = (time: string) => {
            const [h, m] = time.split(':').map(Number);
            return h * 60 + m;
        };

        const start1 = timeToMinutes(from1);
        const end1 = timeToMinutes(to1);
        const start2 = timeToMinutes(from2);
        const end2 = timeToMinutes(to2);

        return Math.max(start1, start2) < Math.min(end1, end2);
    }

    async buildScheduleDates(startDate: Date, endDate: Date, schedule: ScheduleDayDto, appointmentDuration?: number): Promise<IScheduleDates[]> {
        const result: IScheduleDates[] = [];
        const start = moment(startDate).utc(true).startOf('day').toDate();
        const end = moment(endDate).utc(true).endOf('day').toDate();

        while (start <= end) {
            const dayKey = start.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
            const timeSlots: RecurringTimeSlotsDTO[] = schedule[dayKey];
            if (timeSlots && timeSlots.length > 0) {
                for (const slot of timeSlots) {
                    const slotDateTime = moment(start).add(slot.from, 'hours');
                    const nowIST = moment();
                    if (slotDateTime.isBefore(nowIST)) {
                        throw new BadRequestException("Class cannot start in the past.");
                    }

                    const fromTime = moment(slot.from, 'HH:mm');
                    const toTime = moment(slot.to, 'HH:mm');
                    const calculatedDuration = toTime.diff(fromTime, 'minutes');

                    let requestedSessions = 0
                    if (appointmentDuration) {
                        requestedSessions = calculatedDuration / (appointmentDuration);
                        const remainder = calculatedDuration % (appointmentDuration);
                        if (remainder !== 0) {
                            throw new BadRequestException(`Duration must be a multiple of ${appointmentDuration} minutes on ${DayIndexToWeekMap[start.getUTCDay()]}`);
                        };
                    }

                    result.push({
                        date: new Date(start),
                        day: dayKey,
                        from: slot.from,
                        to: slot.to,
                        duration: slot.durationInMinutes,
                        sessions: requestedSessions,
                        capacity: slot?.classCapacity,
                    });
                }
            }
            start.setUTCDate(start.getUTCDate() + 1);
        }

        return result;
    }

    public async validateFacilityAvailability(startDate: Date, endDate: Date, schedule: ScheduleDayDto, facilitiesId: string) {
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(endDate.setHours(23, 59, 59, 999));
        const days = {
            mon: "Monday",
            tue: "Tuesday",
            wed: "Wednesday",
            thu: "Thursday",
            fri: "Friday",
            sat: "Saturday",
            sun: "Sunday",
        };
        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilitiesId),
            $or: [
                {
                    type: "unavailable",
                    fromDate: { $lte: endOfDay },
                    endDate: { $gte: startOfDay },
                },
                {
                    type: "available",
                },
            ],
        }).sort({ createdAt: 1 });

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        const facilityAvailability = facilityRecords.find((record) => record.type === "available");

        if (unavailableRecord) {
            for (const unavailableSlot of unavailableRecord.time) {
                for (const dayOfWeek in schedule) {
                    const requestedSlots = schedule[dayOfWeek];
                    for (const requestedSlot of requestedSlots) {
                        if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                            throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${days[dayOfWeek.toLowerCase()]} `);
                        }
                    }
                }
            }
        }

        if (!facilityAvailability) {
            throw new BadRequestException("No available working hours found for the facility.");
        }

        const facilityWorkingHours = facilityAvailability.workingHours;

        for (const dayOfWeek in schedule) {
            const requestedSlots = schedule[dayOfWeek];
            const availableSlots = facilityWorkingHours[dayOfWeek];

            if (!availableSlots && requestedSlots.length > 0) {
                throw new BadRequestException(`Facility is not available on ${days[dayOfWeek.toLowerCase()]}.`);
            }

            for (const requestedSlot of requestedSlots) {
                const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

                if (!slotIsWithinAvailability) {
                    const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                    throw new BadRequestException(
                        `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${days[dayOfWeek.toLowerCase()]} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                    );
                }
            }
        }
        return true;
    }

    public async validateRoomAvailabilityRecurring(selectedRoom: any, startDate: Date, endDate: Date, requestedScheduleDateList: IScheduleDates[], scheduledIds?: any) {
        // if (!selectedRoom) return false;
        if (!selectedRoom) throw new BadRequestException(`Selected room is not available`);

        const currentRoomStrength = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduledIds ?? [] },
                    roomId: selectedRoom._id,
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    date: {
                        $gte: startDate,
                        $lte: endDate
                    },
                },
            },
            {
                $group: {
                    _id: {
                        date: "$date",
                        from: "$from",
                        to: "$to",
                    },
                    from: { $first: "$from" },
                    to: { $first: "$to" },
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    _id: 0,
                    from: 1,
                    to: 1,
                    date: { $dateToString: { format: "%Y-%m-%d", date: "$_id.date" } },
                    count: 1,
                },
            },
        ]);

        const existingBookingMap = new Map<Date, { date: Date; from: string; to: string; count: number }[]>();
        for (const booking of currentRoomStrength) {
            const date = new Date(booking.date);
            if (!existingBookingMap.has(date)) {
                existingBookingMap.set(date, []);
            }
            existingBookingMap.get(date)!.push(booking);
        }

        for (const requestedSlot of requestedScheduleDateList) {
            const date = requestedSlot.date;
            const from = requestedSlot.from;
            const to = requestedSlot.to;

            const existingBookingsForDate = existingBookingMap.get(date) || [];
            for (const existingBooking of existingBookingsForDate) {
                const existingFrom = existingBooking.from;
                const existingTo = existingBooking.to;
                const count = existingBooking.count;

                if (
                    existingBooking &&
                    ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                        (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                        (from <= existingFrom && to >= existingTo)) &&
                    count >= selectedRoom.capacity
                ) {
                    // New booking completely overlaps existing booking
                    throw new BadRequestException(`Selected room is at full capacity at the selected time at ${date.toDateString().split(" ")[0]} from ${existingFrom} to ${existingTo}`);
                }
            }
        }
    }

    async getAvailableRoomsMultiple(facilityId: string, classType: ClassType, serviceCategory: string, schedules: IScheduleDates[], scheduleIds: string[], session?: ClientSession) {
        const dates = [...new Set(schedules.map(item => item.date))];
        const from = [...new Set(schedules.map(item => item.from))];

        const rooms = await this.RoomModel.find({
            facilityId: new Types.ObjectId(facilityId),
            classType: { $in: [classType] },
            serviceCategory: { $in: [new Types.ObjectId(serviceCategory)] },
            status: true,
        }).lean();

        const roomIds = rooms.map(room => room._id);

        const scheduleCounts = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduleIds ?? [] },
                    roomId: { $in: roomIds },
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    date: { $in: dates },
                    from: { $in: from },
                },
            },
            {
                $group: {
                    _id: {
                        roomId: "$roomId",
                        date: "$date",
                        from: "$from",
                    },
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    _id: 0,
                    roomId: "$_id.roomId",
                    date: "$_id.date",
                    from: "$_id.from",
                    count: 1,
                },
            },
        ]);

        const countMap = new Map<string, number>();
        for (const { roomId, date, from, count } of scheduleCounts) {
            const key = `${roomId.toHexString()}-${moment(date).utc(true).startOf('day').toDate().getTime()}-${from}`;
            countMap.set(key, count);
        }

        const roomsOnDates = new Map<number, Map<string, IDatabaseObjectId[]>>();

        for (const { date, from } of schedules) {
            const dateKey = moment(date).utc(true).startOf('day').toDate().getTime();
            if (!roomsOnDates.has(dateKey)) {
                roomsOnDates.set(dateKey, new Map());
            }
            const fromMap = roomsOnDates.get(dateKey)!;
            if (!fromMap.has(from)) {
                fromMap.set(from, []);
            }
            const availableList = fromMap.get(from)!;
            for (const { _id, capacity } of rooms) {
                const lookupKey = `${_id.toHexString()}-${dateKey}-${from}`;
                const booked = countMap.get(lookupKey) ?? 0;

                if (booked < capacity) {
                    availableList.push(_id);
                }
            }
        }

        return roomsOnDates;
    }

    async checkForExistingScheduleConflict(startDate: Date, endDate: Date, requestedSchedule: IScheduleDates[], scheduleIds?: string[] | IDatabaseObjectId[], trainerId?: string) {
        const existingBooking = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduleIds ?? [] },
                    trainerId: new Types.ObjectId(trainerId),
                    classType: { $in: [ClassType.COURSES] },
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    date: {
                        $gte: moment(startDate).utc(true).toDate(),
                        $lte: moment(endDate).utc(true).toDate(),
                    }
                }
            },
            {
                $group: {
                    _id: {
                        date: "$date",
                        from: "$from",
                    },
                    from: { $first: "$from" },
                    to: { $first: "$to" },
                },
            },
            {
                $project: {
                    _id: 0,
                    from: 1,
                    to: 1,
                    date: { $dateToString: { format: "%Y-%m-%d", date: "$_id.date" } },
                },
            },
            { $sort: { date: 1, from: 1 } },
        ]);

        const requestedScheduleMap = new Map<string, IScheduleDates[]>();
        for (const slot of requestedSchedule) {
            const date = slot.date;
            const day = slot.day;
            if (!requestedScheduleMap.has(moment(date).utc(true).startOf('day').toDate().toISOString())) {
                requestedScheduleMap.set(moment(date).utc(true).startOf('day').toDate().toISOString(), []);
            }
            requestedScheduleMap.get(moment(date).utc(true).startOf('day').toDate().toISOString())!.push(slot);
        }
        const existingBookingMap = new Map<string, Omit<IScheduleDates, "day">[]>();
        for (const booking of existingBooking) {
            const date = booking.date;
            if (!existingBookingMap.has(moment(date).utc(true).startOf('day').toDate().toISOString())) {
                existingBookingMap.set(moment(date).utc(true).startOf('day').toDate().toISOString(), []);
            }
            existingBookingMap.get(moment(date).utc(true).startOf('day').toDate().toISOString())!.push(booking);
        }

        for (const [date, slots] of requestedScheduleMap) {
            const requestedSlots = slots;
            for (const requestedSlot of requestedSlots) {
                const from = requestedSlot.from;
                const to = requestedSlot.to;

                const existingBookingsForDate = existingBookingMap.get(moment(date).utc(true).startOf('day').toDate().toISOString()) || [];
                for (const existingBooking of existingBookingsForDate) {
                    const existingFrom = existingBooking.from;
                    const existingTo = existingBooking.to;

                    if (
                        existingBooking &&
                        ((from >= existingFrom && from < existingTo) ||
                            (to > existingFrom && to <= existingTo) ||
                            (from <= existingFrom && to >= existingTo))
                    ) {
                        throw new BadRequestException(`Schedule already exists from ${existingFrom} to ${existingTo} on ${moment(date).format("DD MMM")}`);
                    }
                }
            }
        }
        return true;
    }

    async validateStaffAvailabilityMultiple(serviceCategoryId: string, appointmentType: string, trainerId: string, startDate: Date, endDate: Date, schedule: ScheduleDayDto, facilityId: string, scheduleId?: string, session?: ClientSession): Promise<boolean> {
        const staff = await this.UserModel.findById(trainerId, { firstName: 1, lastName: 1 });
        if (!staff) throw new BadRequestException("Staff not found");

        const staffName = `${staff.firstName} ${staff.lastName || ""}`.trim();

        const availabilities = await this.StaffAvailabilityModel.find({
            userId: trainerId,
            facilityId,
            date: { $gte: startDate, $lte: endDate },
        });

        // if (!availabilities.length) {
        //     throw new BadRequestException(`${staffName} is not available for the selected dates`);
        // }
        const payRates = await this.PayRateModel.find({
            userId: trainerId,
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        });

        if (!payRates.length) {
            throw new BadRequestException(`${staffName} does not have pay rate configured for the selected service and subtype`);
        }
        const allPayRateIds = payRates.map((payRate) => new Types.ObjectId(payRate._id));


        const availabilityMap = new Map<string, typeof availabilities[0]>();
        for (const entry of availabilities) {
            availabilityMap.set(entry.date.toISOString().split("T")[0], entry);
        }
        let current = new Date(startDate);

        while (current <= endDate) {
            const isoDate = current.toISOString().split("T")[0];
            const dayOfWeek = current.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
            const requestedSlots = schedule[dayOfWeek];

            if (requestedSlots?.length) {
                if (availabilityMap.has(isoDate)) {
                    const dayAvailability = availabilityMap.get(isoDate)!;

                    for (const slot of requestedSlots) {

                        for (const unSlot of dayAvailability.timeSlots) {
                            if (unSlot.availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE && this.isTimeOverlapping(slot, unSlot)) {
                                throw new BadRequestException(`${staffName} is unavailable on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                            }
                        }

                        // const validSlots = dayAvailability.timeSlots.filter((s) =>
                        //     s.availabilityStatus === StaffAvailabilityEnum.AVAILABLE &&
                        //     s.classType === ClassType.PERSONAL_APPOINTMENT &&
                        //     this.isTimeWithin(slot, s) &&
                        //     Array.isArray(s.payRateIds) &&
                        //     s.payRateIds.some((slotPayRateId) =>
                        //         allPayRateIds.some((validId) => validId.equals(slotPayRateId))
                        //     )
                        // );

                        // if (!validSlots.length) {
                        //     throw new BadRequestException(`${staffName} is not available on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                        // }

                        const existingSchedules = await this.SchedulingModel.countDocuments({
                            _id: { $ne: scheduleId },
                            trainerId,
                            facilityId,
                            date: current,
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                            $or: [{ from: { $gte: slot.from, $lt: slot.to } }, { to: { $gt: slot.from, $lte: slot.to } }],
                        });

                        if (existingSchedules > 0) {
                            throw new BadRequestException(`${staffName} already has an appointment on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                        }
                    }
                }
                else {
                    throw new BadRequestException(`${staffName} is not available on ${current.toDateString()} for the requested slots`);
                }
            }


            current.setUTCDate(current.getUTCDate() + 1);
        }

        return true;
    }

    private isTimeWithin(inner: { from: string; to: string }, outer: { from: string; to: string }) {
        const innerFrom = new Date(`1970-01-01T${inner.from}:00Z`).getTime();
        const innerTo = new Date(`1970-01-01T${inner.to}:00Z`).getTime();
        const outerFrom = new Date(`1970-01-01T${outer.from}:00Z`).getTime();
        const outerTo = new Date(`1970-01-01T${outer.to}:00Z`).getTime();

        return innerFrom >= outerFrom && innerTo <= outerTo;
    }
    async validateStaffSpecialization(
        trainerId: string,
        serviceCategoryId: any,
        appointmentType: any,
    ): Promise<boolean> {
        const payRates = await this.PayRateModel.exists({
            userId: trainerId,
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        }).sort({ updatedAt: -1 });

        if (!payRates) {
            throw new BadRequestException("Trainer is not specialized in the selected service");
        }

        return true;
    }

    private validateRecurringScheduleAgainstActiveTimeFrames(
        activeTimeFrames: ActiveTimeFrame[],
        schedule: ScheduleDayDto
    ): void {
        if (!activeTimeFrames || activeTimeFrames.length === 0) return;

        const shortToFullDayMap: Record<string, string> = {
            mon: 'monday',
            tue: 'tuesday',
            wed: 'wednesday',
            thu: 'thursday',
            fri: 'friday',
            sat: 'saturday',
            sun: 'sunday',
        };

        for (const [shortDay, slots] of Object.entries(schedule)) {
            if (!slots || !Array.isArray(slots)) continue;

            const fullDay = shortToFullDayMap[shortDay];
            if (!fullDay) continue;

            const activeForDay = activeTimeFrames.filter((tf) => tf.dayOfWeek?.toLowerCase() === fullDay);

            for (const slot of slots) {
                const from = slot.from!;
                const to = slot.to!;

                const isValid = activeForDay.some(tf => {
                    return tf.startTime <= from && to <= tf.endTime;
                });

                if (!isValid) {
                    throw new BadRequestException(
                        `Course cannot be scheduled on ${fullDay.charAt(0).toUpperCase() + fullDay.slice(1)} between ${from} - ${to} as it falls outside the allowed active time frames.`
                    );

                }
            }
        }
    }


    private async fetchNecessaryDocuments(body: CreateRecurringSchedulingDataDto) {
        return await Promise.all([
            this.FacilityModel.findOne({ _id: new Types.ObjectId(body.facilityId), organizationId: new Types.ObjectId(body.organizationId) }),
            this.ServiceModel.findById(body.serviceCategory),
        ]);
    }
    async getOrganizationId(user: IUserDocument): Promise<any> {
        const { role } = user;
        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staff = await this.StaffProfileModel.findOne({ userId: user._id }).exec();
                organizationId = staff?.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user._id }).exec();
                organizationId = client?.organizationId;
                break;
            default:
                throw new BadRequestException("Access Denied");
        }
        if (organizationId) {
            const organizationSubSettingData = await this.OrganizationSubSettingModel.findOne({ organizationId, key: "subsettings_class_setup_courses" });
            if (!(organizationSubSettingData && organizationSubSettingData?.isEnabled)) {
                throw new BadRequestException("policy.error.abilityForbidden");
            }
            return organizationId;
        }
        throw new BadRequestException("Access Denied");
    }

    async getCourseList(organizationId: IDatabaseObjectId, courseListDto: CourseListDto): Promise<any> {
        try {
            const pageSize = courseListDto.pageSize ?? 10;
            const page = courseListDto.page ?? 1;
            const skip = pageSize * (page - 1);

            const filter: any = {
                organizationId,
                isBundledPricing: { $ne: true },
                "services.type": "courses",
            };

            if (courseListDto.search) filter.name = { $regex: courseListDto.search, $options: "i" };
            if (courseListDto.isFeatured !== undefined) filter.isFeatured = courseListDto.isFeatured;

            const aggregationPipeline: any[] = [
                { $match: filter },
                {
                    $lookup: {
                        from: "services",
                        localField: "services.serviceCategory",
                        foreignField: "_id",
                        as: "serviceCategories",
                    },
                },
                {
                    $unwind: {
                        path: "$serviceCategories",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        expiredDate: {
                            $switch: {
                                branches: [
                                    {
                                        case: { $eq: ["$durationUnit", "days"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 24 * 60 * 60 * 1000] }] },
                                    },
                                    {
                                        case: { $eq: ["$durationUnit", "months"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 30 * 24 * 60 * 60 * 1000] }] },
                                    },
                                    {
                                        case: { $eq: ["$durationUnit", "years"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 365 * 24 * 60 * 60 * 1000] }] },
                                    },
                                ],
                                default: "$createdAt",
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        isExpired: { $lt: ["$expiredDate", new Date()] },
                    },
                },
                {
                    $addFields: {
                        sortOrder: {
                            $cond: {
                                if: { $eq: ["$isExpired", true] },
                                then: 2, // Active first
                                else: {
                                    $cond: {
                                        if: { $eq: ["$isActive", true] },
                                        then: 0,
                                        else: 1,
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $sort: { sortOrder: 1, createdAt: -1 },
                },
            ];

            const dateFilter: any = {};
            if (courseListDto.startDate) {
                dateFilter.expiredDate = { $gte: new Date(courseListDto.startDate) };
            }
            if (courseListDto.endDate) {
                dateFilter.createdAt = { $lte: new Date(courseListDto.endDate) };
            }
            if (Object.keys(dateFilter).length > 0) {
                aggregationPipeline.push({ $match: dateFilter });
            }

            aggregationPipeline.push({
                $facet: {
                    count: [{ $count: "total" }],
                    list: [
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $project: {
                                name: 1,
                                appointmentTypes: {
                                    $filter: {
                                        input: "$serviceCategories.appointmentType",
                                        as: "appointment",
                                        cond: {
                                            $in: ["$$appointment._id", "$services.appointmentType"],
                                        },
                                    },
                                },
                                serviceCategoryId: "$serviceCategories._id",
                                serviceCategoryName: "$serviceCategories.name",
                                startDate: "$createdAt",
                                endDate: "$expiredDate",
                                isActive: 1,
                                isExpired: 1,
                                createdAt: 1,
                            },
                        },
                    ],
                },
            });

            const result = await this.PricingModel.aggregate(aggregationPipeline);
            const count = result[0].count[0]?.total ?? 0;
            const list = result[0].list;

            return { list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getCourseDetail(courseId: string): Promise<any> {
        const pricingData: any = await this.PricingModel.findOne({ _id: courseId }).lean();
        if (!pricingData) throw new BadRequestException("Course not found");
        const { services } = pricingData;
        const serviceCategoryDetails = await this.ServiceModel.findOne({ _id: services.serviceCategory }, "name appointmentType");
        if (!serviceCategoryDetails) throw new BadRequestException("Service category details not found");
        const multiplierMap: Record<string, number> = {
            days: 24 * 60 * 60 * 1000,
            months: 30 * 24 * 60 * 60 * 1000,
            years: 365 * 24 * 60 * 60 * 1000,
        };

        const expiredDate = new Date(new Date(pricingData.createdAt).getTime() + pricingData.expiredInDays * multiplierMap[pricingData.durationUnit]);

        const data = {
            _id: pricingData._id,
            name: pricingData.name,
            image: pricingData.image,
            description: pricingData.description,
            isFeatured: pricingData.isFeatured,
            isActive: pricingData.isActive,
            price: pricingData.price,
            basePrice: pricingData?.basePrice,
            gstAmount: pricingData?.gstAmount,
            finalPrice: pricingData?.finalPrice,
            isInclusiveofGst: pricingData?.isInclusiveofGst ? pricingData?.isInclusiveofGst : false,
            expiredDate,
            createdAt: pricingData.createdAt,
            serviceCategoryName: serviceCategoryDetails.name,
            serviceCategoryId: services.serviceCategory,
            appointmentTypes: [],
            sessionCount: services.sessionCount === Infinity ? "Infinity" : services.sessionCount,
            sessionType: services.sessionType,
            sessionPerDay: services.sessionPerDay,
        };
        for (const appointmentId of services.appointmentType || []) {
            const match = serviceCategoryDetails.appointmentType.find((a: any) => a._id.toString() === appointmentId.toString());
            if (match) data.appointmentTypes.push({ _id: appointmentId, name: match.name });
        }
        return data;
    }

    async getRecurringCourseDetail(
        user: Record<string, any>,
        courseId: string,
        dto: GetScheduleDetailsDto
    ): Promise<any> {
        const { role } = user;
        let { dateRange, startDate, endDate, markType } = dto;

        startDate = new Date(startDate);
        if (endDate) endDate = new Date(endDate);

        if (!dateRange) {
            dateRange = DateRange.SINGLE;
        }

        const query: any = { _id: new Types.ObjectId(courseId) };

        const roleWithOrgAccess = [
            ENUM_ROLE_TYPE.ORGANIZATION,
            ENUM_ROLE_TYPE.WEB_MASTER,
            ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
            ENUM_ROLE_TYPE.TRAINER
        ];

        if (!roleWithOrgAccess.includes(role.type)) {
            throw new BadRequestException("Access denied");
        }

        if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = user._id;
        } else {
            const staffDetails = await this.StaffProfileModel.findOne(
                { userId: user._id },
                { organizationId: 1 }
            );
            if (!staffDetails) {
                throw new BadRequestException("Access denied, Staff does not have access");
            }
            query.organizationId = staffDetails.organizationId;
        }
        const schedule: any = await this.SchedulingModel.findOne(query);

        if (!schedule) throw new NotFoundException("Schedule not found");
        const weekDays: Record<string, any> = {
            mon: {},
            tue: {},
            wed: {},
            thu: {},
            fri: {},
            sat: {},
            sun: {}
        };

        if (dateRange === DateRange.MULTIPLE) {
            const recurringQuery: any = {
                facilityId: new Types.ObjectId(schedule.facilityId),
                packageId: new Types.ObjectId(schedule.packageId),
                classType: ClassType.COURSES,
                serviceCategoryId: new Types.ObjectId(schedule.serviceCategoryId),
                subTypeId: new Types.ObjectId(schedule.subTypeId),
                scheduleStatus: schedule.scheduleStatus,
                date: { $gte: startDate }
            };

            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) {
                    throw new BadRequestException("End date is required for CUSTOM mark type.");
                }
                const maxAllowedEndDate = addDays(startDate, 6);
                if (isAfter(endDate, maxAllowedEndDate)) {
                    endDate = maxAllowedEndDate;
                }
            } else if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = addDays(startDate, 6);
            }

            recurringQuery.date.$lte = endDate;

            const schedules = await this.SchedulingModel.find(recurringQuery).sort({ date: 1 });
            if (!schedules.length) {
                return [];
            }

            const schedulesMap = new Map(
                schedules.map(item => [new Date(item.date).toDateString(), item])
            );
            const seenDays = new Set<string>();

            let current = new Date(startDate);
            while (current <= endDate) {
                const dayName = current.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
                if (!seenDays.has(dayName)) {
                    const dateKey = current.toDateString();
                    const item = schedulesMap.get(dateKey);
                    if (item) {
                        weekDays[dayName] = {
                            from: item.from,
                            to: item.to,
                            durationInMinutes: item.duration,
                            classCapacity: item.classCapacity
                        };
                    }
                    seenDays.add(dayName);
                }
                current.setDate(current.getDate() + 1);
            }
        }

        const pricingData: any = await this.PricingModel.findOne({ _id: schedule.packageId }).lean();
        if (!pricingData) throw new BadRequestException("Course not found");

        const { services } = pricingData;
        const serviceCategoryDetails = await this.ServiceModel.findOne(
            { _id: services.serviceCategory },
            "name appointmentType"
        );
        if (!serviceCategoryDetails) {
            throw new BadRequestException("Service category details not found");
        }

        const metaData = {
            serviceCategoryName: serviceCategoryDetails.name,
            serviceCategoryId: services.serviceCategory,
            appointmentTypes: [],
            sessionType: services.sessionType
        };

        for (const appointmentId of services.appointmentType || []) {
            const match = serviceCategoryDetails.appointmentType?.find(
                (a: any) => a._id.toString() === appointmentId.toString()
            );
            if (match) {
                metaData.appointmentTypes.push({ _id: appointmentId, name: match.name });
            }
        }

        if (dateRange === DateRange.MULTIPLE) {
            const plainSchedule = schedule.toObject();

            const {
                from: _from,
                to: _to,
                date: _date,
                classCapacity: _classCapacity,
                duration: _duration,
                notes: _notes,
                room: _room,
                ...restDetail
            } = plainSchedule;

            return {
                ...restDetail,
                ...metaData,
                startDate,
                endDate,
                slots: weekDays
            };
        }

        if (dateRange === DateRange.SINGLE) {
            return {
                ...schedule.toObject?.() ?? schedule,
                ...metaData
            };
        }
    }

    async getCourseDetailPublic(courseId: string): Promise<any> {
        const pricingData: any = await this.PricingModel.findOne({ _id: courseId }).lean();
        if (!pricingData) throw new BadRequestException("Course not found");
        const { services } = pricingData;
        const serviceCategoryDetails = await this.ServiceModel.findOne({ _id: services.serviceCategory }, "name appointmentType");
        if (!serviceCategoryDetails) throw new BadRequestException("Service category details not found");
        const multiplierMap: Record<string, number> = {
            days: 24 * 60 * 60 * 1000,
            months: 30 * 24 * 60 * 60 * 1000,
            years: 365 * 24 * 60 * 60 * 1000,
        };

        const expiredDate = new Date(new Date(pricingData.createdAt).getTime() + pricingData.expiredInDays * multiplierMap[pricingData.durationUnit]);

        const data = {
            _id: pricingData._id,
            name: pricingData.name,
            image: pricingData.image,
            description: pricingData.description,
            isFeatured: pricingData.isFeatured,
            isActive: pricingData.isActive,
            price: pricingData.price,
            basePrice: pricingData?.basePrice,
            gstAmount: pricingData?.gstAmount,
            finalPrice: pricingData?.finalPrice,
            isInclusiveofGst: pricingData?.isInclusiveofGst ? pricingData?.isInclusiveofGst : false,
            expiredDate,
            createdAt: pricingData.createdAt,
            serviceCategoryName: serviceCategoryDetails.name,
            serviceCategoryId: services.serviceCategory,
            appointmentTypes: [],
            sessionCount: services.sessionCount === Infinity ? "Infinity" : services.sessionCount,
            sessionType: services.sessionType,
            sessionPerDay: services.sessionPerDay,
        };
        for (const appointmentId of services.appointmentType || []) {
            const match = serviceCategoryDetails.appointmentType.find((a: any) => a._id.toString() === appointmentId.toString());
            if (match) data.appointmentTypes.push({ _id: appointmentId, name: match.name });
        }
        return data;
    }

    async getCustomerList(user: IUserDocument, customerListDto: CustomerListDto): Promise<any> {
        try {
            const { role } = user;
            const { pageSize = 10, page = 1, courseId, schedulingIds, search } = customerListDto;
            const skip = pageSize * (page - 1);
            let facilityIds = [];
            if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
                const facilities = await this.FacilityModel.find({ organizationId: user._id }, { _id: 1 }).lean();
                facilityIds = facilities.map((facility) => facility._id);
            } else {
                const staff = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1 }).lean();
                facilityIds = staff?.facilityId || [];
            }

            if (!facilityIds.length) return { list: [], count: 0 };
            const enrollmentMatchConditions: any = [{ $eq: ["$userId", "$$userId"] }, { $eq: ["$packageId", "$$packageId"] }];
            if (schedulingIds && schedulingIds.length) {
                enrollmentMatchConditions.push({ $in: ["$schedulingId", schedulingIds.map((id) => new Types.ObjectId(id))] });
            }

            const searchQuery = search
                ? {
                    $or: [{ name: { $regex: search, $options: "i" } }, { firstName: { $regex: search, $options: "i" } }, { lastName: { $regex: search, $options: "i" } }],
                }
                : {};

            const result = await this.PurchaseModel.aggregate([
                { $match: { facilityId: { $in: facilityIds }, packageId: new Types.ObjectId(courseId) } },
                { $unwind: "$consumers" },
                {
                    $group: {
                        _id: "$consumers",
                        userId: {
                            $first: "$consumers",
                        },
                        packageId: {
                            $first: "$packageId",
                        },
                        startDate: {
                            $first: "$startDate",
                        },
                        purchaseDate: {
                            $first: "$purchaseDate",
                        },
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [
                            {
                                $match: {
                                    isActive: true,
                                    ...searchQuery,
                                },
                            },
                        ],
                    },
                },
                { $unwind: "$userData" },
                {
                    $lookup: {
                        from: "clients",
                        localField: "userId",
                        foreignField: "userId",
                        as: "clientData",
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "enrollments",
                        let: { userId: "$userId", packageId: "$packageId" },
                        as: "enrollmentData",
                        pipeline: [{ $match: { $expr: { $and: enrollmentMatchConditions } } }],
                    },
                },
                ...(schedulingIds && schedulingIds.length
                    ? [
                        {
                            $match: {
                                $expr: {
                                    $ne: [
                                        {
                                            $size: "$enrollmentData",
                                        },
                                        schedulingIds.length,
                                    ],
                                },
                            },
                        },
                    ]
                    : []),
                {
                    $addFields: {
                        totalEnrollments: { $size: "$enrollmentData" },
                        totalCheckedIns: {
                            $size: {
                                $filter: {
                                    input: "$enrollmentData",
                                    as: "enrollment",
                                    cond: { $eq: ["$$enrollment.isCheckedIn", true] },
                                },
                            },
                        },
                        ...(schedulingIds &&
                            schedulingIds.length && {
                            enrolled: { $gt: [{ $size: "$enrollmentData" }, 0] },
                            checkedIn: { $gt: ["$totalCheckedIns", 0] },
                        }),
                    },
                },
                {
                    $group: {
                        _id: "$userId",
                        name: { $first: { $concat: ["$userData.firstName", " ", "$userData.lastName"] } },
                        mobile: { $first: "$userData.mobile" },
                        startDate: {
                            $first: "$startDate",
                        },
                        purchaseDate: {
                            $first: "$purchaseDate",
                        },
                        clientId: { $first: "$clientData.clientId" },
                        totalCheckedIns: { $first: "$totalCheckedIns" },
                        totalEnrollments: { $first: "$totalEnrollments" },
                        ...(schedulingIds && schedulingIds.length ? { enrolled: { $first: "$enrolled" }, checkedIn: { $first: "$checkedIn" } } : {}),
                    },
                },
                ...(schedulingIds && schedulingIds.length ? [{ $match: { totalEnrollments: { $gt: 0 } } }] : []),
                {
                    $facet: {
                        count: [{ $count: "total" }],
                        list: [
                            {
                                $sort: {
                                    createdAt: -1,
                                    name: 1,
                                },
                            },
                            { $skip: skip },
                            { $limit: pageSize },
                        ],
                    },
                },
            ]);

            return {
                list: result[0]?.list || [],
                count: result[0]?.count?.[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    // async getCustomerListV0(user: IUserDocument, customerListDto: CustomerListDto): Promise<any> {
    //     try {
    //         const { role } = user;
    //         const { pageSize = 10, page = 1, courseId, schedulingId, search } = customerListDto;
    //         const skip = pageSize * (page - 1);
    //         let facilityIds = [];
    //         if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
    //             const facilities = await this.FacilityModel.find({ organizationId: user._id }, { _id: 1 }).lean();
    //             facilityIds = facilities.map((facility) => facility._id);
    //         } else {
    //             const staff = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1 }).lean();
    //             facilityIds = staff?.facilityId || [];
    //         }

    //         if (!facilityIds.length) return { list: [], count: 0 };
    //         const enrollmentMatchConditions: any = [{ $eq: ["$userId", "$$userId"] }, { $eq: ["$packageId", "$$packageId"] }];
    //         if (schedulingId) enrollmentMatchConditions.push({ $eq: ["$schedulingId", new Types.ObjectId(schedulingId)] });

    //         const searchQuery = search ?
    //             {
    //                 $or: [
    //                     { name: { $regex: search, $options: "i" } },
    //                     { firstName: { $regex: search, $options: "i" } },
    //                     { lastName: { $regex: search, $options: "i" } },
    //                 ],
    //             }
    //             : {};

    //         const result = await this.PurchaseModel.aggregate([
    //             { $match: { facilityId: { $in: facilityIds }, packageId: new Types.ObjectId(courseId) } },
    //             { $unwind: "$consumers" },
    //             {
    //                 $group: {
    //                     _id: "$consumers",
    //                     userId: {
    //                         $first: "$consumers"
    //                     },
    //                     packageId: {
    //                         $first: "$packageId"
    //                     },
    //                 }
    //             },
    //             {
    //                 $lookup: {
    //                     from: "users",
    //                     localField: "userId",
    //                     foreignField: "_id",
    //                     as: "userData",
    //                     pipeline: [
    //                         {
    //                             $match: {
    //                                 isActive: true,
    //                                 ...searchQuery
    //                             },
    //                         },
    //                     ],
    //                 },
    //             },
    //             { $unwind: "$userData" },
    //             {
    //                 $lookup: {
    //                     from: "clients",
    //                     localField: "userId",
    //                     foreignField: "userId",
    //                     as: "clientData",
    //                 },
    //             },
    //             { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
    //             {
    //                 $lookup: {
    //                     from: "enrollments",
    //                     let: { userId: "$userId", packageId: "$packageId" },
    //                     as: "enrollmentData",
    //                     pipeline: [{ $match: { $expr: { $and: enrollmentMatchConditions } } }],
    //                 },
    //             },
    //             // {
    //             //     $match: {
    //             //         enrollmentData: { $eq: [] },
    //             //     },
    //             // },
    //             {
    //                 $addFields: {
    //                     totalEnrollments: { $size: "$enrollmentData" },
    //                     totalCheckedIns: {
    //                         $size: {
    //                             $filter: {
    //                                 input: "$enrollmentData",
    //                                 as: "enrollment",
    //                                 cond: { $eq: ["$$enrollment.isCheckedIn", true] },
    //                             },
    //                         },
    //                     },
    //                     ...(schedulingId && {
    //                         enrolled: { $gt: [{ $size: "$enrollmentData" }, 0] },
    //                         checkedIn: { $gt: ["$totalCheckedIns", 0] },
    //                     }),
    //                 },
    //             },
    //             {
    //                 $group: {
    //                     _id: "$userId",
    //                     name: { $first: { $concat: ["$userData.firstName", " ", "$userData.lastName"] } },
    //                     mobile: { $first: "$userData.mobile" },
    //                     date: { $first: "$createdAt" },
    //                     clientId: { $first: "$clientData.clientId" },
    //                     totalCheckedIns: { $first: "$totalCheckedIns" },
    //                     totalEnrollments: { $first: "$totalEnrollments" },
    //                     ...(schedulingId ? { enrolled: { $first: "$enrolled" }, checkedIn: { $first: "$checkedIn" } } : {}),
    //                 },
    //             },
    //             ...(schedulingId ? [{ $match: { totalEnrollments: { $gt: 0 } } }] : []),
    //             {
    //                 $facet: {
    //                     count: [{ $count: "total" }],
    //                     list: [
    //                         {
    //                             $sort: {
    //                                 createdAt: -1,
    //                                 name: 1,
    //                             }
    //                         },
    //                         { $skip: skip },
    //                         { $limit: pageSize }
    //                     ],
    //                 },
    //             },
    //         ]);

    //         return {
    //             list: result[0]?.list || [],
    //             count: result[0]?.count?.[0]?.total || 0,
    //         };
    //     } catch (error) {
    //         throw new BadRequestException(error.message);
    //     }
    // }

    async getCustomerListForScheduling(user, customerListForSchedulingDto: CustomerListForSchedulingDto): Promise<any> {
        try {
            const pageSize = customerListForSchedulingDto.pageSize ?? 10;
            const page = customerListForSchedulingDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const searchQuery = customerListForSchedulingDto.search ? { name: { $regex: customerListForSchedulingDto.search, $options: "i" } } : {};

            const result = await this.EnrollmentModel.aggregate([
                {
                    $match: {
                        schedulingId: new Types.ObjectId(customerListForSchedulingDto.schedulingId),
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                    },
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "clients",
                        let: { userId: "$userId" },
                        pipeline: [{ $match: { $expr: { $eq: ["$userId", "$$userId"] } } }],
                        as: "clientData",
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        _id: "$_id",
                        userId: "$userId",
                        name: "$userData.name",
                        enrollmentId: "$userData.name",
                        mobile: "$userData.mobile",
                        date: "$createdAt",
                        clientId: "$clientData.clientId",
                        isCheckedIn: "$isCheckedIn",
                        totalCheckedIns: { $sum: { $cond: [{ $eq: ["$isCheckedIn", true] }, 1, 0] } },
                        totalEnrollments: { $sum: 1 },
                    },
                },
                { $match: searchQuery },
                {
                    $facet: {
                        count: [{ $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            const count = result[0].count.length > 0 ? result[0].count[0].total : 0;
            return { list: result[0].list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async updateCourseStatus(updateCourseDto: UpdateCourseStatusDto, organizationId: any): Promise<any> {
        try {
            const course = await this.PricingModel.findOne({
                _id: updateCourseDto.courseId,
                organizationId,
            });
            if (!course) throw new NotFoundException(`Course not found`);

            if (updateCourseDto.isActive !== undefined && updateCourseDto.isActive !== course.isActive) course.isActive = updateCourseDto.isActive;

            if (updateCourseDto.isFeatured !== undefined && updateCourseDto.isFeatured !== course.isFeatured) course.isFeatured = updateCourseDto.isFeatured;

            if (updateCourseDto.description !== undefined && updateCourseDto.description !== course.description) course.description = updateCourseDto.description;

            if (updateCourseDto.image !== undefined && updateCourseDto.image !== course.image) course.image = updateCourseDto.image;
            if (updateCourseDto.name !== undefined && updateCourseDto.name !== course.name) course.name = updateCourseDto.name;
            await course.save();

            return {
                message: "Course details updated successfully",
                course,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    private async validatePackageOwnership(organizationId: Types.ObjectId, packageId: Types.ObjectId) {
        const pricingData: any = await this.PricingModel.findOne({ _id: packageId, organizationId }).select("_id expiredInDays durationUnit createdAt services");
        if (!pricingData) throw new BadRequestException("Access Denied: Course does not belong to this organization.");

        const startDate = new Date(pricingData.createdAt);
        const endDate = new Date(startDate);

        switch (pricingData.durationUnit) {
            case DurationUnit.DAYS:
                endDate.setDate(endDate.getDate() + pricingData.expiredInDays);
                break;
            case DurationUnit.MONTHS:
                endDate.setMonth(endDate.getMonth() + pricingData.expiredInDays);
                break;
            case DurationUnit.YEARS:
                endDate.setFullYear(endDate.getFullYear() + pricingData.expiredInDays);
                break;
        }
        if (new Date() > endDate) throw new BadRequestException("Access Denied: Course has expired.");

        return pricingData;
    }

    private isTimeOverlapping(slot1: { from: string; to: string }, slot2: { from: String; to: String }): boolean {
        return slot1.from < slot2.to && slot1.to > slot2.from;
    }

    private isTimeWithinAvailableSlot(requestedSlot: { from: string; to: string }, availableSlot: { from: string; to: string }): boolean {
        return (
            new Date(`1970-01-01T${requestedSlot.from}:00`) >= new Date(`1970-01-01T${availableSlot.from}:00`) &&
            new Date(`1970-01-01T${requestedSlot.to}:00`) <= new Date(`1970-01-01T${availableSlot.to}:00`)
        );
    }

    private async validateFacilityValidation(createSchedulingDto: CreateSchedulingDataDto, organizationId: Types.ObjectId) {
        const { facilityId, from, to, date } = createSchedulingDto;
        const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).select("_id");
        if (!facility) throw new BadRequestException("Access Denied: Facility does not belong to this organization.");
        const dayOfWeek = new Date(date).toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const parsedDate = new Date(date);
        const startOfDay = new Date(parsedDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(parsedDate.setHours(23, 59, 59, 999));
        const facilityRecords = await this.FacilityAvailabilityModel.find(
            {
                facilityId: new Types.ObjectId(facilityId),
                organizationId,
                $or: [{ type: "unavailable", fromDate: { $lte: endOfDay }, endDate: { $gte: startOfDay } }, { type: "available" }],
            },
            { type: 1, time: 1, workingHours: 1 },
        );

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        if (unavailableRecord) {
            for (const slot of unavailableRecord.time) {
                if (this.isTimeOverlapping({ from, to }, slot)) {
                    throw new BadRequestException(`Facility is unavailable from ${slot.from} to ${slot.to}`);
                }
            }
        }

        const facilityAvailability = facilityRecords.find((record) => record.type === "available");
        if (!facilityAvailability) throw new BadRequestException("No available working hours found for the facility.");

        const availableSlots = facilityAvailability.workingHours?.[dayOfWeek];
        if (!availableSlots) throw new BadRequestException(`Facility is not available on ${dayOfWeek}.`);

        if (!availableSlots.some((slot) => this.isTimeWithinAvailableSlot({ from, to }, slot))) {
            const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
            throw new BadRequestException(`Requested slot from ${from} to ${to} is outside facility's available hours. Working hours: ${availableSlotsText}.`);
        }
    }

    private async validateStaffValidation(createSchedulingDto: any, organizationId: Types.ObjectId, courseData: any) {
        const { facilityId, from, to, date, trainerId, schedulingId } = createSchedulingDto;
        const staff = await this.StaffProfileModel.findOne({ userId: trainerId, facilityId }).select("_id");
        if (!staff) throw new BadRequestException("Staff is not part of this facility.");
        // const parsedDate = new Date(date);
        // const startOfDay = new Date(parsedDate.setHours(0, 0, 0, 0));
        // const endOfDay = new Date(parsedDate.setHours(23, 59, 59, 999));
        // const staffRecord = await this.StaffAvailabilityModel.findOne(
        //     { userId: trainerId, facilityId: new Types.ObjectId(facilityId), organizationId, date: { $gte: startOfDay, $lte: endOfDay } },
        //     { timeSlots: 1 },
        // );

        // if (!staffRecord) throw new BadRequestException("No available working hours found for the staff.");

        // const availableSlots = staffRecord.timeSlots;
        // if (!availableSlots.length) throw new BadRequestException(`Staff is not available on this date.`);

        // if (!availableSlots.some((slot) => this.isTimeWithinAvailableSlot({ from, to }, slot))) {
        //     const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
        //     throw new BadRequestException(`Requested slot from ${from} to ${to} is outside staff's available hours. Working hours: ${availableSlotsText}.`);
        // }

        const isTrainerBusy = await this.SchedulingModel.countDocuments({
            _id: { $ne: schedulingId },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            trainerId,
            date,
            $and: [{ from: { $lt: to } }, { to: { $gt: from } }],
        });
        if (isTrainerBusy) {
            const user = await this.UserModel.findOne({ _id: trainerId }).exec();
            throw new BadRequestException(`${user.name || `${user.firstName?.trim()} ${user.lastName?.trim() || ""}`} is unavailable due to an existing appointment`);
        }

        // const reqFromDate = new Date(`1970-01-01T${from}:00`);
        // const reqToDate = new Date(`1970-01-01T${to}:00`);
        // const payRateIds = new Set<string>();
        // for (let slot of availableSlots) {
        //     const fromDate = new Date(`1970-01-01T${slot.from}:00`);
        //     const toDate = new Date(`1970-01-01T${slot.to}:00`);
        //     if (fromDate <= reqFromDate && toDate >= reqToDate) {
        //         if (Array.isArray(slot.payRateIds)) {
        //             slot.payRateIds.forEach((id: string) => payRateIds.add(id));
        //         }
        //     }
        // }

        // if (payRateIds.size === 0) throw new BadRequestException("Staff not available at the given time");
        // const payRates = await this.PayRateModel.find({
        //     _id: { $in: Array.from(payRateIds) },
        //     serviceCategory: courseData?.services?.serviceCategory,
        //     appointmentType: { $in: courseData?.services?.appointmentType },
        // });
        const payRateData = await this.PayRateModel.findOne({
            userId: trainerId,
            serviceCategory: courseData?.services?.serviceCategory,
            appointmentType: { $in: courseData?.services?.appointmentType },
        });
        if (!payRateData) throw new BadRequestException("Staff not available for the given sub type");
    }

    private async validateRoomAvailability(createSchedulingDto: any) {
        const { roomId, from, to, date } = createSchedulingDto;

        const selectedRoom = await this.RoomModel.findOne({ _id: roomId, status: true }).select("capacity");
        if (!selectedRoom) throw new BadRequestException("Invalid room.");
        const obj: any = {
            roomId,
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            date,
            from: { $lt: to },
            to: { $gt: from },
        };

        if (createSchedulingDto.schedulingId) obj._id = { $ne: createSchedulingDto.schedulingId };
        const currentRoomStrength = await this.SchedulingModel.countDocuments(obj);

        if (currentRoomStrength >= selectedRoom.capacity) {
            throw new BadRequestException(`Selected room is at full capacity from ${from} to ${to}.`);
        }
    }

    private isFromLessThanTo({ from, to }: { from: string; to: string }): boolean {
        return new Date(`1970-01-01T${from}:00`) < new Date(`1970-01-01T${to}:00`);
    }

    async createCourseScheduling(createSchedulingDto: CreateSchedulingDataDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            if (!this.isFromLessThanTo({ from: createSchedulingDto.from, to: createSchedulingDto.to })) throw new BadRequestException("Invalid time range.");
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const courseId = new Types.ObjectId(createSchedulingDto.courseId);
            const roomId = createSchedulingDto.roomId ? new Types.ObjectId(createSchedulingDto.roomId) : null;

            await this.validateFacilityValidation(createSchedulingDto, organizationId);
            const courseData = await this.validatePackageOwnership(organizationId, courseId);
            await this.validateStaffValidation(createSchedulingDto, organizationId, courseData);
            if (roomId) await this.validateRoomAvailability(createSchedulingDto);

            const newSchedule = new this.SchedulingModel({
                organizationId,
                scheduledBy: user._id,
                facilityId: createSchedulingDto.facilityId,
                packageId: courseId,
                classType: createSchedulingDto.classType,
                subTypeId: createSchedulingDto.subType,
                trainerId: createSchedulingDto.trainerId,
                serviceCategoryId: createSchedulingDto.serviceCategory,
                roomId,
                dateRange: createSchedulingDto.dateRange,
                date: new Date(createSchedulingDto.date),
                from: createSchedulingDto.from,
                to: createSchedulingDto.to,
                duration: createSchedulingDto.duration,
                sessions: 1,
                notes: createSchedulingDto.notes,
                classCapacity: createSchedulingDto.classCapacity,
            });
            await newSchedule.save({ session });

            const distinctUsers = await this.PurchaseModel.aggregate([
                { $match: { packageId: courseId } },
                { $unwind: "$consumers" },
                { $group: { _id: "$consumers", purchaseId: { $first: "$_id" } } },
                { $limit: createSchedulingDto.classCapacity },
            ]);
            const newEnrollments = distinctUsers.map((doc) => ({
                schedulingId: newSchedule._id,
                userId: doc._id,
                packageId: courseId,
                purchaseId: doc.purchaseId,
            }));
            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments, { session });

            await this.transactionService.commitTransaction(session);
            await this.sendScheduleCreationEmail(newSchedule, user);

            return { message: "Schedule created successfully", data: newSchedule };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async createCourseRecurringScheduling(createSchedulingDto: CreateRecurringSchedulingDataDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let { organizationId, facilityId, trainerId, courseId, classType, subType, serviceCategory, roomId, dateRange, notes, markType, startDate, endDate, schedule } = createSchedulingDto;


            const availableRooms = new Map<number, Map<string, IDatabaseObjectId[]>>();
            const userOrganizationId = new Types.ObjectId(await this.getOrganizationId(user));

            if (userOrganizationId.toString() !== organizationId.toString()) {
                throw new BadRequestException("Access Denied: Organization ID mismatch.");
            }

            const istNow = DateTime.now().setZone("Asia/Kolkata");
            const istDateStartOfDay = istNow.startOf("day").toJSDate();
            istDateStartOfDay.setHours(0, 0, 0, 0);
            istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            if (!(startDate >= istDateStartOfDay)) {
                throw new BadRequestException("Start date should be greater than or equal to current date");
            }

            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) throw new BadRequestException("End date is required for custom date range");

            } else if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + 1);
            }

            if (!startDate || !endDate) {
                throw new BadRequestException("Start and End Date are required for multiple update");
            }

            for (const day in schedule) {
                if (Array.isArray(schedule[day])) {
                    schedule[day] = schedule[day].filter(slot => slot.from && slot.to && slot.durationInMinutes != null);
                } else {
                    schedule[day] = [];
                }
            }

            const [checkFacility, servicePackage] = await this.fetchNecessaryDocuments(createSchedulingDto);
            if (!checkFacility) throw new NotFoundException({ message: "Selected facility does not exist" });
            if (!servicePackage) throw new NotFoundException({ message: "Selected service package does not exist" });

            const serviceSubType = servicePackage.appointmentType.find(item => item["_id"].toString() === subType.toString());
            if (!serviceSubType || !serviceSubType.isActive) throw new NotFoundException({ message: "Selected Sub Type does not exist or may be Inactive" });

            const courseDetails = await this.PricingModel.findOne({
                _id: courseId,
                "services.type": classType,
                $or: [
                    {
                        $and: [
                            { "services.serviceCategory": new Types.ObjectId(serviceCategory) },
                            { "services.appointmentType": new Types.ObjectId(subType) },
                        ]
                    },
                    {
                        $and: [
                            {
                                "services.relationShip": {
                                    $elemMatch: {
                                        serviceCategory: new Types.ObjectId(serviceCategory),
                                        subTypeIds: new Types.ObjectId(subType)
                                    }
                                }
                            }
                        ]
                    }
                ]
            }).session(session);

            if (!courseDetails) throw new BadRequestException("Invalid course details.");

            const pricingDuration = serviceSubType.durationInMinutes;
            this.validateSchedule(schedule, pricingDuration);

            const checkStaff = await this.StaffProfileModel.findOne({
                userId: trainerId,
                facilityId: facilityId,
                organizationId: organizationId
            }).session(session);

            if (!checkStaff) throw new BadRequestException("Staff is not part of this facility");

            const scheduleDateList = await this.buildScheduleDates(startDate, endDate, schedule, pricingDuration);
            if (!scheduleDateList.length) throw new BadRequestException("No schedule dates found");

            await this.validateFacilityAvailability(startDate, endDate, schedule, facilityId);
            await this.validateStaffAvailabilityMultiple(serviceCategory, subType, trainerId, startDate, endDate, schedule, facilityId);
            await this.checkForExistingScheduleConflict(startDate, endDate, scheduleDateList, [], trainerId);

            if (roomId && dateRange === DateRange.SINGLE) {
                const room = await this.RoomModel.findOne({ _id: roomId, facilityId, isActive: { $ne: false } }).session(session);
                await this.validateRoomAvailabilityRecurring(room, startDate, endDate, scheduleDateList);
            } else if (dateRange === DateRange.MULTIPLE) {
                const available = await this.getAvailableRoomsMultiple(facilityId, ClassType.CLASSES, serviceCategory, scheduleDateList, [], session);
                if (!available.size) throw new BadRequestException("No available rooms found");
                availableRooms.clear();
                available.forEach((value, key) => availableRooms.set(key, value));
            }

            const schedules = scheduleDateList.map(item => {
                const date = item.date;
                const from = item.from;
                const to = item.to;
                const duration = item.duration;
                const requestedSessions = item.sessions;
                const capacity = item?.capacity;

                const room = roomId ?? availableRooms.get(moment(date).utc(true).startOf('day').toDate().getTime())?.get(from)?.[0];
                if (!room) throw new BadRequestException(`No available rooms found for ${moment(date).format("DD MMM")} from ${from} to ${to}`);

                return new this.SchedulingModel({
                    organizationId,
                    scheduledBy: user._id,
                    facilityId,
                    packageId: courseId,
                    classType: ClassType.COURSES,
                    subTypeId: subType,
                    serviceCategoryId: serviceCategory,
                    trainerId,
                    roomId: room,
                    dateRange,
                    date: moment(date).utc(true).startOf('day').toDate(),
                    from,
                    to,
                    duration,
                    sessions: requestedSessions,
                    notes,
                    classCapacity: capacity,
                });
            });

            await this.SchedulingModel.insertMany(schedules, { session });
            // const consumersWithPurchase = await this.PurchaseModel.aggregate([
            //     {
            //         $match: {
            //             packageId: new Types.ObjectId(courseId),
            //             organizationId: new Types.ObjectId(organizationId),
            //             facilityId: new Types.ObjectId(facilityId),
            //             isActive: { $ne: false },
            //             paymentStatus: { $in: [PaymentStatus.PENDING, PaymentStatus.COMPLETED] },
            //             isExchanged: { $ne: true }
            //         }
            //     },
            //     { $unwind: "$consumers" },
            //     {
            //         $project: {
            //             consumerId: "$consumers",
            //             purchaseId: "$_id",
            //             sessionType: 1,
            //             totalSessions: 1,
            //             sessionConsumed: 1,
            //             sessionPerDay: 1,
            //             dayPassLimit: 1,
            //             suspensions: 1,
            //             startDate: 1,
            //             endDate: 1,
            //         }
            //     }
            // ]);

            // const eligibleUsers = [];

            // for (const user of consumersWithPurchase) {
            //     try {
            //         const isEligible = await this.checkUserEligibilityForCourse({
            //             userId: user.consumerId,
            //             purchase: user,
            //             courseDate,      // date of the class
            //             fromTime,        // e.g. "10:00"
            //             toTime,          // e.g. "11:00"
            //             requestedSessions: 1,
            //             session,         // optional session (for transactions)
            //         });

            //         if (isEligible) {
            //             eligibleUsers.push({
            //                 userId: user.consumerId,
            //                 purchaseId: user.purchaseId
            //             });

            //             if (eligibleUsers.length >= classCapacity) break; // respect capacity limit
            //         }
            //     } catch (err) {
            //         // Skip ineligible users silently
            //     }
            // }

            // const distinctUsers = await this.PurchaseModel.aggregate([
            //     { $match: { packageId: courseId } },
            //     { $unwind: "$consumers" },
            //     { $group: { _id: "$consumers", purchaseId: { $first: "$_id" } } },
            //     { $limit: createSchedulingDto.classCapacity },
            // ]);
            // const newEnrollments = distinctUsers.map((doc) => ({
            //     schedulingId: newSchedule._id,
            //     userId: doc._id,
            //     packageId: courseId,
            //     purchaseId: doc.purchaseId,
            // }));
            // if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments, { session });
            await this.transactionService.commitTransaction(session);

            return { message: "Schedule created successfully" };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateCourseScheduling(updateSchedulingDto: UpdateSchedulingDataDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            if (!this.isFromLessThanTo({ from: updateSchedulingDto.from, to: updateSchedulingDto.to })) throw new BadRequestException("Invalid time range.");

            if (updateSchedulingDto.classCapacity <= 0) throw new BadRequestException("Class capacity must be greater than 0.");

            const scheduleDate = new Date(updateSchedulingDto.date);
            if (isNaN(scheduleDate.getTime())) {
                throw new BadRequestException("Invalid date format.");
            }

            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const courseId = new Types.ObjectId(updateSchedulingDto.courseId);
            const roomId = updateSchedulingDto.roomId ? new Types.ObjectId(updateSchedulingDto.roomId) : null;

            const existingEnrollments = await this.EnrollmentModel.find({ schedulingId: updateSchedulingDto.schedulingId });
            if (existingEnrollments.length > updateSchedulingDto.classCapacity)
                throw new BadRequestException(`Cannot update scheduling: existing enrollments exceed new class capacity.`);

            await this.validateFacilityValidation(updateSchedulingDto, organizationId);
            const courseData = await this.validatePackageOwnership(organizationId, courseId);
            await this.validateStaffValidation(updateSchedulingDto, organizationId, courseData);
            if (roomId) await this.validateRoomAvailability(updateSchedulingDto);

            const schedulingDetails = await this.SchedulingModel.findOneAndUpdate(
                { _id: updateSchedulingDto.schedulingId },
                {
                    $set: {
                        subTypeId: updateSchedulingDto.subType,
                        serviceCategoryId: updateSchedulingDto.serviceCategory,
                        roomId,
                        dateRange: updateSchedulingDto.dateRange,
                        date: scheduleDate,
                        from: updateSchedulingDto.from,
                        trainerId: updateSchedulingDto.trainerId,
                        to: updateSchedulingDto.to,
                        duration: updateSchedulingDto.duration,
                        notes: updateSchedulingDto.notes,
                        classCapacity: updateSchedulingDto.classCapacity,
                    },
                },
                { new: true, session },
            );

            if (!schedulingDetails) throw new BadRequestException("Scheduling does not exist.");

            let newEnrollments = [];

            const availableSlots = updateSchedulingDto.classCapacity - existingEnrollments.length;
            if (availableSlots > 0) {
                const existingUserIds = existingEnrollments.map((enrollment) => enrollment.userId);

                const distinctUsers = await this.PurchaseModel.aggregate([
                    { $match: { packageId: courseId, userId: { $nin: existingUserIds } } },
                    { $unwind: "$consumers" },
                    { $group: { _id: "$consumers", purchaseId: { $first: "$_id" } } },
                    { $limit: availableSlots },
                ]).session(session);

                newEnrollments = distinctUsers.map((doc) => ({
                    schedulingId: updateSchedulingDto.schedulingId,
                    userId: doc._id,
                    packageId: courseId,
                    purchaseId: doc.purchaseId,
                }));

                if (newEnrollments.length) {
                    await this.EnrollmentModel.insertMany(newEnrollments, { session });
                }
            }
            await this.transactionService.commitTransaction(session);
            return {
                message: "Schedule updated successfully",
                data: schedulingDetails,
                newlyEnrolledCount: newEnrollments.length,
            };
        } catch (error) {
            console.error("Error updating scheduling:", error);
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getSchedulingList(user: IUserDocument, schedulingListDto: SchedulingListDto): Promise<any> {
        try {
            const pageSize = schedulingListDto.pageSize ?? 10;
            const page = schedulingListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const filter: any = { classType: ClassType.COURSES };
            if (schedulingListDto.status) filter.scheduleStatus = schedulingListDto.status;
            if (schedulingListDto.courseId) filter.packageId = new Types.ObjectId(schedulingListDto.courseId);
            if (schedulingListDto.roomId) filter.roomId = new Types.ObjectId(schedulingListDto.roomId);
            if (schedulingListDto.facilityId) filter.facilityId = new Types.ObjectId(schedulingListDto.facilityId);
            if (schedulingListDto.startDate && schedulingListDto.endDate) {
                filter["date"] = {
                    $gte: schedulingListDto.startDate,
                    $lte: schedulingListDto.endDate,
                };
            }

            const result = await this.SchedulingModel.aggregate([
                { $match: filter },
                {
                    $lookup: {
                        from: "enrollments",
                        let: {
                            schedulingId: "$_id",
                            userId: new Types.ObjectId(user._id),
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [...(user.role.type === ENUM_ROLE_TYPE.USER ? [{ $eq: ["$userId", "$$userId"] }] : []), { $eq: ["$schedulingId", "$$schedulingId"] }],
                                    },
                                },
                            },
                        ],
                        as: "enrollmentData",
                    },
                },
                ...(user.role.type === ENUM_ROLE_TYPE.USER
                    ? [
                        {
                            $match: {
                                $expr: {
                                    $gt: [
                                        {
                                            $size: "$enrollmentData",
                                        },
                                        0,
                                    ],
                                },
                            },
                        },
                    ]
                    : []),
                { $sort: { date: 1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerData",
                    },
                },
                {
                    $unwind: {
                        path: "$trainerData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facilityData",
                    },
                },
                {
                    $unwind: {
                        path: "$facilityData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "rooms",
                        localField: "roomId",
                        foreignField: "_id",
                        as: "roomData",
                    },
                },
                {
                    $unwind: {
                        path: "$roomData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategoryId",
                        foreignField: "_id",
                        as: "serviceCategoryData",
                    },
                },
                {
                    $unwind: {
                        path: "$serviceCategoryData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "pricingDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$pricingDetails",
                    },
                },
                {
                    $addFields: {
                        totalEnrollments: { $size: "$enrollmentData" },
                        totalCheckedIns: {
                            $size: {
                                $filter: {
                                    input: "$enrollmentData",
                                    as: "enrollment",
                                    cond: { $eq: ["$$enrollment.isCheckedIn", true] },
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        from: "$from",
                        to: "$to",
                        date: "$date",
                        scheduleStatus: "$scheduleStatus",
                        trainerName: {
                            $cond: {
                                if: "$trainerData.name",
                                then: "$trainerData.name",
                                else: {
                                    $concat: ["$trainerData.firstName", " ", "$trainerData.lastName"],
                                },
                            },
                        },
                        trainerId: "$trainerData._id",
                        facilityName: "$facilityData.facilityName",
                        facilityId: "$facilityData._id",
                        roomName: "$roomData.roomName",
                        roomId: "$roomData._id",
                        totalCheckedIns: "$totalCheckedIns",
                        totalEnrollments: "$totalEnrollments",
                        serviceCategoryId: "$serviceCategoryId",
                        subTypeId: "$subTypeId",
                        serviceCategoryName: "$serviceCategoryData.name",
                        subTypeData: {
                            $filter: {
                                input: "$serviceCategoryData.appointmentType",
                                as: "appointment",
                                cond: {
                                    $eq: ["$$appointment._id", "$subTypeId"],
                                },
                            },
                        },
                        courseId: "$packageId",
                        courseName: "$pricingDetails.name",
                    },
                },
                {
                    $facet: {
                        count: [{ $match: { scheduleStatus: { $ne: ScheduleStatusType.CANCELED } } }, { $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            const count = result[0].count.length > 0 ? result[0].count[0].total : 0;

            return { list: result[0].list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getSchedulingListPublic(schedulingListDto: SchedulingListDto): Promise<any> {
        try {
            const pageSize = schedulingListDto.pageSize ?? 10;
            const page = schedulingListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const filter: any = { classType: ClassType.COURSES };
            if (schedulingListDto.status) filter.scheduleStatus = schedulingListDto.status;
            if (schedulingListDto.courseId) filter.packageId = new Types.ObjectId(schedulingListDto.courseId);
            if (schedulingListDto.roomId) filter.roomId = new Types.ObjectId(schedulingListDto.roomId);
            if (schedulingListDto.facilityId) filter.facilityId = new Types.ObjectId(schedulingListDto.facilityId);
            if (schedulingListDto.startDate && schedulingListDto.endDate) {
                filter["date"] = {
                    $gte: schedulingListDto.startDate,
                    $lte: schedulingListDto.endDate,
                };
            }

            const result = await this.SchedulingModel.aggregate([
                { $match: filter },
                {
                    $lookup: {
                        from: "enrollments",
                        let: {
                            schedulingId: "$_id",
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $eq: ["$schedulingId", "$$schedulingId"],
                                    },
                                },
                            },
                        ],
                        as: "enrollmentData",
                    },
                },
                // {
                //     $match: {
                //         $expr: {
                //             $gt: [
                //                 { $size: "$enrollmentData" },
                //                 0,
                //             ],
                //         },
                //     },
                // },
                { $sort: { date: 1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerData",
                    },
                },
                {
                    $unwind: {
                        path: "$trainerData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facilityData",
                    },
                },
                {
                    $unwind: {
                        path: "$facilityData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "rooms",
                        localField: "roomId",
                        foreignField: "_id",
                        as: "roomData",
                    },
                },
                {
                    $unwind: {
                        path: "$roomData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategoryId",
                        foreignField: "_id",
                        as: "serviceCategoryData",
                    },
                },
                {
                    $unwind: {
                        path: "$serviceCategoryData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "pricingDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$pricingDetails",
                    },
                },
                {
                    $addFields: {
                        totalEnrollments: { $size: "$enrollmentData" },
                        totalCheckedIns: {
                            $size: {
                                $filter: {
                                    input: "$enrollmentData",
                                    as: "enrollment",
                                    cond: { $eq: ["$$enrollment.isCheckedIn", true] },
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        from: "$from",
                        to: "$to",
                        date: "$date",
                        scheduleStatus: "$scheduleStatus",
                        trainerName: {
                            $cond: {
                                if: "$trainerData.name",
                                then: "$trainerData.name",
                                else: {
                                    $concat: ["$trainerData.firstName", " ", "$trainerData.lastName"],
                                },
                            },
                        },
                        trainerId: "$trainerData._id",
                        facilityName: "$facilityData.facilityName",
                        facilityId: "$facilityData._id",
                        roomName: "$roomData.roomName",
                        roomId: "$roomData._id",
                        totalCheckedIns: "$totalCheckedIns",
                        totalEnrollments: "$totalEnrollments",
                        serviceCategoryId: "$serviceCategoryId",
                        subTypeId: "$subTypeId",
                        serviceCategoryName: "$serviceCategoryData.name",
                        subTypeData: {
                            $filter: {
                                input: "$serviceCategoryData.appointmentType",
                                as: "appointment",
                                cond: {
                                    $eq: ["$$appointment._id", "$subTypeId"],
                                },
                            },
                        },
                        courseId: "$packageId",
                        courseName: "$pricingDetails.name",
                    },
                },
                {
                    $facet: {
                        count: [{ $match: { scheduleStatus: { $ne: ScheduleStatusType.CANCELED } } }, { $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            const count = result[0].count.length > 0 ? result[0].count[0].total : 0;

            return { list: result[0].list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getSchedulingDetails(user: IUserDocument, schedulingId: string): Promise<Record<string, any>> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            let schedule: any = await this.SchedulingModel.findOne({ organizationId, _id: schedulingId }).lean();
            if (!schedule) throw new NotFoundException("Schedule not found");
            return {
                message: "Schedule fetched successfully",
                data: schedule,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteSchedule(user: IUserDocument, schedulingId: string): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new NotFoundException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only delete the session before it starts");

            const isEnrolled = await this.EnrollmentModel.exists({ schedulingId });
            if (isEnrolled) throw new BadRequestException("Schedule cannot be deleted because clients are already enrolled.");

            await this.SchedulingModel.deleteOne({ _id: schedulingId, organizationId });

            return {
                message: "Schedule deleted successfully",
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async cancelSchedule(user: IUserDocument, schedulingId: string): Promise<any> {
        try {
            const organizationId = await this.getOrganizationId(user);
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException(`Schedule already canceled.`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new BadRequestException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only cancel the session before it starts");
            schedule.scheduleStatus = ScheduleStatusType.CANCELED;
            await schedule.save();
            return {
                message: "Schedule cancelled successfully",
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async cancelRecurringSchedule(user: IUserDocument, dto: CancelSchedulesByIdsDto): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const { scheduleIds } = dto;
            if (!scheduleIds?.length) throw new BadRequestException("No schedule IDs provided for cancellation");

            const baseQuery: any = {
                _id: { $in: scheduleIds.map(id => new Types.ObjectId(id)) },
                classType: ClassType.COURSES,
                scheduleStatus: { $ne: ScheduleStatusType.CANCELED }
            };
            //console.log("Base Query for Cancel Recurring Schedule:", baseQuery);
            switch (user.role.type) {
                case ENUM_ROLE_TYPE.ORGANIZATION:
                    baseQuery.organizationId = user._id;
                    break;
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                case ENUM_ROLE_TYPE.TRAINER: {
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user._id },
                        { organizationId: 1 }
                    ).lean();

                    if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");

                    baseQuery.organizationId = staffDetails.organizationId;

                    if (user.role.type === ENUM_ROLE_TYPE.TRAINER) {
                        baseQuery.trainerId = user._id;
                    }
                    break;
                }
                default:
                    throw new BadRequestException("Access denied");
            }

            const schedules = await this.SchedulingModel.find(baseQuery).sort({ date: 1 }).session(session).lean();
            //console.log("Schedules found for cancellation:", schedules);
            if (!schedules?.length) throw new NotFoundException("Schedule not found");
            if (schedules.length !== scheduleIds.length) throw new NotFoundException("Some of the provided schedules were not found");

            const nowIST = DateTime.now().setZone("Asia/Kolkata");
            const todayIST = nowIST.startOf("day");
            const currentISTTime = nowIST.toFormat("HH:mm");
            const todayStr = todayIST.toISODate();

            for (const sch of schedules) {
                const scheduleDateStr = DateTime.fromJSDate(sch.date).toISODate();
                //console.log("Schedule Date String:", scheduleDateStr);
                if (currentISTTime >= sch.to && scheduleDateStr === todayStr) {
                    throw new BadRequestException(`You cannot cancel past schedules for ${scheduleDateStr}`);
                }
            }

            await this.SchedulingModel.updateMany(
                { _id: { $in: schedules.map(s => s._id) } },
                { $set: { scheduleStatus: ScheduleStatusType.CANCELED, canceledBy: user._id, canceledAt: new Date() } },
                { session }
            );

            const enrollments = await this.EnrollmentModel.find(
                { schedulingId: { $in: schedules.map(s => s._id) },enrollmentStatus: { $ne: ScheduleStatusType.CANCELED } }
            ).session(session).lean();

            if (!enrollments?.length) {
                await this.transactionService.commitTransaction(session);
                return { message: "Schedules cancelled successfully, but no enrollments found to update." };
            }

            const enrollmentMap = new Map<string, string[]>();
            for (const e of enrollments) {
                if (!e.purchaseId) continue;
                const sid = e.schedulingId.toString();
                if (!enrollmentMap.has(sid)) enrollmentMap.set(sid, []);
                enrollmentMap.get(sid).push(e.purchaseId.toString());
            }

            const scheduleSessionMap = new Map<string, number>(
                schedules.map(sch => [sch._id.toString(), sch.sessions])
            );
            //console.log("Enrollment Map:", enrollmentMap);
            //console.log("Schedule Session Map:", scheduleSessionMap);

            for (const [scheduleId, purchaseIds] of enrollmentMap.entries()) {
                const sessionsToDecrement = scheduleSessionMap.get(scheduleId) || 0;
                if (purchaseIds.length && sessionsToDecrement > 0) {
                    await this.PurchaseModel.updateMany(
                        { _id: { $in: purchaseIds.map(id => new Types.ObjectId(id)) } },
                        [
                            {
                                $set: {
                                    sessionConsumed: {
                                        $max: [
                                            { $subtract: ["$sessionConsumed", sessionsToDecrement] },
                                            0
                                        ]
                                    }
                                }
                            }
                        ],
                        { session }
                    );
                }
            }

            await this.EnrollmentModel.updateMany(
                { schedulingId: { $in: schedules.map(s => s._id.toString()) } },
                { $set: { isCheckedIn: false, enrollmentStatus: ScheduleStatusType.CANCELED } },
                { session }
            );

            await this.transactionService.commitTransaction(session);
            this.waitTimeGatewayService.sendWaitingTimeUpdate(schedules[0].facilityId);

            return { message: "Schedules cancelled successfully" };

        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async enrollScheduling(enrollSchedulingDto: EnrollSchedulingDto, user): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const { scheduleIds, customerIds, courseId } = enrollSchedulingDto;

            const [schedules, clients, existingEnrollments]: any = await Promise.all([
                this.SchedulingModel.find({ _id: { $in: scheduleIds } }).lean(),
                this.ClientsModel.find({ userId: { $in: customerIds } })
                    .populate("userId")
                    .lean(),
                this.EnrollmentModel.find({ schedulingId: { $in: scheduleIds }, userId: { $in: customerIds } }).lean(),
            ]);

            const errorMessages: string[] = [];
            const scheduleMap = new Map(schedules.map((s) => [s._id.toString(), s]));
            const clientMap = new Map(clients.map((c) => [c.userId?._id?.toString(), c]));
            const existingEnrollmentSet = new Set(existingEnrollments.map((e) => `${e.schedulingId}-${e.userId}`));

            const missingSchedules = scheduleIds.filter((id) => !scheduleMap.has(id.toString()));
            const missingClients = customerIds.filter((id) => !clientMap.has(id.toString()));

            if (missingSchedules.length) errorMessages.push(`Schedules not found: ${missingSchedules.join(", ")}`);
            if (missingClients.length) errorMessages.push(`Clients not found: ${missingClients.join(", ")}`);

            const now = moment();
            const newEnrollments: any[] = [];

            const purchaseDataMap = new Map();
            const purchaseResults = await Promise.all(clients.map((client) => this.PurchaseModel.findOne({ consumers: client.userId?._id, packageId: courseId }).lean()));
            clients.forEach((client, index) => purchaseDataMap.set(client.userId?._id?.toString(), purchaseResults[index]));

            for (const schedule of schedules) {
                if (!schedule) continue;
                const { date, from, to, organizationId: scheduleOrgId, packageId, scheduleStatus, classCapacity } = schedule;
                const scheduleDate = date ? moment(date).format("DD/MM/YYYY") : "Unknown Date";
                const scheduleTime = from && to ? `Time: ${from} to ${to}` : "Unknown Time";
                const sessionEnd = date && to ? moment(`${moment(date).format("YYYY-MM-DD")} ${to}`, "YYYY-MM-DD HH:mm") : null;
                if (!scheduleOrgId || scheduleOrgId.toString() !== organizationId.toString()) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not belong to the current organization.`);
                    continue;
                }
                if (!packageId || packageId.toString() !== courseId?.toString()) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not belong to the current course.`);
                    continue;
                }
                if (scheduleStatus === ScheduleStatusType.CANCELED) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} is canceled.`);
                    continue;
                }
                if (sessionEnd && now.isAfter(sessionEnd)) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} has already ended.`);
                    continue;
                }

                const totalEnrollments = await this.EnrollmentModel.countDocuments({ schedulingId: schedule._id });
                if (classCapacity - totalEnrollments < clients.length) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not have enough spots.`);
                    continue;
                }
            }

            for (const client of clients) {
                if (!client) continue;
                const clientName = client.userId?.name || "Unknown Client";
                const purchaseData = purchaseDataMap.get(client.userId?._id.toString());

                if (!client.organizationId || client.organizationId.toString() !== organizationId.toString()) {
                    errorMessages.push(`${clientName} is not in the current organization.`);
                    continue;
                }
                if (!purchaseData) {
                    errorMessages.push(`${clientName} has no access to this course.`);
                    continue;
                }
                if (purchaseData.totalSessions === purchaseData.sessionConsumed) {
                    errorMessages.push(`${clientName} has no remaining sessions.`);
                    continue;
                }

                for (const schedule of schedules) {
                    if (existingEnrollmentSet.has(`${schedule._id}-${client.userId?._id}`)) {
                        errorMessages.push(`${clientName} is already enrolled in ${moment(schedule.date).format("DD/MM/YYYY")} (${schedule.from} - ${schedule.to}).`);
                        continue;
                    }
                    newEnrollments.push({
                        schedulingId: schedule._id,
                        userId: client.userId?._id,
                        packageId: courseId,
                        purchaseId: purchaseData._id,
                    });
                    await this.PurchaseModel.updateOne({ _id: purchaseData._id }, { $inc: { sessionConsumed: schedule.sessions } });
                }
            }

            if (errorMessages.length) throw new BadRequestException(errorMessages.join("\n"));
            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments);

            await this.transactionService.commitTransaction(session);
            await this.sendConfirmationEnrolledEmail(clients, schedules, courseId);
            return { message: "All schedules enrolled successfully." };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkIn(user, checkInDto: CheckedInDto): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const { enrollmentId, isCheckedIn } = checkInDto;
            const enrollment = await this.EnrollmentModel.findOne({ _id: enrollmentId });

            if (!enrollment) throw new BadRequestException("Enrollment not found.");

            if (isCheckedIn && enrollment.isCheckedIn) throw new BadRequestException("User has already checked in.");
            else if (!isCheckedIn && !enrollment.isCheckedIn) throw new BadRequestException("User has already not arrived.");

            enrollment.isCheckedIn = isCheckedIn;
            enrollment.checkedInDate = isCheckedIn ? new Date() : null;
            await enrollment.save();

            return { message: isCheckedIn ? "Checked in successfully." : "Mark un-arrived successfully." };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteEnrollment(user, enrollmentId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const enrollmentData = await this.EnrollmentModel.findOne({ _id: enrollmentId }).lean();
            if (!enrollmentData) throw new NotFoundException(`Enrollment not found.`);
            if (enrollmentData.isCheckedIn) throw new BadRequestException("Enrollment cannot be remove because client has already checked in.");
            const schedule = await this.SchedulingModel.findOne({ _id: enrollmentData.schedulingId }).lean();
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            await this.EnrollmentModel.deleteOne({ _id: enrollmentId });
            await this.PurchaseModel.updateOne({ _id: schedule.purchaseId }, { $inc: { sessionConsumed: -1 * schedule.sessions } });

            await this.transactionService.commitTransaction(session);
            return {
                message: "Enrollment deleted successfully",
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }
    async sendConfirmationEnrolledEmail(clientDetails: any, schedule: any, courseId: any): Promise<void> {
        const courseData = await this.PricingModel.findOne({ _id: courseId }).lean();
        if (!courseData) throw new NotFoundException("course Not Found");
        for (const client of clientDetails) {
            for (const slot of schedule) {
                try {
                    const trainerData = await this.UserModel.findOne({ _id: slot.trainerId }).lean();
                    const organizationDetail = await this.UserModel.findOne({ _id: slot.organizationId }).lean();
                    const staffsList = await this.getALltheStaffs(slot?.facilityId);
                    if (client.userId.email) {
                        await this.mailService.sendMail({
                            to: client.userId.email,
                            subject: `You're enrolled in ${courseData.name}`,
                            template: "course-enrolled",
                            context: {
                                clientName: client.name,
                                courseName: courseData.name,
                                date: slot.date,
                                from: slot.from,
                                to: slot.to,
                                trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
                            },
                        });
                    }
                    if (organizationDetail?.email) {
                        await this.mailService.sendMail({
                            to: organizationDetail.email.toString(),
                            subject: `New Enrollment in ${courseData.name}`,
                            template: "course-enrollment-notification",
                            context: {
                                clientName: client.userId.name,
                                courseName: courseData.name,
                                date: slot.date,
                                from: slot.from,
                                to: slot.to,
                                trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
                            },
                        });
                    }
                    for (const staff of staffsList) {
                        await this.mailService.sendMail({
                            to: staff.email.toString(),
                            subject: `New Enrollment in ${courseData.name}`,
                            template: "course-enrollment-notification",
                            context: {
                                clientName: client.userId.name,
                                courseName: courseData.name,
                                date: slot.date,
                                from: slot.from,
                                to: slot.to,
                                trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
                            },
                        });
                    }
                } catch (err) {
                    console.error(`Failed to send email to ${client.email}`, err);
                }
            }
        }
    }
    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "role",
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                },
            },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },
                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || [];
    }
    private async sendScheduleCreationEmail(schedule: any, scheduledBy: any): Promise<void> {
        const courseData = await this.PricingModel.findOne({ _id: schedule.packageId }).lean();
        if (!courseData) throw new NotFoundException("Course not found");

        const trainerData = await this.UserModel.findOne({ _id: schedule.trainerId }).lean();
        const organizationDetail = await this.UserModel.findOne({ _id: schedule.organizationId }).lean();
        const staffList = await this.getALltheStaffs(schedule.facilityId);

        const context = {
            clientName: scheduledBy.name || "Admin",
            courseName: courseData.name,
            date: schedule.date,
            from: schedule.from,
            to: schedule.to,
            trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
        };

        // ➤ Send to Trainer
        if (trainerData?.email) {
            await this.mailService.sendMail({
                to: trainerData.email.toString(),
                subject: `New Schedule Assigned: ${courseData.name}`,
                template: "schedule-created-notification",
                context,
            });
        }

        // ➤ Send to Organization
        if (organizationDetail?.email) {
            await this.mailService.sendMail({
                to: organizationDetail.email.toString(),
                subject: `New Course Schedule Created: ${courseData.name}`,
                template: "schedule-created-notification",
                context,
            });
        }

        // ➤ Send to All Staff
        for (const staff of staffList) {
            if (staff.email) {
                await this.mailService.sendMail({
                    to: staff.email,
                    subject: `New Schedule Alert: ${courseData.name}`,
                    template: "schedule-created-notification",
                    context,
                });
            }
        }

    }
}
