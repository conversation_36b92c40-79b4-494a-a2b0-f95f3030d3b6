
import { applyDecorators } from '@nestjs/common';
import { Transform } from 'class-transformer';
import { IsMongoId, ValidationOptions } from 'class-validator';


export function VoucherCodeNormalize(validationOptions?: ValidationOptions) {
    return applyDecorators(
        Transform(({ value }) => {
            if (!value) return value;

            if (Array.isArray(value)) {
                return value.map(item => {
                    if (typeof item === 'string') {
                        return item.replace(/-/g, '').trim();
                    }
                    return item;
                });
            }

            if (typeof value === 'string') {
                return value.replace(/-/g, '').trim();
            }

            return value;
        }),
    );
}
