import { faker } from '@faker-js/faker';
import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { DatabaseDto } from 'src/common/database/dtos/database.dto';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';

export class PermissionGetResponseDto extends DatabaseDto {
    @ApiProperty({
        description: 'Name of permission',
        example: faker.word.noun(),
        required: true,
    })
    name: string;

    @ApiProperty({
        description: 'Description of permission',
        example: faker.lorem.sentence(),
        required: false,
    })
    description?: string;

    @ApiProperty({
        description: 'Active flag of permission',
        example: true,
        required: true,
    })
    isActive: boolean;

    @ApiProperty({
        description: 'Type of permission',
        example: ENUM_PERMISSION_TYPE,
        required: true,
        enum: ENUM_PERMISSION_TYPE,
    })
    type: ENUM_PERMISSION_TYPE;

    @ApiProperty({
        description: 'Indicates if the permission is delegated',
        example: false,
        required: true,
    })
    isDelegated: boolean;
}