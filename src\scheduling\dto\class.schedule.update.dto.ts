import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, Matches, Validate, ValidateIf } from "class-validator";
import { ClassScheduleCreateDto, ClassScheduleCreateDtoV1 } from "./class.schedule.create.dto";

export class ClassScheduleUpdateDtoV1 extends ClassScheduleCreateDtoV1 {

    @ApiProperty({
        description: "Scheduling ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    scheduleId: string;
}

export class ClassScheduleUpdateDto extends ClassScheduleCreateDto {

    // @ApiProperty({
    //     description: "Start time for availability on specified days (HH:mm)",
    //     example: "08:00",
    // })
    // @IsString({ message: "From time must be a string" })
    // @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    // @IsOptional()
    // from?: string;

    // @ApiProperty({
    //     description: "End time for availability on specified days (HH:mm)",
    //     example: "17:00",
    // })
    // @IsString({ message: "To time must be a string" })
    // @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    // @IsOptional()
    // to?: string;

    @ApiProperty({
        description: "Scheduling ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true,
    })
    @IsString({ message: "Schedule ID must be a string" })
    @IsNotEmpty({message: "Schedule ID is required"})
    scheduleId: string;
}