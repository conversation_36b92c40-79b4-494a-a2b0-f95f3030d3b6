import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty, IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class AddRevenueCategoryDto {
    @ApiProperty({
        description: "The name of the revenue category.",
        example: "Membership Fees",
        required: true,
    })
    @IsString({ message: "Invalid type of revenue category name" })
    @IsNotEmpty({ message: "Revenue category name cannot be empty" })
    @MinLength(2, { message: "Revenue category name must be at least 2 characters" })
    @MaxLength(50, { message: "Revenue category name must be at most 50 characters" })
    name: string;

    @ApiProperty({
        description: "The description of the revenue category.",
        example: "Revenue from membership subscriptions",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of revenue category description" })
    description: string;

    @ApiProperty({
        description: "Whether the revenue category is active.",
        example: true,
        required: false,
        default: true,
    })
    @IsOptional()
    @IsBoolean({ message: "isActive must be a boolean value" })
    isActive?: boolean = true;
}
