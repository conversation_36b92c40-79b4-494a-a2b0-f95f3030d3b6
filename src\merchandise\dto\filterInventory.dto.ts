import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ProductType } from "../schema/product.schema";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ApiProperty } from '@nestjs/swagger';

export class FilterInventoryDto extends PaginationDto {
    @ApiProperty({
        description: 'The Product to search',
        type: String,
        required: false,
        example: 'lenovo ',
    })
    @IsOptional()
    @IsString()
    search: string;

    @ApiProperty({
        description: 'Inventory list of the store Id',
        type: String,
        required: false,
        example: '60d5f5c5b4d3f5a4d4e4f4e5 ',
    })
    @IsOptional()
    @IsMongoId()
    storeId: string;

    @ApiProperty({
        description: 'filter the Product',
        type: String,
        required: false,
        example: 'simple/variable ',
    })
    @IsOptional()
    @IsString()
    productType: string;


}
