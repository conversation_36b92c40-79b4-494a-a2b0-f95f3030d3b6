import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Address } from './address.schema';

@Schema()
export class BusinessAddress extends Address {
    @Prop({ type: String, required: false, trim: true })
    businessName: string;

    @Prop({ type: String, required: false , trim: true})
    gstNumber: string;
}

export const BusinessAddressSchema = SchemaFactory.createForClass(BusinessAddress);
export type BusinessAddressDocument = BusinessAddress & Document;
