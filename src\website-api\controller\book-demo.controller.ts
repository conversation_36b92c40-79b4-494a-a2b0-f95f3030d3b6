import {
    BadRequestException,
    Body,
    Controller,
    Post,
    HttpCode,

} from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { BookDemoService } from "../service/book-demo.service";
import { BookDemoDto } from "../dto/book-demo.dto";

@ApiTags("Website - Book Demo")
@ApiBearerAuth()
@Controller("website/book-demo")
export class BookDemoController {
    constructor(private readonly bookDemoService: BookDemoService) { }

    /**
     * Create a new Booking Demo
     */
    @Post("")
    @HttpCode(200)
    @ApiOperation({ summary: "Create a New Booking Demo" })
    async createBookDemo(@Body() bookDemoDto: BookDemoDto) {
        try {
            const demo = await this.bookDemoService.createDemoRequest(bookDemoDto);
            return {
                message: "Booking Demo created successfully",
                data: demo,
            };
        } catch (error) {
            throw new BadRequestException("Error creating Booking Demo");
        }
    }
}
