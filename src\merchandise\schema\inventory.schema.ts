import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";
import { ProductType } from "./product.schema";

export type InventoryDocument = HydratedDocument<Inventory>;

export enum InventoryActionType {
  RESTOCK = 'restock',
  ADJUSTMENT = 'adjustment',
  BULK_RESTOCK = 'bulk_restock',
}

@Schema({ _id: false }) // embedded subdoc; no separate _id, no own timestamps
export class InventoryHistoryEntry {
  @Prop({ type: String, enum: Object.values(InventoryActionType), required: true })
  actionType: InventoryActionType;

  @Prop({ type: Number, required: false })
  previousQty: number;

  @Prop({ type: Number, required: false })
  changeQty: number; // delta (restock) or new - previous (adjustment)

  @Prop({ type: Number, required: false })
  newQty: number;

  @Prop({ type: Number, required: false })
  newMrp: number;
  @Prop({ type: Number, required: false })
  previousMrp: number;
  @Prop({ type: String })
  notes?: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true })
  performedBy: Types.ObjectId;

  @Prop({ type: Date, default: Date.now })
  at: Date;
}

export const InventoryHistoryEntrySchema = SchemaFactory.createForClass(InventoryHistoryEntry);

@Schema({ timestamps: true })
export class Inventory {
  @Prop({ type: String, required: true, enum: ProductType })
  productType: ProductType;

  @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "facility", index: true })
  storeId: Types.ObjectId;

  @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Product", index: true })
  productId: Types.ObjectId;

  @Prop({ type: SchemaTypes.ObjectId, ref: "Promotion" })
  promotion?: Types.ObjectId | null;

  @Prop({ type: SchemaTypes.ObjectId, ref: "ProductVariant", index: true })
  productVariantId?: Types.ObjectId;

  @Prop({ type: Number, required: false, default: 0 })
  salePrice: number;

  @Prop({ type: Number, required: true })
  mrp: number;

  @Prop({ type: Number, required: true })
  quantity: number;

  @Prop({ type: Date, default: null })
  expiryDate?: Date | null;

  @Prop({ type: Number, default: 0 })
  discount?: number;

  @Prop({ type: Number, required: true, default: 0 })
  discountPrice: number;

  @Prop({ type: SchemaTypes.ObjectId, ref: "User", index: true })
  createdBy?: Types.ObjectId;

  @Prop({ type: SchemaTypes.ObjectId, ref: "Organization", index: true })
  organizationId: Types.ObjectId;

  @Prop({ type: [InventoryHistoryEntrySchema], default: [] })
  history: InventoryHistoryEntry[];
}

export const InventorySchema = SchemaFactory.createForClass(Inventory);

// ---------- Indexes helpful for ledger reads ----------
InventorySchema.index({ _id: 1, 'history.at': -1 });
InventorySchema.index({ _id: 1, 'history.actionType': 1, 'history.at': -1 });
// If you often query all movements for a store or product:
InventorySchema.index({ storeId: 1, productId: 1 });
