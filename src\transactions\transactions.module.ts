import { Module } from '@nestjs/common';
import { TransactionsService } from './service/transactions.service';
import { TransactionsController } from './controllers/transactions.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Reconciliation, ReconciliationSchema } from './schema/reconciliation.schema';
import { StaffProfileDetails, StaffSchema } from 'src/staff/schemas/staff.schema';
import { AuthModule } from 'src/auth/auth.module';
import { Facility, FacilitySchema } from 'src/facility/schemas/facility.schema';
import { Purchase, PurchaseSchema } from 'src/users/schemas/purchased-packages.schema';
import { Invoice, InvoiceSchema } from 'src/users/schemas/invoice.schema';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { PaymentMethod, PaymentMethodSchema } from "src/paymentMethod/schemas/payment-method.schema";


@Module({
  imports: [
    AuthModule,
    MongooseModule.forFeature([
      { name: Reconciliation.name, schema: ReconciliationSchema },
      { name: StaffProfileDetails.name, schema: StaffSchema },
      { name: Facility.name, schema: FacilitySchema },
      { name: Purchase.name, schema: PurchaseSchema },
      { name: Invoice.name, schema: InvoiceSchema },
      { name: PaymentMethod.name, schema: PaymentMethodSchema }
    ], 
      DATABASE_PRIMARY_CONNECTION_NAME
    ),
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService],
})
export class TransactionsModule {}
