import { <PERSON>, Get, Res } from '@nestjs/common';
import { HealthService } from '../services/health.service';
// import { Response } from 'express';
import { CachingService } from 'src/common/caching/services/caching.service';
import { Response } from 'src/common/response/decorators/response.decorator';
@Controller('health')
export class HealthController {
    constructor(
        private readonly healthService: HealthService,
        private readonly cachingService: CachingService
    ) {}

    @Get('/status')
    async checkHealth(@Res() res) {
        const health = await this.healthService.checkHealth();
        res.status(health.status).json(health.data);
    }

    @Get('/redis')
    @Response('response.success')
    async testTheThings(){
        await this.cachingService.set('test', "success", 10);
        return {
            data: true
        };
    }
}
