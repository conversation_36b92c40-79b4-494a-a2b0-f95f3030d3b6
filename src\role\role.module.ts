import { Module } from '@nestjs/common';
import { RoleRepositoryModule } from 'src/role/repository/role.repository.module';
import { RoleService } from 'src/role/services/role.service';
import { RoleAdminController } from './controllers/role.admin.controller';
import { RoleController } from './controllers/role.controller';
import { PolicyModule } from 'src/policy/policy.module';

@Module({
    controllers: [
        RoleAdminController,
        RoleController,
    ],
    providers: [RoleService],
    exports: [RoleService],
    imports: [
        RoleRepositoryModule,
        PolicyModule,
    ],
})
export class RoleModule {}
