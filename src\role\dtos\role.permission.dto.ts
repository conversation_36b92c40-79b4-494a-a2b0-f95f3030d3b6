import { ApiProperty } from '@nestjs/swagger';
import {
    ArrayNotEmpty,
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsString,
} from 'class-validator';
import {
    ENUM_POLICY_ACTION,
} from 'src/policy/enums/policy.enum';

export class RolePermissionDto {
    @ApiProperty({
        required: true,
        description: 'Permission subject',
        // enum: ENUM_POLICY_SUBJECT,
    })
    @IsNotEmpty()
    @IsString()
    // @IsEnum(ENUM_POLICY_SUBJECT)
    subject: string;// ENUM_POLICY_SUBJECT;

    @ApiProperty({
        required: true,
        description: 'Permission action base on subject',
        isArray: true,
        default: [ENUM_POLICY_ACTION.VIEW],
        enum: ENUM_POLICY_ACTION,
    })
    @IsString({ each: true })
    @IsEnum(ENUM_POLICY_ACTION, { each: true })
    @IsArray()
    @IsNotEmpty()
    @ArrayNotEmpty()
    action: ENUM_POLICY_ACTION[];
}
