import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsInt, <PERSON>, <PERSON>, IsMongoId, IsOptional } from "class-validator";

export class ZoutHistoryDTO {
    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number;

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty({ message: "Facility Id is required" })
    @IsMongoId({ message: "Facility Id must be a valid Mongo Id" })
    facilityId: string;

    @ApiProperty({
        description: "Start date",
        example: "2024-09-15T00:00:00Z",
    })
    @IsOptional()
    @Type(() => Date)
    startDate?: Date;

    @ApiProperty({
        description: "End date ",
        example: "2024-09-15T00:00:00Z",
    })
    @IsOptional()
    @Type(() => Date)
    endDate?: Date;
}