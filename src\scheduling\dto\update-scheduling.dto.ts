import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { <PERSON><PERSON><PERSON>y, IsBoolean, IsDate, IsEmpty, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min, ValidateIf, ValidateNested } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { RecurringScheduleDTO } from "./schedule-week.dto";


export class UpdateSchedulingDto {

    @ApiProperty({
        description: "Schedule ID",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    scheduleId: string;

    @ApiProperty({
        description: "Client ID for whom the appointment is made",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @ValidateIf(val => [ClassType.PERSONAL_APPOINTMENT, ClassType.COURSES].includes(val.classType))
    trainerId: string;

    @ApiProperty({
        description: "Purchase Id selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    purchaseId?: string;

    // @ApiProperty({
    //     description: "Package ID if any package is selected for the appointment",
    //     type: String,
    //     example: "615c2f8e2c1ae9123cbb3c1b",
    // })
    // @IsOptional()
    // packageId?: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
    })
    @IsNotEmpty({ message: "Class type is required" })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Service ID if any service is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty({ message: "Subtype is required" })
    subType?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty({ message: "Service Category is required" })
    serviceCategory: string;

    @ApiProperty({
        description: "Pay-rate id",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    payRate: string;

    @ApiProperty({
        description: "Room Id for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    roomId: string;

    @ApiProperty({

        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsOptional()
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    dateRange: DateRange;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty({ message: "Duration is required" })
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    duration: number;

    @ApiProperty({
        description: "Date of the appointment",
        type: Date,
        example: new Date(),
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty({ message: "Date is required" })
    @Type(() => Date)
    date: Date;

    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty({ message: "From time is required" })
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty({ message: "To time is required" })
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;

    @ApiProperty({
        description: "Flag to send confirmation for the appointment",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    sendConfirmation?: boolean;

    @ApiProperty({
        description: "Note for appointment",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "MarkType for Scheduling",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: false,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "MarkType is required" })
    @IsEnum(MarkAvailabilityType, { message: "MarkType must be a valid enum value" })
    markType?: MarkAvailabilityType;

    @ApiProperty({
        description: "Start Date for Scheduling",
        type: Date,
        example: new Date(),
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty()
    @Type(() => Date)
    startDate: Date;

    @ApiProperty({
        description: "End Date for Scheduling",
        type: Date,
        example: new Date(),
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE && obj.markType === MarkAvailabilityType.CUSTOM)
    @IsNotEmpty()
    @Type(() => Date)
    endDate: Date;

    @ApiProperty({
        description: "The schedule for scheduling",
        type: RecurringScheduleDTO,
        required: true
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "Schedule is required" })
    schedule: RecurringScheduleDTO;

}
