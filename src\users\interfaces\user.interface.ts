import { IPolicyDocument } from "src/policy/interfaces/policy.interface";
import { UserDocument } from "../schemas/user.schema";
import { IRoleDocument } from "src/role/interfaces/role.interface";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";

export interface IUserDocument
    extends Omit<UserDocument, 'role' | 'assignedPolicies' | 'restrictedPolicies'> {
    role: IRoleDocument;
    assignedPolicies: IPolicyDocument[];
    restrictedPolicies: IPolicyDocument[];
    permissions?: ENUM_PERMISSION_TYPE[];
}