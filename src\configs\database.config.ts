import { registerAs } from '@nestjs/config';

export default registerAs(
    'database',
    (): Record<string, any> => ({
        url:
            process.env?.MONGODB_URI ?? process.env?.DATABASE_URL ??
            'mongodb://localhost:27017',

        debug: process.env.DATABASE_DEBUG === 'true',
        timeoutOptions: {
            serverSelectionTimeoutMS: 60000, // Increase from 30s to 60s
            socketTimeoutMS: 60000, // Increase from 30s to 60s
            heartbeatFrequencyMS: 10000, // Increase from 5s to 10s
        },
    })
);
