import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

@Schema()
export class Denomination {
    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true })
    value: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true })
    count: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, default: 0 })
    total?: number;
}

@Schema()
export class OtherPayments {
    @ApiProperty({ type: String })
    @Prop({ type: String, required: true })
    method: string;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, min: 0 })
    total: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, min: 0 })
    collected: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: false, default: 0 })
    overUnder: number;
}

const DenominationSchema = SchemaFactory.createForClass(Denomination);

@Schema({ timestamps: true })
export class Reconciliation extends Document {
    @ApiProperty({ type: String })
    @Prop({ type: SchemaTypes.ObjectId, ref: 'Facility', required: true, index: true })
    facilityId: string;

    @ApiProperty({ type: String })
    @Prop({ type: SchemaTypes.ObjectId, ref: 'Organization', required: true, index: true })
    organizationId: string;

    @ApiProperty({ type: String })
    @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true, index: true })
    staffId: string;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true })
    startingAmount: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true })
    leaveAmount: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, default: 0 })
    pettyAmount?: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: false, default: 0 })
    drawerAmount?: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: false, default: 0 })
    depositAmount?: number;

    @ApiProperty({ type: () => [Denomination] })
    @Prop({ type: [DenominationSchema], required: false, default: [] })
    denominations?: Denomination[];

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, default: 0 })
    cashSales?: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: true, default: 0 })
    overUnder?: number;

    @ApiProperty({ type: Number })
    @Prop({ type: Number, required: false, default: 0 })
    onlineOverUnder?: number;

    @ApiProperty({ type: [OtherPayments] })
    @Prop({ type: [OtherPayments], required: false, default: [] })
    otherPayments?: OtherPayments[];

    @ApiProperty({ type: Date })
    @Prop({ type: Date, index: true })
    createdAt: Date;

    @ApiProperty({ type: Date })
    @Prop({ type: Date, index: true })
    updatedAt: Date;
}

export const ReconciliationSchema = SchemaFactory.createForClass(Reconciliation);

ReconciliationSchema.pre('save', function (next) {
    if (this.isModified('denominations')) {
        this.denominations.forEach(denomination => {
            denomination.total = denomination.value * denomination.count;
        });
    }
    if (this.isModified('otherPayments')) {
        let totalOverUnder = 0;
        this.otherPayments.forEach(otherPayment => {
            otherPayment.overUnder = otherPayment.collected - otherPayment.total;
            totalOverUnder += otherPayment.overUnder;
        });
        this.onlineOverUnder = totalOverUnder;
    }
    next();
});
