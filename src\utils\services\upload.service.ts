import { BadRequestException, Injectable } from "@nestjs/common";
import { S3 } from "aws-sdk";
import { ConfigService } from "@nestjs/config";
import { v4 as uuidV4 } from "uuid";

@Injectable()
export class UploadService {
    private bucketName = this.configService.getOrThrow<string>("AWS_S3_BUCKET");

    private readonly s3Client = new S3({
        region: this.configService.getOrThrow<string>("AWS_S3_REGION"),
    });

    constructor(private readonly configService: ConfigService) {}

    async upload(file: Buffer, path: string, fileName: string) {
        try {
            const response = await this.s3Client
                .upload({
                    Bucket: this.bucketName,
                    Key: `${path}${uuidV4()}-${fileName}`,
                    Body: file,
                    ACL: "public-read",
                })
                .promise();

            return response;
        } catch (error) {
            console.log(error);
        }
    }

    async uploadPdf(file: Buffer, path: string, fileName: string,contentType:string) {
        try {
            const response = await this.s3Client
                .upload({
                    Bucket: this.bucketName,
                    Key: `${path}${uuidV4()}-${fileName}`,
                    Body: file,
                    ACL: "public-read",
                    ContentType: contentType,
                })
                .promise();

            return response;
        } catch (error) {
            console.log(error);
        }
    }

    async uploadImage(image: Express.Multer.File): Promise<any> {
        try {
            const uploadImage = await this.upload(image.buffer, "gymBanners/", image.originalname);
            if (!uploadImage?.Location) throw new BadRequestException("Sorry! Unable to upload image");
            return {
                message: "Image uploaded successfully",
                data: uploadImage.Location,
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }
}
