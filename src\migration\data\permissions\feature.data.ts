import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const featurePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Feature Write",
        type: ENUM_PERMISSION_TYPE.FEATURE_WRITE,
        description: 'Grant feature write access',
        isDelegated: true,
    },
    {
        name: "Feature Read",
        type: ENUM_PERMISSION_TYPE.FEATURE_READ,
        description: 'Grant feature read access',
        isDelegated: true,
    },
    {
        name: "Feature Update",
        type: ENUM_PERMISSION_TYPE.FEATURE_UPDATE,
        description: 'Grant feature update access',
        isDelegated: true,
    },
    {
        name: "Feature Delete",
        type: ENUM_PERMISSION_TYPE.FEATURE_DELETE,
        description: 'Grant feature delete access',
        isDelegated: true,
    },
];

