import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import { RoleService } from 'src/role/services/role.service';

@Injectable()
export class MigrationRoleSeed {
    constructor(private readonly roleService: RoleService) { }

    @Command({
        command: 'seed:role',
        describe: 'seed roles',
    })
    async seeds(): Promise<void> {
        const data = [
            {
                _id: '67ea30f201b7ae2d10a0cb6d',
                name: 'superadmin',
                type: ENUM_ROLE_TYPE.SUPER_ADMIN,
                description: 'Super admin role',
            },
            {
                _id: '67ea30f201b7ae2d10a0cb6e',
                name: 'Organization',
                type: ENUM_ROLE_TYPE.ORGANIZATION,
                description: 'Organization role',
            },
            {
                _id: '67ea30f201b7ae2d10a0cb6f',
                name: 'Front Desk Admin',
                type: ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
                description: 'Front Desk Admin role',
            },
            {
                _id: '67ea30f201b7ae2d10a0cb70',
                name: 'Web Master',
                type: ENUM_ROLE_TYPE.WEB_MASTER,
                description: 'Web Master role',
            },
            {
                _id: '67ea30f201b7ae2d10a0cb71',
                name: 'Instructor',
                type: ENUM_ROLE_TYPE.TRAINER,
                description: 'Trainer role',
            },
            {
                _id: '67ea30f201b7ae2d10a0cb72',
                name: 'User',
                type: ENUM_ROLE_TYPE.USER,
                description: 'User role',
                default: true,
            },
        ];

        try {
            const updatePromises = data.map(async (role) => {
                const existingRole = await this.roleService.findOne({ type: role.type });
                if (existingRole) {
                    const { type, _id, ...updateData } = role;
                    return await this.roleService.updateOne(
                        { type: type },
                        updateData
                    );
                } else {
                    return await this.roleService.create(role);
                }
            });
            await Promise.all(updatePromises);
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

    @Command({
        command: 'remove:role',
        describe: 'remove roles',
    })
    async remove(): Promise<void> {
        try {
            await this.roleService.deleteMany({});
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }
}
