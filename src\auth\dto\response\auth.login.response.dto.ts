
import { faker } from "@faker-js/faker";
import { ApiProperty } from "@nestjs/swagger";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthUserResponseDto } from "./auth.user.response.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";

export class AuthLoginResponseDto  {
    @ApiProperty({
        description: "The user object",
        required: true,
        type: AuthUserResponseDto,
    })
    user: AuthUserResponseDto;

    @ApiProperty({
        description: "The role type of the user",
        required: true,
        enum: ENUM_ROLE_TYPE,
    })
    roleType: ENUM_ROLE_TYPE;

    @ApiProperty({
        description: "The access token for the user",
        required: true,
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjoiNjdkOTFjOWZlMWE5MjUyZDRkYWFmMGQzIiwidHlwZSI6InVzZXIiLCJyb2xl....'
    })
    accessToken: string;

    @ApiProperty({
        description: "The type of token",
        required: true,
        example: 'Bearer',
    })
    tokenType: string;

    @ApiProperty({
        description: "The date and time when the user logged in",
        required: true,
        example: faker.date.recent(),
    })
    loginDate: Date;

    @ApiProperty({
        description: "The organization ID associated with the user",
        required: false,
        example: "659d268dee4b6081dacd41fd",
    })
    organizationId: IDatabaseObjectId;

    @ApiProperty({
        description: "The session ID associated with the user",
        required: true,
        example: "659d268dee4b6081dacd41fd",
    })
    session: string;

}