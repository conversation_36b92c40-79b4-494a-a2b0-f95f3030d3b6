import { Optional } from "@nestjs/common";
import { IsEnum, IsOptional, IsString } from "class-validator";
import { ProductType } from "src/merchandise/schema/product.schema";
import { PaginationDto } from "src/utils/dto/pagination.dto";

export class InventoryProductFilterDto extends PaginationDto {
  @IsString()
  search: string;

  @IsEnum(ProductType, { message: "Invalid product type" })
  @IsOptional()
  productType?: ProductType;
}
