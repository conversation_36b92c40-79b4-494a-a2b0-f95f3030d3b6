import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsDate, IsMongoId, IsNotEmpty, IsOptional, IsString, Validate, ValidateIf } from "class-validator";

export class SuspendMembershipDto {

    // @ApiProperty({
    //     description: "Client ID",
    //     type: String,
    //     example: "666666666666666666666666",
    // })
    // @IsNotEmpty()
    // @IsMongoId()
    // clientId: string;

    // @ApiProperty({
    //     description: "Membership ID",
    //     type: String,
    //     example: "666666666666666666666666",
    // })
    // @IsNotEmpty()
    // @IsMongoId()
    // membershipId: string;

    @ApiProperty({
        description: "Purchase ID",
        type: String,
        example: "666666666666666666666666",
    })
    @IsNotEmpty()
    @IsMongoId()
    purchaseId: string;


    @ApiProperty({
        description: 'From Date',
        type: Date,
        example: new Date(new Date().setDate(new Date().getDate() + 2)),
      })
      @IsNotEmpty()
      @IsDate()
      @Type(() => Date)
      @Transform(({ value }) => {
        const date = new Date(value);
        return new Date(Date.UTC(
          date.getUTCFullYear(),
          date.getUTCMonth(),
          date.getUTCDate(),
          0, 0, 0, 0
        ));
      })
      @Validate((value: Date) => value > new Date(), {
        message: 'From date must be greater than current time',
      })
      fromDate: Date;
    
      @ApiProperty({
        description: 'To Date',
        type: Date,
        example: new Date(new Date().setDate(new Date().getDate() + 12)),
      })
      @IsNotEmpty()
      @IsDate()
      @Type(() => Date)
      @Transform(({ value }) => {
        const date = new Date(value);
        return new Date(Date.UTC(
          date.getUTCFullYear(),
          date.getUTCMonth(),
          date.getUTCDate(),
          23, 59, 59, 999
        ));
      })
      endDate: Date;

    @ApiProperty({
        description: "Notes",
        type: String,
        example: "Suspended due to maintenance",
    })
    @IsOptional()
    @IsString()
    notes?: string = "";
}