import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types, PipelineStage } from "mongoose";
import { Appointment } from "src/appointment/schema/appointment.schema";
import { Services } from "src/organization/schemas/services.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { Room } from "src/room/schema/room.schema";
import { SchedulingService } from "src/scheduling/services/scheduling.service";
import { User } from "src/users/schemas/user.schema";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { Organizations } from "src/organization/schemas/organization.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import { MailService } from "src/mail/services/mail.service";
import { ConfigService } from "@nestjs/config";
import { GeneralService } from "src/utils/services/general.service";
import { Otp } from "src/auth/schemas/otp.schema";

/** Local runtime-shape type for appointment subtypes (no schema change needed) */
type SubtypePOJO = {
  _id: Types.ObjectId;
  name?: string;
  durationInMinutes?: number;
  onlineBookingAllowed?: boolean;
  isActive?: boolean;
  image?: string;
  isFeatured?: boolean;
};

/** ---- Types for room-availability helpers ---- */
type HHmm = `${number}${number}:${number}${number}`;
type TimeWindow = { from: HHmm; to: HHmm };
type Interval = { startMin: number; endMin: number };

@Injectable()
export class widgetService {
  readonly adminFrontEndHost = this.configService.getOrThrow<string>("ADMIN_FRONTEND_APP_URL");

  constructor(
    @InjectModel(Services.name) private ServiceModel: Model<Services>,
    @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
    @InjectModel(StaffAvailability.name) private staffAvailabilityModel: Model<StaffAvailability>,
    @InjectModel(Purchase.name) private readonly purchaseModel: Model<PurchaseDocument>,
    @InjectModel(Clients.name) private ClientModel: Model<Clients>,
    @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
    @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
    @InjectModel('Pricing') private PricingModel: Model<any>,
    @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
    @InjectModel(Room.name) private RoomModel: Model<Room>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Otp.name) private OtpModel: Model<Otp>,

    /** Optional: facility-level availability windows. Adjust token/path if your schema name differs. */
    @InjectModel('FacilityAvailability') private FacilityAvailabilityModel: Model<any>,
    private readonly schedulingService: SchedulingService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    private readonly generalService: GeneralService,

  ) { }

  /******************************** Services & Options ********************************/
  async getServiceType(body: any) {
    try {
      if (!body || Object.keys(body).length === 0) {
        throw new BadRequestException('Invalid request data');
      }
      console.log(body, "body")
      let query: any = { organizationId: body?.organizationId };
      query["classType"] = body?.classType ? body?.classType : ["personalAppointment", "bookings"];

      const countProm = this.ServiceModel.countDocuments(query);
      const listProm = this.ServiceModel.find(query).sort({ createdAt: -1 }).lean();
      const [list, _count] = await Promise.all([listProm, countProm]);
      const groupedList = await this.groupByClassType(list);
      return groupedList;
    } catch (error: any) {
      console.error(error);
      throw new BadRequestException(error.message || 'Failed to fetch service types');
    }
  }

  async getServiceOptionType(body: any) {
    try {
      if (!body || Object.keys(body).length === 0) {
        throw new BadRequestException('Invalid request data');
      }
      let query: any = { organizationId: "67a5e32e1bbdd61401b5e36c" };
      query["classType"] = ["personalAppointment", "bookings"];
      if (body.serviceId) query["_id"] = body.serviceId;

      const countProm = this.ServiceModel.countDocuments(query);
      const listProm = this.ServiceModel.find(query)
        .select({ appointmentType: 1, classType: 1 })
        .sort({ createdAt: -1 })
        .lean();

      const [list, _count] = await Promise.all([listProm, countProm]);

      const subTypeIds = list.flatMap((service: any) =>
        service.appointmentType?.map((type: any) => type._id)
      );

      const dayStart = new Date();
      dayStart.setUTCHours(0, 0, 0, 0);
      const dayEnd = new Date();
      dayEnd.setUTCHours(23, 59, 59, 999);

      const pipeline: PipelineStage[] = [
        {
          $match: {
            date: { $gte: dayStart, $lte: dayEnd },
            "timeSlots.availabilityStatus": { $ne: "unavailable" },
          }
        },
        {
          $addFields: {
            timeSlots: {
              $filter: {
                input: "$timeSlots",
                as: "slot",
                cond: {
                  $and: [
                    { $ne: ["$$slot.availabilityStatus", "unavailable"] },
                    { $in: ["$$slot.subTypeId", subTypeIds] },
                  ]
                }
              }
            }
          }
        },
        { $match: { timeSlots: { $ne: [] } } },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "user",
            pipeline: [{ $project: { firstName: 1, lastName: 1 } }]
          }
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        {
          $project: {
            userId: 1,
            facilityId: 1,
            date: 1,
            timeSlots: 1,
            userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
          }
        }
      ];

      const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);

      const payrateIds = [
        ...new Set(
          staffAvailabilities.flatMap((doc: any) =>
            doc.timeSlots.flatMap((slot: any) => slot.payRateIds || [])
          )
        )
      ];

      const payrateDetails = await this.PayRateModel.find({
        _id: { $in: payrateIds },
        serviceCategory: body.serviceId,
        appointmentType: { $in: subTypeIds }
      }).lean();

      return payrateDetails;
    } catch (error: any) {
      console.error(error);
      throw new BadRequestException(error.message || 'Failed to fetch service option types');
    }
  }

  private async groupByClassType(list: any[]) {
    const result: Record<string, Set<string>> = {};
    list.forEach((item: any) => {
      const { classType, ...rest } = item;
      if (!result[classType]) result[classType] = new Set();
      result[classType].add(JSON.stringify(rest));
    });
    const out: Record<string, any[]> = {};
    Object.keys(result).forEach((key) => {
      out[key] = Array.from(result[key]).map((str: string) => JSON.parse(str));
    });
    return out;
  }

  /******************************** Availability (Single Day, Filtered) ********************************/
  async getTrainerAvailablity(body: any) {
    try {
      const organizationId = body.organizationId || "67a5e32e1bbdd61401b5e36c";
      const date = body.date ? new Date(body.date) : new Date();
      const serviceTypes = body.serviceTypes || ["personalAppointment"];

      const dayStart = new Date(date);
      dayStart.setUTCHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setUTCHours(23, 59, 59, 999);

      // Get all trainers
      const trainers = await this.StaffModel.find(
        { organizationId: new Types.ObjectId(organizationId) },
        { userId: 1, facilityId: 1 }
      ).lean();

      if (!trainers.length) return { trainers: [] };

      const trainerIds = trainers.map((t) => t.userId);

      // All availability records on date
      const allAvailabilities = await this.staffAvailabilityModel.find({
        organizationId: new Types.ObjectId(organizationId),
        userId: { $in: trainerIds },
        date: { $gte: dayStart, $lte: dayEnd },
      }).lean();

      // Group by trainer
      const availabilityByTrainer: Record<string, any> = {};
      allAvailabilities.forEach((rec: any) => {
        const key = rec.userId.toString();
        if (!availabilityByTrainer[key]) {
          availabilityByTrainer[key] = rec;
        } else {
          availabilityByTrainer[key].timeSlots = [
            ...availabilityByTrainer[key].timeSlots,
            ...rec.timeSlots,
          ];
        }
      });

      // Facilities
      const facilityIds = [...new Set(trainers.map((t) => t.facilityId))];
      const facilities = await this.FacilityModel.find(
        { _id: { $in: facilityIds } },
        { facilityName: 1 }
      ).lean();
      const facilityMap: Record<string, string> = {};
      facilities.forEach((f: any) => (facilityMap[f._id.toString()] = f.facilityName));

      // User details
      const userIds = Object.keys(availabilityByTrainer).map((id) => new Types.ObjectId(id));
      const users = await this.StaffModel.aggregate([
        { $match: { userId: { $in: userIds } } },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "user",
            pipeline: [{ $project: { firstName: 1, lastName: 1 } }],
          },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: "staffprofiledetails",
            localField: "userId",
            foreignField: "userId",
            as: "staffProfile",
            pipeline: [{ $project: { profilePicture: 1 } }],
          },
        },
        { $unwind: { path: "$staffProfile", preserveNullAndEmptyArrays: true } },
        {
          $project: {
            userId: 1,
            facilityId: 1,
            userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
            profilePicture: "$staffProfile.profilePicture",
          },
        },
      ]);
      const userMap: Record<string, any> = {};
      users.forEach((u: any) => (userMap[u.userId.toString()] = u));

      // Collect payRates
      const payrateIds = [
        ...new Set(
          Object.values(availabilityByTrainer).flatMap((record: any) =>
            record.timeSlots
              .filter((s: any) => s.availabilityStatus !== "unavailable")
              .flatMap((s: any) => s.payRateIds || [])
          )
        ),
      ];
      const payrateDetails = await this.PayRateModel.find({ _id: { $in: payrateIds } }).lean();

      // Service categories & pricing maps
      const serviceCategoryIds = [...new Set(payrateDetails.map((pr: any) => pr.serviceCategory))];
      const serviceCategories = await this.ServiceModel.find({ _id: { $in: serviceCategoryIds } }).lean();

      const pricingQuery = {
        organizationId: new Types.ObjectId(organizationId),
        isActive: true,
        isSellOnline: true,
        $or: [
          { "services.serviceCategory": { $in: serviceCategoryIds.map((id: any) => new Types.ObjectId(id)) } },
          { "services.relationShip.serviceCategory": { $in: serviceCategoryIds.map((id: any) => new Types.ObjectId(id)) } },
        ],
      };
      const pricingList = await this.PricingModel.find(pricingQuery).lean();

      const pricingByServiceCategory: Record<string, any[]> = {};
      const pricingBySubtype: Record<string, any[]> = {};

      const allSubtypeIds = (serviceCategories as any[]).flatMap((cat: any) =>
        (cat.appointmentType || []).map((st: any) => st._id?.toString?.()).filter(Boolean)
      );

      serviceCategoryIds.forEach((id: any) => (pricingByServiceCategory[id.toString()] = []));
      allSubtypeIds.forEach((id: any) => (pricingBySubtype[id] = []));

      for (const pricing of pricingList || []) {
        if (!pricing.services || pricing.isTrialPricing !== true) continue;

        const pricingInfo = {
          _id: pricing._id,
          name: pricing.name,
          price: pricing.price,
          tax: pricing.tax || 0,
          sessionType: pricing.services.sessionType,
          sessionCount: pricing.services.sessionCount,
          expiredInDays: pricing.expiredInDays || 0,
          durationUnit: pricing.durationUnit || 'days',
          isTrialPricing: true,
          description: pricing.description || ''
        };

        if (pricing.services.serviceCategory) {
          const scId = pricing.services.serviceCategory.toString();
          if (pricingByServiceCategory[scId]) {
            pricingByServiceCategory[scId].push(pricingInfo);
          }
          if (Array.isArray(pricing.services.appointmentType)) {
            pricing.services.appointmentType.forEach((aid: any) => {
              const stId = aid.toString();
              if (pricingBySubtype[stId]) pricingBySubtype[stId].push(pricingInfo);
            });
          }
        }

        if (Array.isArray(pricing.services.relationShip)) {
          for (const rel of pricing.services.relationShip) {
            if (!rel?.serviceCategory) continue;
            const rsc = rel.serviceCategory.toString();
            if (pricingByServiceCategory[rsc]) pricingByServiceCategory[rsc].push(pricingInfo);
            if (Array.isArray(rel.subTypeIds)) {
              rel.subTypeIds.forEach((sid: any) => {
                const stId = sid.toString();
                if (pricingBySubtype[stId]) pricingBySubtype[stId].push(pricingInfo);
              });
            }
          }
        }
      }

      // Build subtype map
      const subtypeMap: Record<string, any> = {};
      for (const category of serviceCategories as any[]) {
        if (!category.appointmentType) continue;

        const apt = (category.appointmentType as any[]) || [];
        for (const subtype of apt) {
          const st = subtype as Partial<SubtypePOJO> & { _id?: any };
          const stId = st?._id ? (st._id.toString?.() ?? String(st._id)) : undefined;
          if (!stId) continue;

          const scId = (category._id as any).toString();

          subtypeMap[stId] = {
            name: st.name ?? "Unknown",
            durationInMinutes: st.durationInMinutes ?? 0,
            onlineBookingAllowed: !!st.onlineBookingAllowed,
            isActive: st.isActive ?? true,
            image: st.image ?? "",
            isFeatured: st.isFeatured ?? false,
            serviceId: category._id,
            serviceName: (category as any).name,
            classType: (category as any).classType,
            pricing: pricingBySubtype[stId] || [],
            serviceCategoryPricing: pricingByServiceCategory[scId] || [],
          };
        }
      }

      const trainerAvailability: any[] = [];
      for (const userId of Object.keys(availabilityByTrainer)) {
        const record = availabilityByTrainer[userId];
        const userInfo = userMap[userId] || {
          userName: "Unknown",
          facilityId: record.facilityId,
          profilePicture: "",
        };

        const facilityId = userInfo.facilityId?.toString();
        const facilityName = facilityId ? facilityMap[facilityId] || "Unknown Facility" : "Unknown Facility";

        const availableSlots = record.timeSlots.filter((s: any) => s.availabilityStatus !== "unavailable");

        const slotsWithPayrates = availableSlots.map((slot: any) => {
          const slotPayrates = slot.payRateIds
            ? payrateDetails.filter((pr: any) =>
              slot.payRateIds.some((id: any) => id.toString() === pr._id.toString())
            )
            : [];

          const payratesWithSubtypes = slotPayrates.map((pr: any) => {
            const subId = pr.appointmentType?.toString();
            const sd = subtypeMap[subId] || {
              name: 'Unknown',
              durationInMinutes: 0,
              onlineBookingAllowed: false,
              isActive: true,
              image: "",
              isFeatured: false,
              pricing: [],
              serviceCategoryPricing: [],
            };
            return {
              ...pr,
              subtypeName: sd.name,
              durationInMinutes: sd.durationInMinutes,
              onlineBookingAllowed: sd.onlineBookingAllowed,
              isActive: sd.isActive,
              image: sd.image,
              isFeatured: sd.isFeatured,
              serviceId: sd.serviceId,
              serviceName: sd.serviceName,
              classType: sd.classType,
              pricing: sd.pricing || [],
              serviceCategoryPricing: sd.serviceCategoryPricing || [],
            };
          });

          const filteredPayrates = payratesWithSubtypes.filter((pr: any) => {
            const hasTrialPricing =
              (pr.pricing && pr.pricing.length > 0) ||
              (pr.serviceCategoryPricing && pr.serviceCategoryPricing.length > 0);
            return pr.classType && serviceTypes.includes(pr.classType) && hasTrialPricing;
          });

          return { ...slot, payrates: filteredPayrates };
        });

        const filteredSlots = slotsWithPayrates.filter((s: any) => s.payrates && s.payrates.length > 0);

        if (filteredSlots.length > 0) {
          trainerAvailability.push({
            userId: new Types.ObjectId(userId),
            userName: userInfo.userName,
            facilityId: userInfo.facilityId,
            facilityName,
            profilePicture: userInfo.profilePicture || "",
            date: record.date,
            timeSlots: filteredSlots,
          });
        }
      }

      return { date: date, trainers: trainerAvailability };
    } catch (error: any) {
      console.error('Error in getTrainerAvailablity:', error);
      throw new BadRequestException(error.message);
    }
  }

  /******************************** Availability (Multi-trainer, Multi-day) ********************************/
  async listStaffAvailabilityListByTypesForApp(listStaffAvailabilityDto: any): Promise<any> {
    const { userIds, facilityIds, startDate, endDate, classType, serviceCategoryId, subTypeId } = listStaffAvailabilityDto;

    if (!facilityIds) throw new Error('facilityIds is required');

    const filter: any = {
      date: {
        $gte: new Date(new Date(startDate).setUTCHours(0, 0, 0, 0)),
        $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999)),
      },
    };
    if (userIds?.length > 0) filter.userId = { $in: userIds.map((e: string) => new Types.ObjectId(e)) };
    if (classType) filter["timeSlots.classType"] = classType;
    filter.facilityId = { $in: facilityIds.map((e: string) => new Types.ObjectId(e)) };

    const pipeline: PipelineStage[] = [
      { $match: filter },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user"
        }
      },
      { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$timeSlots", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$timeSlots.payRateIds", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "payrates",
          localField: "timeSlots.payRateIds",
          foreignField: "_id",
          as: "payrate",
          pipeline: [
            {
              $match: {
                serviceCategory: new Types.ObjectId(serviceCategoryId),
                appointmentType: new Types.ObjectId(subTypeId),
                serviceType: classType
              }
            },
            { $project: { _id: 1 } }
          ]
        }
      },
      { $match: { payrate: { $ne: [] } } },
      {
        $addFields: {
          userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
          "timeSlots.payRateIds": ["$timeSlots.payRateIds"]
        }
      },
      {
        $group: {
          _id: { userId: "$userId", facilityId: "$facilityId", organizationId: "$organizationId", date: "$date" },
          userName: { $first: "$userName" },
          date: { $first: "$date" },
          dateId: { $first: "$_id" },
          timeSlots: { $push: "$timeSlots" }
        }
      },
      {
        $group: {
          _id: { userId: "$_id.userId", facilityId: "$_id.facilityId", organizationId: "$_id.organizationId" },
          userName: { $first: "$userName" },
          schedule: {
            $push: { dateId: "$dateId", date: "$date", timeSlots: "$timeSlots" }
          }
        }
      },
      { $set: { startDate: startDate, endDate: endDate } }
    ];

    const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);
    return staffAvailabilities;
  }

  /**
   * Generate time slots for ALL trainers across a date range
   */
  async getAllTrainersTimeSlots(body: {
    organizationId: string;
    facilityIds: string[];
    startDate: string;  // ISO
    endDate: string;    // ISO
    classType: string;  // e.g., "personalAppointment"
    serviceCategoryId: string;
    subTypeId: string;
    userIds?: string[];
    clientUserId?: string; // optional; if provided & single-day, returns purchasePackage
  }) {
    const {
      organizationId,
      facilityIds,
      startDate,
      endDate,
      classType,
      serviceCategoryId,
      subTypeId,
      userIds,
      clientUserId,
    } = body;

    if (!organizationId || !facilityIds?.length || !startDate || !endDate || !classType || !serviceCategoryId || !subTypeId) {
      throw new BadRequestException('organizationId, facilityIds[], startDate, endDate, classType, serviceCategoryId, and subTypeId are required');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const dayStart = new Date(start); dayStart.setUTCHours(0, 0, 0, 0);
    const dayEnd = new Date(end); dayEnd.setUTCHours(23, 59, 59, 999);

    // Subtype exact (returns the exact subdoc)
    const subtype = await this.getSubtypeDetailsExact(subTypeId, serviceCategoryId, organizationId);
    if (!subtype) throw new BadRequestException('Invalid subtype/serviceCategory combination');

    const durationInMinutes = subtype.durationInMinutes;
    if (!durationInMinutes || Number.isNaN(Number(durationInMinutes))) {
      throw new BadRequestException('Subtype durationInMinutes is missing/invalid');
    }

    // Availability list filtered by classType & payrate match
    const staffAvailabilities = await this.listStaffAvailabilityListByTypesForApp({
      userIds,
      facilityIds,
      startDate: dayStart.toISOString(),
      endDate: dayEnd.toISOString(),
      classType,
      serviceCategoryId,
      subTypeId,
    });

    if (!staffAvailabilities?.length) {
      return { startDate: dayStart, endDate: dayEnd, trainers: [], purchasePackage: [] };
    }

    // Trainer ids
    const trainerIds = Array.from(
      new Set(staffAvailabilities.map((it: any) => it._id?.userId?.toString()).filter(Boolean))
    ).map((id: any) => new Types.ObjectId(id));

    // Batch bookings across the whole range
    const existing = await this.SchedulingModel.aggregate([
      {
        $match: {
          trainerId: { $in: trainerIds },
          date: { $gte: dayStart, $lte: dayEnd },
          scheduleStatus: { $ne: 'canceled' },
        }
      },
      {
        $project: {
          trainerId: 1,
          from: 1,
          to: 1,
          dateKey: { $dateToString: { format: "%Y-%m-%d", date: "$date", timezone: "UTC" } },
        }
      },
      {
        $group: {
          _id: { trainerId: "$trainerId", dateKey: "$dateKey" },
          bookings: { $push: { from: "$from", to: "$to" } },
        }
      }
    ]).exec();

    const bookingMap = new Map<string, Array<{ from: string; to: string }>>();
    for (const row of existing) {
      const key = `${row._id.trainerId.toString()}_${row._id.dateKey}`;
      bookingMap.set(key, row.bookings || []);
    }

    // Generate slots
    const trainers = staffAvailabilities.map((staff: any) => {
      const trainerId = staff._id.userId.toString();
      const facilityId = staff._id.facilityId?.toString?.() ?? staff._id.facilityId;
      const trainerName = staff.userName || "Unknown Trainer";

      const schedule = (staff.schedule || []).map((dayEntry: any) => {
        const dateObj: Date = new Date(dayEntry.date);
        const dateKey = this.dateKeyUTC(dateObj);
        const existingBookings = bookingMap.get(`${trainerId}_${dateKey}`) || [];

        const outSlots: Array<any> = [];
        for (const av of (dayEntry.timeSlots || [])) {
          if (!av) continue;
          if (av.availabilityStatus !== 'available') continue;

          const payRateIds = Array.isArray(av.payRateIds)
            ? av.payRateIds
            : (av.payRateIds ? [av.payRateIds] : []);

          const generated = this.generateSlotsForTimeRange(
            av.from,
            av.to,
            durationInMinutes,
            existingBookings,
            payRateIds
          );
          outSlots.push(...generated);
        }

        // de-dup
        const dedupMap = new Map<string, any>();
        for (const s of outSlots) dedupMap.set(`${s.from}-${s.to}`, s);
        const timeSlots = Array.from(dedupMap.values()).sort((a: any, b: any) => a.from.localeCompare(b.from));

        return { date: dateObj, durationInMinutes, timeSlots };
      });

      return { trainerId, trainerName, facilityId, organizationId, schedule };
    });

    // Optional purchased package (single-day only)
    let purchasePackage: any[] = [];
    const singleDay = this.dateKeyUTC(dayStart) === this.dateKeyUTC(dayEnd);
    if (clientUserId && singleDay) {
      purchasePackage = await this.pricingByUserAndSubType(
        {
          serviceCategoryId,
          subTypeId,
          classType,
          clientUserId,
          date: dayStart.toISOString(),
        },
        organizationId
      );
    }

    return { startDate: dayStart, endDate: dayEnd, trainers, purchasePackage };
  }

  /******************************** Courses ********************************/
  async getCourseList(body: any) {
    try {
      const organizationId = body.organizationId || "678505b84d8168563536ed5a";
      const filter: any = {
        organizationId: new Types.ObjectId(organizationId),
        isBundledPricing: { $ne: true },
        "services.type": "courses",
        isActive: true,
        isSellOnline: true,
      };
      if (body.search) filter.name = { $regex: body.search, $options: "i" };
      if (body.isFeatured !== undefined) filter.isFeatured = body.isFeatured;

      const pricingList = await this.PricingModel.find(filter)
        .select('name price tax expiredInDays services.serviceCategory isFeatured')
        .sort({ createdAt: -1 })
        .lean();

      const serviceCategoryIds = pricingList.map((p: any) => p.services?.serviceCategory);
      const serviceCategories = await this.ServiceModel.find(
        { _id: { $in: serviceCategoryIds } },
        { name: 1, appointmentType: 1, image: 1 }
      ).lean();

      const serviceCategoryMap: Record<string, any> = {};
      serviceCategories.forEach((c: any) => (serviceCategoryMap[c._id.toString()] = c));

      const courses = pricingList.map((pricing: any) => {
        const sc = pricing.services?.serviceCategory
          ? serviceCategoryMap[pricing.services.serviceCategory.toString()]
          : null;
        return {
          _id: pricing._id,
          name: pricing.name,
          price: pricing.price,
          tax: pricing.tax || 0,
          expiredInDays: pricing.expiredInDays,
          isFeatured: pricing.isFeatured || false,
          serviceCategory: sc
            ? { _id: sc._id, name: sc.name, image: sc.image || "" }
            : null,
        };
      });

      return courses;
    } catch (error: any) {
      console.error('Error in getCourseList:', error);
      throw new BadRequestException(error.message);
    }
  }

  async getCourseDetail(body: any) {
    try {
      if (!body || !body.id) throw new BadRequestException('Course ID is required');
      const courseId = body.id;

      const pricingData: any = await this.PricingModel.findOne({
        _id: new Types.ObjectId(courseId),
        isActive: true,
      }).lean();
      if (!pricingData) throw new NotFoundException('Course not found');

      const serviceCategoryId = pricingData?.services?.serviceCategory;
      const serviceCategory = await this.ServiceModel.findOne(
        { _id: serviceCategoryId },
        { name: 1, appointmentType: 1, description: 1, image: 1 }
      ).lean();
      if (!serviceCategory) throw new NotFoundException('Service category not found');

      const trainers = await this.PayRateModel.aggregate([
        {
          $match: {
            serviceCategory: new Types.ObjectId(serviceCategoryId),
            appointmentType: { $in: pricingData.services?.appointmentType || [] },
          }
        },
        {
          $lookup: {
            from: "staffprofiledetails",
            localField: "userId",
            foreignField: "userId",
            as: "staffProfile",
          }
        },
        { $unwind: "$staffProfile" },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userData",
          }
        },
        { $unwind: "$userData" },
        {
          $lookup: {
            from: "facilities",
            localField: "staffProfile.facilityId",
            foreignField: "_id",
            as: "facilityData",
          }
        },
        { $unwind: { path: "$facilityData", preserveNullAndEmptyArrays: true } },
        {
          $project: {
            _id: 1,
            userId: 1,
            serviceCategory: 1,
            appointmentType: 1,
            payRate: 1,
            trainerName: { $concat: ["$userData.firstName", " ", "$userData.lastName"] },
            profilePicture: "$staffProfile.profilePicture",
            facilityId: "$staffProfile.facilityId",
            facilityName: "$facilityData.facilityName",
            experience: "$staffProfile.experience",
            certification: "$staffProfile.certification",
            description: "$staffProfile.description",
          }
        }
      ]);

      let expirationInfo = null;
      if (pricingData && pricingData.expiredInDays) {
        const multiplierMap: Record<string, number> = {
          days: 24 * 60 * 60 * 1000,
          months: 30 * 24 * 60 * 60 * 1000,
          years: 365 * 24 * 60 * 60 * 1000,
        };
        const durationUnit = pricingData.durationUnit || 'days';
        const multiplier = multiplierMap[durationUnit] || multiplierMap.days;
        const expirationMs = pricingData.expiredInDays * multiplier;
        expirationInfo = { days: pricingData.expiredInDays, unit: durationUnit, expirationMs };
      }

      const singlePricingData = Array.isArray(pricingData) ? pricingData[0] : pricingData;
      if (!singlePricingData) throw new NotFoundException('Course not found');

      const courseDetail = {
        _id: singlePricingData._id,
        name: singlePricingData.name,
        price: singlePricingData.price,
        tax: singlePricingData.tax || 0,
        description: singlePricingData.description || serviceCategory.description || "",
        expiredInDays: singlePricingData.expiredInDays,
        expirationInfo,
        isFeatured: singlePricingData.isFeatured || false,
        isSellOnline: singlePricingData.isSellOnline ?? true,
        sessionType: singlePricingData.services?.sessionType,
        sessionCount: singlePricingData.services?.sessionCount,
        serviceCategory: {
          _id: serviceCategory._id,
          name: serviceCategory.name,
          image: serviceCategory.image || "",
          appointmentTypes: serviceCategory.appointmentType || [],
        },
        trainers,
      };

      return courseDetail;
    } catch (error: any) {
      console.error('Error in getCourseDetail:', error);
      throw new BadRequestException(error.message);
    }
  }

  /******************************** Time Slots: Single Trainer ********************************/
  async getTrainerTimeSlots(body: any) {
    try {
      if (!body || !body.trainerId || !body.subtypeId || !body.date) {
        throw new BadRequestException('trainerId, subtypeId, and date are required');
      }

      const { trainerId, subtypeId, date, organizationId = "678505b84d8168563536ed5a", serviceCategoryId } = body;

      const targetDate = new Date(date);
      const dayStart = new Date(targetDate); dayStart.setUTCHours(0, 0, 0, 0);
      const dayEnd = new Date(targetDate); dayEnd.setUTCHours(23, 59, 59, 999);

      // Exact subtype (safer than "first element")
      const subtypeDetails = await this.getSubtypeDetailsExact(subtypeId, serviceCategoryId, organizationId);
      if (!subtypeDetails) throw new BadRequestException('Invalid subtype ID');

      const durationInMinutes = subtypeDetails.durationInMinutes;

      const trainerAvailability = await this.staffAvailabilityModel.findOne({
        userId: new Types.ObjectId(trainerId),
        date: { $gte: dayStart, $lte: dayEnd },
        organizationId: new Types.ObjectId(organizationId),
      }).lean();

      if (!trainerAvailability) {
        return {
          date: targetDate,
          trainerId,
          subtypeId,
          durationInMinutes,
          timeSlots: [],
        };
      }

      const existingBookings = await this.SchedulingModel.find(
        {
          trainerId: new Types.ObjectId(trainerId),
          date: { $gte: dayStart, $lte: dayEnd },
          scheduleStatus: { $ne: 'canceled' },
        },
        { from: 1, to: 1 }
      ).lean();

      const timeSlots = this.generateTimeSlots(
        trainerAvailability.timeSlots,
        existingBookings,
        durationInMinutes,
        subtypeId
      );

      const reqBody = {
        serviceCategoryId,
        subTypeId: subtypeId,
        classType: "personalAppointment",
        clientUserId: body.userId,
        date,
      };
      const purchasedPackage = await this.pricingByUserAndSubType(reqBody, organizationId);

      return {
        date: targetDate,
        trainerId,
        subtypeId,
        durationInMinutes,
        trainerName: await this.getTrainerName(trainerId),
        timeSlots,
        purchasePackage: purchasedPackage,
      };
    } catch (error: any) {
      console.error('Error in getTrainerTimeSlots:', error);
      throw new BadRequestException(error.message);
    }
  }

  /******************************** NEW: Room Availability (Bookings) ********************************/
  /**
   * Compute room-wise bookable slots for classType='bookings' on a given day.
   * Inputs (body):
   * - organizationId, facilityId, serviceCategoryId, subTypeId, date (ISO)
   * Optional:
   * - stepMinutes (defaults to subtype duration)
   *
   * Returns rooms[] with schedule [{from,to}].
   */
  async getRoomsAvailabilityForBookings(body: {
    organizationId: string;
    facilityId: string;
    serviceCategoryId: string;
    subTypeId: string;
    date: string;           // ISO or yyyy-mm-dd
    stepMinutes?: number;   // optional override
  }) {
    const {
      organizationId,
      facilityId,
      serviceCategoryId,
      subTypeId,
      date,
      stepMinutes,
    } = body || {};

    if (!organizationId || !facilityId || !serviceCategoryId || !subTypeId || !date) {
      throw new BadRequestException(
        "organizationId, facilityId, serviceCategoryId, subTypeId and date are required"
      );
    }

    // Day bounds (UTC)
    const day = new Date(date);
    if (isNaN(day.getTime())) throw new BadRequestException("Invalid date");
    const dayStart = new Date(day); dayStart.setUTCHours(0, 0, 0, 0);
    const dayEnd = new Date(day); dayEnd.setUTCHours(23, 59, 59, 999);

    // Subtype duration/buffer
    const svc = await this.ServiceModel.findOne(
      { _id: new Types.ObjectId(serviceCategoryId), "appointmentType._id": new Types.ObjectId(subTypeId) },
      { "appointmentType.$": 1 }
    ).lean();

    const sub = (svc as any)?.appointmentType?.[0] || {};
    const durationInMinutes: number = Number(sub?.durationInMinutes) > 0 ? Number(sub.durationInMinutes) : 30;
    const bufferInMinutes: number = Number(sub?.bufferInMinutes) >= 0 ? Number(sub.bufferInMinutes) : 0;
    const step = Number.isFinite(stepMinutes) && stepMinutes! > 0 ? stepMinutes! : durationInMinutes;

    // Facility hours (default: full day)
    let baseIntervals: Interval[] = [{ startMin: 0, endMin: 24 * 60 }];
    try {
      const dayKey = this.dayKey(day); // 'mon'..'sun'
      const recs = await this.FacilityAvailabilityModel.find({
        facilityId: new Types.ObjectId(facilityId),
        $or: [
          { type: 'available' },
          { type: 'unavailable', fromDate: { $lte: dayEnd }, endDate: { $gte: dayStart } },
        ],
      }).lean();

      const availableRec = recs?.find((r: any) => r?.type === 'available');
      const unavailableRec = recs?.find((r: any) => r?.type === 'unavailable');

      const workingWindows: TimeWindow[] = availableRec?.workingHours?.[dayKey] || [];
      if (workingWindows?.length) baseIntervals = this.windowsToIntervals(workingWindows);

      const facilityBlocks: TimeWindow[] = unavailableRec?.time || [];
      if (facilityBlocks?.length) {
        const blocks = this.windowsToIntervals(facilityBlocks);
        baseIntervals = this.subtractIntervals(baseIntervals, blocks);
      }
    } catch {
      // If FacilityAvailability model not present or has diff schema, fallback to full day.
      baseIntervals = [{ startMin: 0, endMin: 24 * 60 }];
    }

    // Fetch rooms
    const rooms = await this.RoomModel.find(
      {
        facilityId: new Types.ObjectId(facilityId),
        classType: 'bookings',
        serviceCategory: new Types.ObjectId(serviceCategoryId),
        status: true,
      },
      { _id: 1, roomName: 1, capacity: 1 }
    ).lean();

    if (!rooms?.length) {
      return {
        date: dayStart,
        facilityId,
        serviceCategoryId,
        subTypeId,
        classType: 'bookings',
        durationInMinutes,
        rooms: [],
      };
    }

    // Fetch existing bookings for the day for these rooms
    const roomIds = rooms.map((r: any) => r._id);
    const bookingAgg = await this.SchedulingModel.aggregate([
      {
        $match: {
          roomId: { $in: roomIds },
          facilityId: new Types.ObjectId(facilityId),
          date: { $gte: dayStart, $lte: dayEnd },
          scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
        }
      },
      {
        $group: {
          _id: "$roomId",
          windows: { $push: { from: "$from", to: "$to" } }
        }
      }
    ]).exec();

    const bookingsByRoom = new Map<string, TimeWindow[]>();
    for (const row of bookingAgg || []) {
      bookingsByRoom.set(String(row._id), (row.windows || []) as TimeWindow[]);
    }

    // Build per-room availability
    const roomsOut = rooms.map((room: any) => {
      const windows = bookingsByRoom.get(String(room._id)) || [];

      // Fully-booked intervals (concurrency >= capacity)
      const fullyBooked = this.computeFullyBookedIntervals(windows, Number(room.capacity) || 1);

      // Free intervals = baseIntervals - fullyBooked
      let free = this.subtractIntervals(baseIntervals, fullyBooked);

      // Generate slots
      const slots = this.generateSlots(free, durationInMinutes, step, bufferInMinutes)
        .map(s => ({ from: this.minToHHmm(s.startMin), to: this.minToHHmm(s.endMin) }));

      return {
        roomId: room._id,
        roomName: room.roomName,
        capacity: room.capacity,
        schedule: slots,
      };
    });

    // Filter out empty rooms if desired
    const nonEmptyRooms = roomsOut.filter(r => r.schedule?.length);

    return {
      date: dayStart,
      facilityId,
      serviceCategoryId,
      subTypeId,
      classType: 'bookings',
      durationInMinutes,
      rooms: nonEmptyRooms,
    };
  }

  /******************************** Helpers: Subtype, Names ********************************/
  private async getSubtypeDetails(subtypeId: string, serviceCategoryId: string, organizationId: string) {
    try {
      const service = await this.ServiceModel.findOne({
        'appointmentType._id': new Types.ObjectId(subtypeId),
        _id: new Types.ObjectId(serviceCategoryId),
        organizationId: new Types.ObjectId(organizationId),
      }).lean();
      if (service && service.appointmentType && service.appointmentType.length > 0) {
        return service.appointmentType[0]; // kept for backward compatibility
      }
      return null;
    } catch (error) {
      console.error('Error getting subtype details:', error);
      return null;
    }
  }

  /** Exact appointmentType sub-doc for (serviceCategoryId, subtypeId) */
  private async getSubtypeDetailsExact(subtypeId: string, serviceCategoryId: string, organizationId: string) {
    const rows = await this.ServiceModel.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(serviceCategoryId),
          organizationId: new Types.ObjectId(organizationId),
        }
      },
      { $unwind: "$appointmentType" },
      { $match: { "appointmentType._id": new Types.ObjectId(subtypeId) } },
      { $replaceRoot: { newRoot: "$appointmentType" } },
      { $project: { _id: 1, name: 1, durationInMinutes: 1, onlineBookingAllowed: 1, isActive: 1 } }
    ]).exec();
    return rows?.[0] ?? null;
  }

  private async getTrainerName(trainerId: string) {
    try {
      const trainer = await this.StaffModel.aggregate([
        { $match: { userId: new Types.ObjectId(trainerId) } },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "user",
            pipeline: [{ $project: { firstName: 1, lastName: 1 } }],
          },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        { $project: { userName: { $concat: ["$user.firstName", " ", "$user.lastName"] } } },
      ]);

      return trainer.length > 0 ? trainer[0].userName : "Unknown Trainer";
    } catch (error) {
      console.error('Error getting trainer name:', error);
      return "Unknown Trainer";
    }
  }

  /******************************** Slot Generation ********************************/
  private generateTimeSlots(availabilitySlots: any[], existingBookings: any[], durationInMinutes: number, _subtypeId: string) {
    const timeSlots: any[] = [];
    const relevantSlots = (availabilitySlots || []).filter((slot: any) =>
      slot.availabilityStatus === 'available' &&
      slot.payRateIds && slot.payRateIds.length > 0
    );

    for (const availabilitySlot of relevantSlots) {
      const slotTimeSlots = this.generateSlotsForTimeRange(
        availabilitySlot.from,
        availabilitySlot.to,
        durationInMinutes,
        existingBookings,
        availabilitySlot.payRateIds
      );
      timeSlots.push(...slotTimeSlots);
    }

    timeSlots.sort((a, b) => a.from.localeCompare(b.from));
    return timeSlots;
  }

  private generateSlotsForTimeRange(
    startTime: string,
    endTime: string,
    durationInMinutes: number,
    existingBookings: Array<{ from: string; to: string }>,
    payRateIds: string[]
  ) {
    const slots: any[] = [];
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    let currentMinutes = startMinutes;

    while (currentMinutes + durationInMinutes <= endMinutes) {
      const slotStart = this.minutesToTime(currentMinutes);
      const slotEnd = this.minutesToTime(currentMinutes + durationInMinutes);

      const isAvailable = !this.hasTimeConflict(slotStart, slotEnd, existingBookings);

      slots.push({
        from: slotStart,
        to: slotEnd,
        isAvailable,
        payRateIds,
        durationInMinutes,
      });

      currentMinutes += durationInMinutes;
    }
    return slots;
  }

  private timeToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /** Improved numeric overlap check */
  private hasTimeConflict(
    slotStart: string,
    slotEnd: string,
    existingBookings: Array<{ from: string; to: string }>
  ): boolean {
    const s = this.timeToMinutes(slotStart);
    const e = this.timeToMinutes(slotEnd);
    return existingBookings.some((b) => {
      const bs = this.timeToMinutes(b.from);
      const be = this.timeToMinutes(b.to);
      return s < be && e > bs; // overlap if start < otherEnd && end > otherStart
    });
  }

  private dateKeyUTC(d: Date | string): string {
    const x = new Date(d);
    return x.toISOString().slice(0, 10);
  }

  /** --- Helpers for room availability --- */
  private dayKey(d: Date): string {
    // 0=Sun..6=Sat, return 'sun'..'sat'
    const map = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    return map[d.getUTCDay()];
  }

  private hhmmToMin(hhmm: HHmm): number {
    const [h, m] = hhmm.split(':').map(Number);
    return h * 60 + m;
  }

  private minToHHmm(mins: number): HHmm {
    const h = Math.floor(mins / 60).toString().padStart(2, '0');
    const m = (mins % 60).toString().padStart(2, '0');
    return `${h}:${m}` as HHmm;
  }

  private windowsToIntervals(windows: TimeWindow[]): Interval[] {
    return (windows || [])
      .map(w => ({ startMin: this.hhmmToMin(w.from), endMin: this.hhmmToMin(w.to) }))
      .filter(i => i.endMin > i.startMin)
      .sort((a, b) => a.startMin - b.startMin);
  }

  private mergeIntervals(ints: Interval[]): Interval[] {
    if (!ints?.length) return [];
    const sorted = [...ints].sort((a, b) => a.startMin - b.startMin);
    const out: Interval[] = [sorted[0]];
    for (let i = 1; i < sorted.length; i++) {
      const last = out[out.length - 1];
      const cur = sorted[i];
      if (cur.startMin <= last.endMin) {
        last.endMin = Math.max(last.endMin, cur.endMin);
      } else {
        out.push({ ...cur });
      }
    }
    return out;
  }

  /** base - subtract blocks (returns parts of base not overlapped by blocks) */
  private subtractIntervals(base: Interval[], blocks: Interval[]): Interval[] {
    if (!base?.length) return [];
    if (!blocks?.length) return this.mergeIntervals(base);

    const mergedBlocks = this.mergeIntervals(blocks);
    const out: Interval[] = [];

    for (const b of base) {
      let cur: Interval[] = [{ ...b }];
      for (const blk of mergedBlocks) {
        const next: Interval[] = [];
        for (const seg of cur) {
          // no overlap
          if (blk.endMin <= seg.startMin || blk.startMin >= seg.endMin) {
            next.push(seg);
            continue;
          }
          // overlap cases
          if (blk.startMin > seg.startMin) {
            next.push({ startMin: seg.startMin, endMin: Math.max(seg.startMin, blk.startMin) });
          }
          if (blk.endMin < seg.endMin) {
            next.push({ startMin: Math.min(blk.endMin, seg.endMin), endMin: seg.endMin });
          }
        }
        cur = next;
        if (cur.length === 0) break;
      }
      out.push(...cur);
    }
    return this.mergeIntervals(out).filter(i => i.endMin - i.startMin >= 5);
  }

  /**
   * Build intervals where concurrency >= capacity using sweep-line.
   */
  private computeFullyBookedIntervals(bookings: TimeWindow[], capacity: number): Interval[] {
    if (!bookings?.length || capacity <= 0) return [];
    const events: Array<{ t: number; d: number }> = [];

    for (const b of bookings) {
      const s = this.hhmmToMin(b.from);
      const e = this.hhmmToMin(b.to);
      if (e > s) {
        events.push({ t: s, d: +1 });
        events.push({ t: e, d: -1 });
      }
    }
    events.sort((a, b) => (a.t - b.t) || (a.d - b.d));

    const out: Interval[] = [];
    let cur = 0;
    let segStart: number | null = null;

    for (const ev of events) {
      const prev = cur;
      cur += ev.d;

      if (prev >= capacity && segStart !== null && segStart < ev.t) {
        out.push({ startMin: segStart, endMin: ev.t });
        segStart = null;
      }
      if (cur >= capacity && segStart === null) {
        segStart = ev.t;
      }
    }
    if (segStart !== null) {
      out.push({ startMin: segStart, endMin: 24 * 60 });
    }

    return this.mergeIntervals(out);
  }

  /**
   * Generate fixed-length slots inside free intervals.
   * Each slot "occupies" duration + optional buffer, but exposed window is [duration].
   */
  private generateSlots(
    free: Interval[],
    durationInMinutes: number,
    stepMinutes: number,
    bufferInMinutes = 0,
  ): Interval[] {
    const occupy = durationInMinutes + (bufferInMinutes || 0);
    const slots: Interval[] = [];

    for (const f of free) {
      let start = f.startMin;
      if (start % stepMinutes !== 0) {
        start = start + (stepMinutes - (start % stepMinutes));
      }
      while (start + occupy <= f.endMin) {
        slots.push({ startMin: start, endMin: start + durationInMinutes });
        start += stepMinutes;
      }
    }
    return slots;
  }

  /******************************** Pricing lookup ********************************/
  async pricingByUserAndSubType(reqBody: any, organizationId: any): Promise<any> {
    let { classType, clientUserId, serviceCategoryId, subTypeId, date } = reqBody;

    const targetDate = new Date(date);
    const dayStart = new Date(targetDate); dayStart.setUTCHours(0, 0, 0, 0);
    const dayEnd = new Date(targetDate); dayEnd.setUTCHours(23, 59, 59, 999);

    if (!organizationId) throw new BadRequestException("Organization ID not found");

    let purchasedPackages = await this.purchaseModel.aggregate([
      {
        $match: {
          organizationId: new Types.ObjectId(organizationId),
          consumers: new Types.ObjectId(clientUserId),
          isActive: true,
          $and: [
            { isExpired: { $ne: true } },
            { startDate: { $lte: dayStart }, endDate: { $gte: dayEnd } },
          ],
        }
      },
      {
        $lookup: {
          from: "pricings",
          localField: "packageId",
          foreignField: "_id",
          as: "pricingDetails",
        }
      },
      { $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: true } },
      {
        $match: {
          "pricingDetails.services.type": classType,
          "pricingDetails.services.serviceCategory": new Types.ObjectId(serviceCategoryId),
          "pricingDetails.services.appointmentType": new Types.ObjectId(subTypeId),
        }
      },
      {
        $project: {
          purchaseId: "$_id",
          packageId: "$pricingDetails._id",
          packageName: "$pricingDetails.name",
          sessionCount: {
            $cond: [{ $eq: ["$totalSessions", Infinity] }, "unlimited", "$totalSessions"]
          },
          sessionConsumed: "$sessionConsumed",
          remainingSession: 1,
          isPurchased: { $literal: true },
          sessionType: 1,
          dayPassLimit: 1,
          pricingDetails: "$pricingDetails.services",
        }
      }
    ]).exec();

    // Fallback: available packages if none purchased
    if (purchasedPackages.length === 0) {
      purchasedPackages = await this.PricingModel.aggregate([
        {
          $match: {
            organizationId: new Types.ObjectId(organizationId),
            "services.type": classType,
            isActive: { $ne: false },
            $or: [
              {
                "services.serviceCategory": new Types.ObjectId(serviceCategoryId),
                "services.appointmentType": new Types.ObjectId(subTypeId),
              },
            ],
          }
        },
        {
          $project: {
            packageId: "$_id",
            packageName: "$name",
            sessionCount: {
              $cond: [{ $eq: ["$services.sessionCount", Infinity] }, "unlimited", "$services.sessionCount"]
            },
            sessionConsumed: { $literal: 0 },
            remainingSession: {
              $cond: [{ $eq: ["$services.sessionCount", Infinity] }, "unlimited", "$services.sessionCount"]
            },
            isPurchased: { $literal: false },
            sessionType: 1,
            dayPassLimit: 1,
          }
        }
      ]).exec();
    }

    if (purchasedPackages.length) {
      const dayPassPurchases = purchasedPackages.filter((i: any) => i.sessionType === SessionType.DAY_PASS);
      const dayPassPurchaseIds = dayPassPurchases.map((i: any) => new Types.ObjectId(i._id));

      const sessionsCount = await this.SchedulingModel.aggregate([
        {
          $match: {
            purchaseId: { $in: dayPassPurchaseIds },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
          }
        },
        { $group: { _id: { purchaseId: "$purchaseId", date: "$date" } } }
      ]);

      const sessionsByPurchaseId = sessionsCount.reduce((acc: any, session: any) => {
        const purchaseId = session._id.purchaseId.toString();
        const k = session._id.date.toISOString().split("T")[0];
        if (!acc[purchaseId]) acc[purchaseId] = [];
        acc[purchaseId].push(k);
        return acc;
      }, {});

      const todayUTC = new Date();
      todayUTC.setUTCHours(0, 0, 0, 0);
      const todayISTDate = todayUTC.toISOString().split("T")[0];

      for (const item of purchasedPackages || []) {
        if (item.sessionType === SessionType.DAY_PASS) {
          const bookedDates = sessionsByPurchaseId[item._id?.toString?.()] || [];
          const pastDays = bookedDates.filter((d: string) => d < todayISTDate);
          const consumed = pastDays.length;
          const assigned = item.dayPassLimit || 0;
          item.remainingSession = !isNaN(assigned - consumed) ? `${assigned - consumed} x Day Pass(es)` : 0;
        }
      }
    }

    return purchasedPackages;
  }

  /******************************** Schedule Personal Appointment ********************************/
  async schedulePersonalAppointment(body: any) {
    try {
      const { clientId } = body;
      const clientDetail = await this.ClientModel.findOne({ userId: clientId });
      if (clientDetail?.facilityId) body["facilityId"] = clientDetail.facilityId;
      body["date"] = new Date(body.date);

      const userDetail: any = await this.userModel.findById(clientId).populate("role");
      userDetail.id = userDetail._id;
      let res
      if (body?.classType === ClassType.BOOKINGS) {
        res = await this.schedulingService.scheduleSession(body, userDetail);

      }
      if (body?.classType === ClassType.PERSONAL_APPOINTMENT) {

        res = await this.schedulingService.schedulePersonalAppointment(body, userDetail);
      }
      return res;
    } catch (error: any) {
      throw new InternalServerErrorException(error.message);
    }
  }
  async pricingListByService(pricingListDto: any): Promise<any> {
    const query: any = {
      itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
      $or: [],
    };

    const firstCondition: any = {
      $and: [
        { "services.serviceCategory": new Types.ObjectId(pricingListDto.serviceId) },
      ],
    };
    if (pricingListDto.appointmentId) {
      firstCondition.$and.push({
        "services.appointmentType": new Types.ObjectId(pricingListDto.appointmentId),
      });
    }

    const secondCondition: any = {
      $and: [
        {
          "services.relationShip": {
            $elemMatch: {
              serviceCategory: new Types.ObjectId(pricingListDto.serviceId),
              ...(pricingListDto.appointmentId && {
                subTypeIds: new Types.ObjectId(pricingListDto.appointmentId),
              }),
            },
          },
        },
      ],
    };

    query.$or.push(firstCondition, secondCondition);

    const list: (Omit<Pricing, 'organizationId'> & { organizationId: Pick<Organizations, "isInclusiveofGst"> })[] = await this.PricingModel.find(query, {
      name: 1,
      price: 1,
      tax: 1,
      isActive: 1,
      createdAt: 1,
      services: 1,
      finalPrice: 1

    }).sort({ createdAt: -1 }).populate([
      {
        path: 'organizationId',
        select: 'isInclusiveofGst'
      }
    ]).lean();

    const modifiedList = list.map((item: any) => {
      const isAssigned = item.services.relationShip?.some((relation) => {
        const isServiceMatch = relation.serviceCategory?.toString() === pricingListDto.serviceId;
        const isAppointmentMatch = !pricingListDto.appointmentId || relation.subTypeIds?.some((id) => id.toString() === pricingListDto.appointmentId);
        return isServiceMatch && isAppointmentMatch;
      });

      return {
        ...item,
        finalPrice: item.organizationId.isInclusiveofGst ? item.price : item.price + (item.price * item.tax / 100),
        isInclusiveofGst: item.organizationId.isInclusiveofGst,
        isAssigned: isAssigned || false,
      };
    });

    return modifiedList;
  }
  async sendForgotPasswordLink(body: {
    email?: string;
    mobile?: string;
    organizationId: string;
  }): Promise<{ message: string }> {
    const { email, mobile, organizationId } = body || {};

    if (!organizationId || (!email && !mobile)) {
      throw new BadRequestException('organizationId and (email or mobile) are required');
    }

    const normalizedEmail = email?.trim().toLowerCase();
    const normalizedMobile = mobile?.toString().replace(/\D/g, '');

    const orgId = Types.ObjectId.isValid(organizationId)
      ? new Types.ObjectId(organizationId)
      : organizationId;

    const user = await this.userModel.findOne(
      normalizedEmail
        ? { email: normalizedEmail, organizationId: orgId }
        : { mobile: normalizedMobile, organizationId: orgId },
    ).lean();

    const genericResponse = {
      message:
        'If an account exists for the provided details, a password reset link will be sent shortly.',
    };

    if (!user) return genericResponse;

    const sendToEmail = (user as any).email?.toString().toLowerCase();
    if (!sendToEmail) {

      return genericResponse;
    }



    const otp = await this.generalService.randomOTP();


    await new this.OtpModel({
      otp,
      for: sendToEmail,

    }).save();


    const resetUrl =
      `${this.adminFrontEndHost}widget-set-password` +
      `?uid=${encodeURIComponent(String(otp))}` +
      `&id=${encodeURIComponent(String(user._id))}` +
      `firstName=${user.firstName}&lastName=${user.lastName || ""}`+
      `&email=${encodeURIComponent(sendToEmail)}` +
      `&organizationId=${encodeURIComponent(String(organizationId))}` +
      `&cause=forgot`;
    console.log(resetUrl)
    // await this.mailService.sendMail({
    //   to: sendToEmail,
    //   subject: 'Reset your password',
    //   template: 'password-reset',
    //   context: {
    //     name: (user as any).name || (user as any).firstName || 'there',
    //     email: sendToEmail,
    //     dashboardUrl: resetUrl,
    //   },
    // });

    return genericResponse;
  }
}
