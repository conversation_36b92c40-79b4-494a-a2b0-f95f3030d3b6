import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, NotFoundException, Param, Post, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Types } from "mongoose";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { OrganizationSettingService } from "../services/organization-settings.service";
import { GrantOneSettingOptionsDto, GrantSettingOptionsDto } from "../dto/grant-setting-options.dto";
import { StatusSettingOptionsDto } from "../dto/status-setting-options.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";

@ApiTags("Organization Settings")
@ApiBearerAuth()
@Controller('organization-settings')
export class OrganizationSettingsController {
    constructor(
        private readonly organizationSettingService: OrganizationSettingService,
    ) {}

    // @Post("/grant-all")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    // @ApiOperation({ summary: "Create a new settings option" })
    // @ApiResponse({ status: 201, description: "Settings option created successfully" })
    // async grantAll(@Body() body : GrantSettingOptionsDto, @GetUser() user: any) {
    //     if(!body.organizationId){
    //         throw new BadRequestException({message: "Select a valid organization"})
    //     }
    //     // const data = await this.SettingOptionsService.grantOneSettings(body);
    //     // return {
    //     //     message: "Settings granted successfully",
    //     //     data: data,
    //     // };
    // }

    @Post("/grant-settings/:organizationId")
    // @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 201, description: "Settings option created successfully" })
    async create(
        @Body() body : GrantOneSettingOptionsDto[],
        @Param('organizationId') organizationId : string,
    ) {
        if(!organizationId){
            throw new BadRequestException({message: "Select a valid organization"})
        }
        const data = await this.organizationSettingService.grantSettings(body, organizationId);
        return {
            message: "Settings granted successfully",
            data: data,
        };
    }

    @Post("/get-granted-settings")
    @HttpCode(HttpStatus.OK)
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 201, description: "Settings option created successfully" })
    async getEnabledSettings(
        @Body() body : GrantSettingOptionsDto,
    ) {
        const {page, pageSize: limit} = body;
        if(!body.organizationId){
            throw new BadRequestException({message: "Select a valid organization"})
        }
        const offset = (page - 1) * limit;
        const data = await this.organizationSettingService.findSettings({organizationId: body.organizationId}, {skip:offset, limit: limit});
        const totalPages = Math.ceil(data.count / limit);
        return {
            message: "Settings fetched successfully",
            total: data.count,
            page: page,
            pageSize: limit,
            totalPages: totalPages,
            data: data.data,
        };
    }


    // -- Organization access

    @Post("/get-settings")
    @HttpCode(HttpStatus.OK)
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER)
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 201, description: "Settings option created successfully" })
    async getAllSettings(@Body() body : GrantSettingOptionsDto, @Body('page') page : number, @GetUser() user: any) {
        if(!body.organizationId){
            throw new BadRequestException({message: "Select a valid organization"})
        }
        const data = await this.organizationSettingService.findSettings({organizationId: body.organizationId, isEnabled: true}, {skip:1, limit: 10});
        const totalPages = Math.ceil(data.count / 10);
        return {
            message: "Settings fetched successfully",
            total: data.count,
            page: page,
            pageSize: 10,
            totalPages: totalPages,
            data: data.data,
        };
    }

    @Get("/:settingKey/get-setting")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 200, description: "Settings option created successfully" })
    async getSettings(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Param('settingKey') settingKey : string, @GetUser() user: any
    ) {
        if(!settingKey){
            throw new BadRequestException({message: "Select a valid organization"})
        }
        let data: any = []
        // const organizationId = user._id;
        // data = await this.organizationSettingService.findOneSetting({key: settingKey, organizationId: organizationId}, {skip:0, limit: 1});
        data = await this.organizationSettingService.findOne({key: settingKey, organizationId: organizationId});
        if(!data){
            // throw new NotFoundException({message: "Setting not found"})
            const subSetting = await this.organizationSettingService.findSubSetting({key: settingKey, organizationId: organizationId}, {skip:0, limit: 10});
            data = subSetting.data.length ? subSetting.data[0] : null
        }


        // if(setting.organizationId.toString() != user._id && user.role.type == ENUM_ROLE_TYPE.ORGANIZATION){
        //     throw new BadRequestException({message: "This setting is not enabled for your organization"})
        // }

        return {
            message: "Settings fetched successfully",
            data: data,
        };
    }

    // @Get("/:settingKey/get-subsetting")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    // @ApiOperation({ summary: "Get a sub-setting" })
    // async getSubSettings(@Param('settingKey') settingKey : string, @GetUser() user: any) {
    //     const organizationId = user._id;
    //     if(!settingKey){
    //         throw new BadRequestException({message: "Select a valid organization"})
    //     }
    //     const data = await this.organizationSettingService.findSubSetting({key: settingKey, organizationId: organizationId}, {skip:0, limit: 10});
    //     const setting = data.data.length ? data.data[0] : null
    //     if(!setting){
    //         throw new NotFoundException({message: "Setting not found"})
    //     }

    //     if(setting.organizationId.toString() != user._id && user.role.type == ENUM_ROLE_TYPE.ORGANIZATION){
    //         throw new BadRequestException({message: "This setting is not enabled for your organization"})
    //     }

    //     return {
    //         message: "Settings fetched successfully",
    //         data: setting,
    //     };
    // }


    @Post("/update/:id/status")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Get a sub-setting" })
    async activateSetting(@Param('id') id : string, @Body() body: StatusSettingOptionsDto, @GetUser() user: any) {
        if(!id || !Types.ObjectId.isValid(id)){
            throw new BadRequestException({message: "Select a valid Setting"})
        }
        const data = await this.organizationSettingService.changeStatusOfSetting(id, body.status);
        
        return {
            message: "Settings fetched successfully",
            data: data,
        };
    }

    @Post("/update/subsetting/:subSettingId/status")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Get a sub-setting" })
    async activateSubSetting(@Param('subSettingId') settingId : string, @Body() {status}: StatusSettingOptionsDto, @GetUser() user: any) {
        if(!settingId || !Types.ObjectId.isValid(settingId)){
            throw new BadRequestException({message: "Select a valid Setting"})
        }
        const data = await this.organizationSettingService.changeStatusOfSubSetting(settingId, status);
        
        return {
            message: "Settings fetched successfully",
            data: data,
        };
    }
}
