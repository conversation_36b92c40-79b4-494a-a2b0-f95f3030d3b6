// src/msg91/services/msg91.service.ts
import axios, { AxiosError } from 'axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class Msg91Service {
  private readonly logger = new Logger(Msg91Service.name);

  constructor(
    private readonly authKey: string,
    private readonly templateId: string,
    private readonly BASE_URL: string,
    private readonly MSG91_INTEGRATED_NO: string,
    private readonly MSG91_URL: string,
    private readonly MSG91_NAMESPACE: string,
  ) {}

  async sendOtp(mobile: string,otp:string): Promise<void> {
    const url = this.BASE_URL;
    const formattedMobile = mobile.startsWith('+91') ? mobile : `+91${mobile}`;
    const params = {
      otp: otp,
      otp_length: '6',
      otp_expiry: '5', // in minutes
      template_id: this.templateId,
      mobile: `${formattedMobile}`,
      realTimeResponse: '1',
    };
    const headers = {
      'Content-Type': 'application/json',
      authkey: this.authKey,
    };
    try {
      
      const response = await axios.post(url, {}, { headers, params });
  
      if (response.data.type !== 'success') {
        throw new Error(response.data.message || 'Failed to send OTP');
      }
  
    } catch (error) {
      this.logger.error('MSG91 sendOtp error', error.response?.data || error.message);
      throw new Error(error.response?.data?.message || 'Error sending OTP');
    }
  }
  

async verifyOtp(mobile: string, otp: string): Promise<boolean> {
    const url = `${this.BASE_URL}/verify`;
    const formattedMobile = mobile.startsWith('+91') ? mobile : `+91${mobile}`;
    const params = {
      otp,
      mobile: `${formattedMobile}`,
    };

    const headers = {
      authkey: this.authKey,
    };

    try {
      const response = await axios.get(url, { params, headers });

      const isVerified = response.data?.message === 'OTP verified success';

      return isVerified;
    } catch (error) {
      const errorMsg = error.response?.data?.message || error.message;
      this.logger.error(`MSG91 verifyOtp error for ${mobile}: ${errorMsg}`);
      return false;
    }
  }

  async sendWhatsappTemplateMessage(integratedNumber: string,templateName: string,languageCode: string,toAndComponents: Array<{to: string[],components: Record<string, { type: string, value: string }>}>): Promise<void> {
    const url = `${this.BASE_URL}/whatsapp/whatsapp-outbound-message/bulk/`;
    const payload = {
      integrated_number: integratedNumber,
      content_type: 'template',
      payload: {
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: languageCode,
            policy: 'deterministic'
          },
          to_and_components: toAndComponents
        },
        messaging_product: 'whatsapp'
      }
    };
    const headers = {
      'accept': 'application/json',
      'content-type': 'application/json',
      authkey: this.authKey,
    };

    try {
      const response = await axios.post(url, payload, { headers });
      if (response.data.type !== 'success') {
        throw new Error(response.data.message || 'Failed to send WhatsApp message');
      }
    } catch (error) {
      this.logger.error('MSG91 sendWhatsappTemplateMessage error', error.response?.data || error.message);
      throw new Error(error.response?.data?.message || 'Error sending WhatsApp message');
    }
  }

  async sendTemplateMessage(to: string[], templateName: string, bodyValues: Record<string, string>): Promise<any> {

    try {
      if (!to || to.length === 0) {
        throw new HttpException('Recipient list cannot be empty', HttpStatus.BAD_REQUEST);
      }

      if (!templateName) {
        throw new HttpException('Template name is required', HttpStatus.BAD_REQUEST);
      }

      if (!this.authKey) {
        throw new HttpException('MSG91 auth key not configured', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      const apiUrl = `${this.MSG91_URL}whatsapp/whatsapp-outbound-message/bulk/`;


      const payload = {
        integrated_number: this.MSG91_INTEGRATED_NO,
        content_type: 'template',
        payload: {
          messaging_product: 'whatsapp',
          type: 'template',
          template: {
            name: templateName,
            language: { code: 'en', policy: 'deterministic' },
            namespace: this.MSG91_NAMESPACE,
            to_and_components: [
              {
                to: to,
                components: bodyValues,
              },
            ],
          },
        },
      };


      const response = await axios.post(apiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          authkey: this.authKey,
        },
      });

      return response.data;
    } catch (error) {
      const err = error as AxiosError;

      if (err.response) {
        throw new HttpException(
          err.response.data || 'MSG91 API Error',
          err.response.status || HttpStatus.BAD_GATEWAY,
        );
      }

      throw new HttpException(
        err.message || 'Unknown error while sending WhatsApp message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
