import { faker } from '@faker-js/faker';
import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { DatabaseDto } from 'src/common/database/dtos/database.dto';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { RolePermissionDto } from 'src/role/dtos/role.permission.dto';

export class RoleGetResponseDto extends DatabaseDto {

    
    @ApiProperty({
        description: 'Name of role',
        example: faker.person.jobTitle(),
        required: true,
    })
    name: string;

    @ApiProperty({
        description: 'Description of role',
        example: faker.lorem.sentence(),
        required: false,
    })
    description?: string;

    @ApiProperty({
        description: 'Active flag of role',
        example: true,
        required: true,
    })
    isActive: boolean;

    @ApiProperty({
        description: 'Representative for role type',
        example: ENUM_ROLE_TYPE.ORGANIZATION,
        required: true,

        enum: ENUM_ROLE_TYPE,
    })
    type: ENUM_ROLE_TYPE;

    @ApiProperty({
        type: RolePermissionDto,
        oneOf: [{ $ref: getSchemaPath(RolePermissionDto) }],
        required: true,

        isArray: true,
        default: [],
    })
    @Type(() => RolePermissionDto)
    permissions: RolePermissionDto[];
}
