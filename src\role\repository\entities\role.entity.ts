import { Schema, Types } from 'mongoose';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import {
    DatabaseEntity,
    DatabaseProp,
    DatabaseSchema,
} from 'src/common/database/decorators/database.decorator';
import { IDatabaseDocument } from 'src/common/database/interfaces/database.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PolicyEntity } from 'src/policy/repository/entities/policy.entity';

export const RoleTableName = 'roles';

@DatabaseEntity({ collection: RoleTableName })
export class RoleEntity extends DatabaseEntityBase {
    @DatabaseProp({
        required: true,
        index: true,
        trim: true,
        maxlength: 30,
        type: String,
    })
    name: string;

    @DatabaseProp({
        required: false,
        trim: true,
        type: String,
    })
    description?: string;

    @DatabaseProp({
        required: true,
        default: true,
        index: true,
        type: Boolean,
    })
    isActive: boolean;

    @DatabaseProp({
        required: false,
        default: false,
        index: true,
        type: Boolean,
    })
    isDefault?: boolean;

    @DatabaseProp({
        required: true,
        index: true,
        unique: true,
        maxlength: 30,
        enum: ENUM_ROLE_TYPE,
    })
    type: ENUM_ROLE_TYPE

    @DatabaseProp({
        required: false,
        ref: PolicyEntity.name,
        type: [Schema.Types.ObjectId],
        default: [],
    })
    policies?: Types.ObjectId[];

    // @DatabaseProp({
    //     required: false,
    //     ref: PolicyEntity.name,
    //     type: [Schema.Types.ObjectId],
    //     default: [],
    // })
    // delegatedPolicies?: Types.ObjectId[];
}

export const RoleSchema = DatabaseSchema(RoleEntity);
export type RoleDocument = IDatabaseDocument<RoleEntity>;

RoleSchema.pre('save', function(next) {
    const role = this;
    const policiesSet = new Set(role.policies?.map(p => p.toString()));
    // const delegatedPoliciesSet = new Set(role.delegatedPolicies?.map(p => p.toString()));

    // Remove duplicates from policies and delegatedPolicies
    role.policies = [...new Set(role.policies)];
    // role.delegatedPolicies = [...new Set(role.delegatedPolicies)];

    // const intersection = new Set([...policiesSet].filter(x => delegatedPoliciesSet.has(x)));
    const intersection = new Set([...policiesSet]);
    
    // if (intersection.size > 0) {
    //     next(new Error('Policies and Delegated Policies should not contain the same permissions.'));
    // } else {
    //     next();
    // }
    next()
});
