import { Controller, Post, Body, Get, Query } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger";
import { PurchaseService } from "../services/purchase.service";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { FamilyShareClientListPublicDto, SharePackagePublicDto } from "../dto/share-package.dto";
import { PaginationQuery } from "src/common/pagination/decorators/pagination.decorator";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from "src/utils/decorators/pagination-query.decorator";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { GetUser } from "src/auth/decorators/get-user.decorator";

@ApiTags("modules.purchase")
@ApiBearerAuth()
@Controller("public/purchase")
export class PurchasePublicController {
    constructor(
        private readonly purchaseService: PurchaseService,
        private readonly paginationService: PaginationService
    ) { }

    @ApiOperation({ summary: "Share package to multiple users for users" })
    @Response("purchase.sharePackage")
    @Post("/share-package")
    @AuthJwtAccessProtected()
    async sharePackage(
        @Body() body: SharePackagePublicDto,
        @GetUser('_id') userId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const result = await this.purchaseService.sharePackage(organizationId, {
            ...body,
            shareFrom: userId.toString()
        });
        return {
            data: result,
        };
    }

    @ApiOperation({ summary: "Share package to multiple users for users" })
    @ResponsePaging("purchase.sharePackage")
    @Get("/share-package/client-list")
    @AuthJwtAccessProtected()
    async shareClientList(
        @PaginationQuery({
            defaultOrderBy: 'name',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC,
            availableOrderBy: ['name'],
            availableSearch: ['name']
        }) { _limit, _search, _offset, _order }: FamilyShareClientListPublicDto,
        @Query() body: FamilyShareClientListPublicDto,
        @GetUser('_id') userId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {

        const filter: Record<string, any> = {
            _id: body.purchaseId,
            userId: userId,
            organizationId,
            isActive: true,
            isExpired: false,
            endDate: { $gte: new Date() },
            ..._search
        }

        const result = await this.purchaseService.familyShareClientList(filter, _limit, _offset, _order);
        return {
            _pagination: {
                total: result.total,
                totalPage: this.paginationService.totalPage(result.total, _limit),
                page: body.page,
                pageSize: body.pageSize,
            },
            data: result.data,
        };
    }

}