import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Clients } from "src/users/schemas/clients.schema";
import { CreateCustomPackageDto, CustomPackageListDto, UpdateCustomPackageDto } from "src/customPackage/dto/custom-package.dto";
import { CustomPackage } from "src/customPackage/schemas/custom-package.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { OrganizationService } from "src/organization/services/organization.service";

@Injectable()
export class CustomPackageService {
    constructor(
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        @InjectModel(CustomPackage.name) private customPackageModel: Model<CustomPackage>,
        private readonly transactionService: TransactionService,
        private readonly organizationService: OrganizationService,
    ) { }

    private async getOrganizationId(user: any): Promise<any> {
        const { role } = user;
        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staff = await this.StaffProfileModel.findOne({ userId: user.id }).exec();
                organizationId = staff.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break;
            default:
                throw new BadRequestException("Access Denied");
        }
        if (organizationId) {
            return organizationId;
        }
        throw new BadRequestException("Access Denied");
    }

    async createCustomPackage(createCustomPackageDto: CreateCustomPackageDto, organizationId: IDatabaseObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const newPackage = new this.customPackageModel({
                organizationId,
                facilityId: createCustomPackageDto.facilityId,
                quantity: createCustomPackageDto.quantity,
                name: createCustomPackageDto.name,
                unitPrice: createCustomPackageDto.unitPrice,
                total: createCustomPackageDto.total,
                tax: createCustomPackageDto.tax,
                hsnOrSacCode: createCustomPackageDto.hsnOrSacCode,
                discount: createCustomPackageDto.discount,
                isTaxable: createCustomPackageDto.isTaxable,
            });
            await newPackage.save();
            await this.transactionService.commitTransaction(session);
            return { message: "Custom package created successfully", data: newPackage };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateCustomPackage(updateCustomPackageDto: UpdateCustomPackageDto, organizationId: IDatabaseObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {


            const packageDetails = await this.customPackageModel.findOneAndUpdate(
                { _id: updateCustomPackageDto.packageId },
                {
                    $set: {
                        organizationId,
                        facilityId: updateCustomPackageDto.facilityId,
                        quantity: updateCustomPackageDto.quantity,
                        name: updateCustomPackageDto.name,
                        unitPrice: updateCustomPackageDto.unitPrice,
                        total: updateCustomPackageDto.total,
                        tax: updateCustomPackageDto.tax,
                        hsnOrSacCode: updateCustomPackageDto.hsnOrSacCode,
                        discount: updateCustomPackageDto.discount,
                        isTaxable: updateCustomPackageDto.isTaxable,
                    },
                },
                { new: true, session },
            );

            if (!packageDetails) throw new BadRequestException("Custom Package does not exist");

            await this.transactionService.commitTransaction(session);
            return { message: "Custom Package updated successfully", data: packageDetails };
        } catch (error) {
            console.error("Error updating custom package:", error);
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getCustomPackageList(organizationId: IDatabaseObjectId, customPackageListDto: CustomPackageListDto): Promise<{ data: any[] }> {
        const { facilityId } = customPackageListDto;
        try {
            const isInclusiveofGst = await this.organizationService.checkIsInclusiveGst(organizationId);
            const data = await this.customPackageModel.find({ facilityId }).sort({ createdAt: -1 }).lean();
            data.forEach((item: any) => {
                item.finalPrice = isInclusiveofGst ? item.unitPrice : item.unitPrice + (item.unitPrice * item.tax / 100);
                item.isInclusiveofGst = isInclusiveofGst;
            });
            return { data };
        } catch (error) {
            throw new BadRequestException(error?.message || "Failed to fetch custom package list");
        }
    }

    async getCustomPackageDetails(organizationId: IDatabaseObjectId, packageId: IDatabaseObjectId): Promise<Record<string, any>> {
        const isInclusiveofGst = await this.organizationService.checkIsInclusiveGst(organizationId);
        let data: any = await this.customPackageModel.findOne({ _id: packageId }).lean();
        if (!data) throw new NotFoundException("Custom package not found");
        data.finalPrice = isInclusiveofGst ? data.unitPrice : data.unitPrice + (data.unitPrice * data.tax / 100);
        data.isInclusiveofGst = isInclusiveofGst;
        return { message: "Custom package fetched successfully", data };

    }
}
