import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes, Types } from 'mongoose';
import { Attributes } from 'src/utils/enums/attribute.enum';
import { ProductVariant } from './product-variant.schema';
import { ProductAttribute, ProductAttributeSchema } from './product-attribute.schema';

export type ProductDocument = HydratedDocument<Product>;

export enum ProductType {
  SIMPLE = 'simple',
  VARIABLE = 'variable',
}

@Schema({ timestamps: true })
export class Product {
  @Prop({ required: true, type: String, index: 'text' })
  name: string;

  // NOTE: Do NOT put `unique: true` here — uniqueness is enforced by the compound index below
  @Prop({ required: true, type: String })
  itemCode: string;

  // Optional (org + sku) uniqueness is enforced by the compound index below, not here
  @Prop({ required: true, type: String })
  sku: string;

  @Prop({ required: true, type: String })
  slug: string;

  @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Category', index: true })
  firstCategoryId: Types.ObjectId;

  @Prop({ type: SchemaTypes.ObjectId, required: false, ref: 'Category', index: true })
  secondCategoryId?: Types.ObjectId | null;

  @Prop({ type: String, required: true })
  hsn: string;

  @Prop({ type: String })
  mfgCode?: string;

  @Prop({ type: Number, required: true })
  gst: number;

  @Prop({ type: String })
  introduction?: string;

  @Prop({ type: String })
  description?: string;

  @Prop({ required: true, type: String, enum: ProductType })
  type: ProductType;

  @Prop({ type: [ProductAttributeSchema], default: [] })
  attributes: ProductAttribute[];

  // IMPORTANT: fix ref name and typing to ProductVariant model
  @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: 'ProductVariant' }], index: true, default: [] })
  variantIds: Types.ObjectId[];

  // Store variant attribute keys (e.g., size, color). Keep as string enum values.
  @Prop({ type: [String], enum: Object.values(Attributes), default: [] })
  variantAttributesList: Attributes[];

  // Default true to match your service behavior
  @Prop({ type: Boolean, default: true })
  status: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: false })
  createdBy?: Types.ObjectId;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'User', required: true, index: true })
  organizationId: Types.ObjectId;

  // Not persisted; just a convenience for populated variants
  populatedVariants?: ProductVariant[];

  @Prop({
    type: SchemaTypes.ObjectId,
    required: false,
    default: null,
    ref: 'Organization.revenueCategory',
  })
  revenueCategory?: Types.ObjectId | null;
  @Prop({ type: String, trim: true, })
  migrationId?: string; 
}

export const ProductSchema = SchemaFactory.createForClass(Product);

/**
 * COMPOUND UNIQUE INDEXES (per-organization uniqueness)
 * - itemCode must be unique within the same organization
 * - sku    must be unique within the same organization (optional but recommended)
 *
 * If you previously had global unique indexes on itemCode/sku, make sure to drop them:
 *   db.products.dropIndex("itemCode_1")
 *   db.products.dropIndex("sku_1")
 * Then let these build.
 */
ProductSchema.index(
  { organizationId: 1, itemCode: 1 },
  { unique: true, name: 'uniq_itemCode_per_org' },
);

ProductSchema.index(
  { organizationId: 1, sku: 1 },
  { unique: true, name: 'uniq_sku_per_org' },
);

/** ---- Duplicate key error handling with clear messages ---- */
function duplicateKeyMessage(err: any): string {
  // Mongo duplicate key error
  const kp = err?.keyPattern || {};
  const kv = err?.keyValue || {};

  if (kp.organizationId && kp.itemCode) {
    return `Item code '${kv.itemCode}' already exists within this organization.`;
  }
  if (kp.organizationId && kp.sku) {
    return `SKU '${kv.sku}' already exists within this organization.`;
  }
  return 'Duplicate value violates a unique constraint.';
}

const duplicateHandler = function (error: any, _doc: any, next: any) {
  if (error?.name === 'MongoServerError' && error?.code === 11000) {
    return next(new Error(duplicateKeyMessage(error)));
  }
  return next(error);
};

ProductSchema.post('save', duplicateHandler);

