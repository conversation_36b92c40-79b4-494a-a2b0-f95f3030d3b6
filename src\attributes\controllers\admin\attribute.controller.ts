import { BadRequestException, Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { AttributeService } from "../../services/attribute.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AttributeDto } from "../../dto/attribute.dto";
import { AttributeStatusDto } from "../../dto/attribute-status.dto";
import { AttributeListDto } from "../../dto/attribute-list.dto";
import { AttributeType } from "src/utils/enums/attribute-type.enum";
import { AttributeListFilterDto } from "../../dto/attribute-list-filter.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";

@ApiTags("Admin-Attributes")
@ApiBearerAuth()
@Controller("admin/attribute")
export class AttributeController {
    constructor(private attributeService: AttributeService) { }

    @Post("/create")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute create" })
    async createAttribute(@GetUser() user, @Body() createAttributeDto: AttributeDto): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.createAttribute(createAttributeDto, user);
        return {
            message: "Attribute created",
            data: output,
        };
    }

    @Get("/parentAttributes")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Parent Attributes" })
    async parentAttribute(): Promise<{ message: String; data: any }> {
        return {
            message: "Parent Attributes",
            data: AttributeType,
        };
    }

    @Post("/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute Listing" })
    async attributeList(@GetUser() user, @Body() attributeListDto: AttributeListDto): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.attributeListing(attributeListDto, user);
        return {
            message: "Attribute listing",
            data: output,
        };
    }

    @Post("/filter")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute Listing by it's type" })
    async attributeListByType(@GetUser() user, @Body() attributeFilterDto: AttributeListFilterDto): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.attributeListByType(attributeFilterDto, user);
        return {
            message: "Attribute listing by it's type",
            data: output,
        };
    }

    @Patch("/update/:attributeId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute Update" })
    async attributeUpdate(@GetUser() user, @Param("attributeId") attributeId: string, @Body() updateAttributeDto: AttributeDto): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.attributeUpdate(updateAttributeDto, attributeId, user);
        return {
            message: "Attribute update",
            data: output,
        };
    }

    @Patch("/updateStatus/:attributeId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute Status Update" })
    async attributeSUpdate(@GetUser() user, @Param("attributeId") attributeId: string, @Body() updateAttributeStatusDto: AttributeStatusDto): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.attributeStatusUpdate(updateAttributeStatusDto, attributeId, user);
        return {
            message: "Attribute Status update",
            data: output,
        };
    }

    @Get("/details/:attributeId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Fetch attribute details by Id" })
    async getAttributeDetailsById(@GetUser() user, @Param("attributeId") attributeId: string): Promise<any> {
        const data = await this.attributeService.attributeDetailsById(attributeId, user);
        return data;
    }

    @Delete("/delete/:attributeId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Attribute deleted" })
    async attributeDelete(@GetUser() user, @Param("attributeId") attributeId: string): Promise<{ message: String; data: any }> {
        let output = await this.attributeService.attributeDelete(attributeId, user);
        return {
            message: "Attribute deleted",
            data: output,
        };
    }
}
