import { DynamicModule, Global, Module, forwardRef } from '@nestjs/common';
import { SessionService } from 'src/session/services/session.service';
import { SessionRepositoryModule } from './repository/session.repository.module';
import { BullModule } from '@nestjs/bullmq';
import { ENUM_WORKER_QUEUES } from 'src/worker/enums/worker.enum';
import { UsersModule } from 'src/users/users.module';
import { SessionActiveGuard } from './guards/session.active.guard';
import { CachingModule } from 'src/common/caching/caching.module';

@Global()
@Module({})
export class SessionModule {
    static forRoot(): DynamicModule {
        return {
            module: SessionModule,
            imports: [
                forwardRef(() => UsersModule),
                SessionRepositoryModule,
                CachingModule,
                BullModule.registerQueueAsync({
                    name: ENUM_WORKER_QUEUES.SESSION_QUEUE,
                }),
            ],
            providers: [
                SessionService,
                SessionActiveGuard
            ],
            exports: [
                SessionService,
                SessionActiveGuard
            ],
            controllers: [],
        };
    }
}
