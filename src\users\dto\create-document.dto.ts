import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsString } from "class-validator";

export class CreateDocumentDto {

    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Facility ID is required" })
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;

    @ApiProperty({ description: "Name of the document", example: "Aadhar Card" })
    @IsString({ message: "Document name must be a string" })
    @IsNotEmpty({ message: "Document name is required" })
    documentName: string;

    @ApiProperty({ description: "File location (S3 URL)", example: "https://bucket.s3.amazonaws.com/file.png" })
    @IsString({ message: "File must be a string" })
    @IsNotEmpty({ message: "File is required" })
    file: string;

    @ApiProperty({ description: "Client User ID", example: "64e0b25d8c92a0a23c45d8f1" })
    @IsMongoId({ message: "Invalid User ID format" })
    userId: string;
}