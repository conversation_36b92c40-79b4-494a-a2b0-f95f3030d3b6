import { ApiProperty } from "@nestjs/swagger";
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsEnum,
    IsMongoId,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Validate,
    ValidateIf,
    ValidateNested,
    ValidationArguments,
    ValidatorConstraintInterface,
} from "class-validator";
import { Type } from "class-transformer";
import { PaymentStatus } from "src/utils/enums/payment.enum";
import { Types } from "mongoose";

export class IsValidIndianDenominationConstraint implements ValidatorConstraintInterface {
    private readonly validDenominations = [2000, 500, 200, 100, 50, 20, 10, 5, 2, 1];

    validate(denominations: Record<number, number>): boolean {
        return Object.keys(denominations).every((key) => this.validDenominations.includes(Number(key)));
    }

    defaultMessage(args: ValidationArguments): string {
        return `Denominations must only include valid Indian notes or coins: ${this.validDenominations.join(", ")}`;
    }
}

export class PaymentDetailsDTO {
    @ApiProperty({
        description: "Payment method used for the purchase",
        example: "VISA",
        required: true,
    })
    @IsString()
    @IsNotEmpty({ message: "Payment method is required" })
    paymentMethod: string;

    @ApiProperty({
        description: "payment Method Id ",
        required: false,
        example: new Types.ObjectId().toString(),
    })
    @IsMongoId({ message: "PaymentMethodId" })
    @Type(() => String)
    paymentMethodId: string;

    @ApiProperty({
        description: "Transaction ID for the payment",
        example: "txn_123456",
        required: false,
    })
    @IsString()
    @IsOptional()
    transactionId?: string;

    @ApiProperty({
        description: "Amount paid for the package",
        example: 1000,
        required: true,
    })
    @IsNumber()
    @IsNotEmpty({ message: "Amount is required" })
    amount: number;

    @ApiProperty({
        description: "Date of the payment",
        example: new Date().toISOString(),
        required: true,
    })
    @IsDate({ message: "Payment date must be a valid date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Payment date is required" })
    paymentDate: Date;

    @ApiProperty({
        description: "Payment status",
        example: "completed",
        enum: PaymentStatus,
        required: true,
    })
    @IsString()
    @IsEnum(PaymentStatus, {
        message: "Payment status must be one of ['pending', 'completed', 'failed']",
    })
    paymentStatus: string;

    @ApiProperty({
        description: "Payment gateway used",
        example: "Stripe",
        required: false,
    })
    @IsString()
    @IsNotEmpty({ message: "Payment gateway is required if the payment method is not CASH" })
    paymentGateway?: string;

    @ApiProperty({
        description: "Additional description for the payment",
        example: "Payment for January subscription",
        required: false,
    })
    @IsString()
    description?: string;

    @ApiProperty({
        description: "Denominations as key-value pairs (e.g., { 100: 5, 500: 2 })",
        example: { 100: 5, 500: 2 },
        required: false, // Conditionally required
    })
    @IsOptional()
    @Validate(IsValidIndianDenominationConstraint, { message: "Invalid denomination detected" })
    denominations?: Record<number, number>;
}

export class CreateInvoicePurchaseDto {
    @ApiProperty({
        description: "User ID",
        example: "66d83c7db6e7eb2e195211bd",
    })
    @IsString()
    @IsNotEmpty({ message: "User ID is required" })
    userId: string;

    @ApiProperty({
        description: "Organization ID",
        example: "66cea29781ee8034250895aa",
    })
    @IsString()
    @IsNotEmpty({ message: "Organization ID is required" })
    organizationId: string;

    @ApiProperty({
        description: "Facility ID",
        example: "66ceffc7a13db5380edb06b1",
    })
    @IsString()
    @IsNotEmpty({ message: "Facility ID is required" })
    facilityId: string;

    @ApiProperty({
        description: "Array of purchase items with packageId and quantity",
        example: [
            { packageId: "677f78dbc6a56b77d8ae0743", quantity: 2, isBundledPricing: true },
            { packageId: "67823eddab0a9e0f1c733eae", quantity: 1, isBundledPricing: false },
        ],
    })
    @IsOptional()
    purchaseItems: {
        packageId: string;
        quantity: number;
        isBundledPricing?: boolean;
    }[];

    @ApiProperty({
        description: "Array of Product items with productId and productVariantId and quantity",
    })
    @IsOptional()
    productsItem: [];
    @ApiProperty({
        description: "Array of Custom Package items with packageId  and quantity",
    })
    @IsOptional()
    customPackageItems: {
        customPackageId: string;
        quantity: number;
    }[];

    @ApiProperty({
        description: "Array of returned purchase IDs",
        example: [
            "677f78dbc6a56b77d8ae0743",
            "67823eddab0a9e0f1c733eae"
        ],
        required: false,
    })
    @IsOptional()
    returnPurchaseIds?: string[];

    @ApiProperty({
        description: "Total value of returned purchases",
        example: 500,
        required: false,
    })
    @IsNumber()
    @IsOptional()
    returnTotal?: number;

    @ApiProperty({
        description: "cart Discount applied",
        example: 50,
        required: false,
    })
    @IsNumber()
    cartDiscount?: number;

    @ApiProperty({
        description: "cart Discount type",
        example: "percentage/Flat",
        required: false,
    })
    @IsString()
    cartDiscountType?: string;

    @ApiProperty({
        description: "Subtotal amount",
        example: 950,
        required: true,
    })
    @IsNumber()
    @IsNotEmpty({ message: "Subtotal is required" })
    subTotal: number;

    @ApiProperty({
        description: "Total amount",
        example: 1040,
        required: true,
    })
    @IsNumber()
    @IsNotEmpty({ message: "Total is required" })
    total: number;

    @ApiProperty({
        description: "Total amount paid",
        example: 1040,
        required: false,
    })
    @IsNumber()
    amountPaid: number;

    @ApiProperty({
        description: "Purchased on platform",
        example: "Web",
        required: true,
    })
    @IsString()
    @IsNotEmpty({ message: "Platform is required" })
    platform: string;

    @ApiProperty({
        description: "Payment details for the purchase",
        required: false,
        type: [PaymentDetailsDTO],
    })
    @ValidateNested({ each: true })
    @Type(() => PaymentDetailsDTO)
    @IsOptional()
    @IsArray()
    paymentDetails: PaymentDetailsDTO[];
    validateDenominations() {
        if (!this.isSplittedPayment) {
            this.paymentDetails?.forEach((detail, index) => {
                if (detail.paymentMethod === "cash" && (!detail.denominations || Object.keys(detail.denominations).length === 0)) {
                    throw new Error(`Denominations are required for cash payment at index ${index} when split payment is disabled.`);
                }
            });
        }
    }

    @ApiProperty({ description: "The payment is splitted or not.", example: true })
    @IsBoolean()
    @IsOptional()
    @Type(() => Boolean)
    isSplittedPayment?: boolean;

    @ApiProperty({
        description: "Billing address id of client",
        required: false,
        example: new Types.ObjectId().toString(),
    })
    @IsMongoId({ message: "Please select address from list" })
    @Type(() => String)
    billingAddressId: string;

    //  @ApiProperty({
    //   description: "GST details",
    //   example: { cgst: 45, sgst: 45, igst: 0 },
    //   required: true,
    // })
    // @ValidateNested()
    // @Type(() => Object)
    // gst: {
    //   cgst: number;
    //   sgst: number;
    //   igst: number;
    // };

    // @ApiProperty({
    //   description: "Currency used",
    //   example: "INR",
    //   required: true,
    // })
    // @IsString()
    // @IsNotEmpty({ message: "Currency is required" })
    // currency: string;

    // @ApiProperty({
    //   description: "Invoice date",
    //   example: new Date().toISOString(),
    //   required: true,
    // })
    // @IsDate({ message: "Invoice date must be a valid date" })
    // @Type(() => Date)
    // @IsNotEmpty({ message: "Invoice date is required" })
    // invoiceDate: Date;

    // @ApiProperty({
    //   description: "Purchase date",
    //   example: new Date().toISOString(),
    //   required: true,
    // })
    // @IsDate({ message: "Purchase date must be a valid date" })
    // @Type(() => Date)
    // @IsNotEmpty({ message: "Purchase date is required" })
    // purchaseDate: Date;

    // @ApiProperty({
    //   description: "Status of the package purchase",
    //   example: PackagePurchaseStatus.ACTIVE,
    //   enum: PackagePurchaseStatus,
    // })
    // @IsString()
    // @IsEnum(PackagePurchaseStatus, { message: "Invalid status value" })
    // @IsNotEmpty({ message: "Status is required" })
    // status: PackagePurchaseStatus;

    // Nested Payment Details
}
