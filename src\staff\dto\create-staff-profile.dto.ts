import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, Length, IsNotEmpty, MaxLength, IsEnum, IsMongoId, IsArray, ArrayNotEmpty, IsObject, IsOptional } from "class-validator";
import { Transform } from "class-transformer";
import { Gender } from "src/utils/enums/gender.enum";
import { Types } from "mongoose";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

export class CreateStaffDto {
    @ApiProperty({
        description: "Organization id",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Required valid organization details" })
    organizationId: string;

    @ApiProperty({
        description: "The IDs of the Branches where the trainer is assigned.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41ff"],
        required: true,
    })
    @IsArray({ message: "FacilityId must be an array" })
    @ArrayNotEmpty({ message: "FacilityId array must not be empty" })
    @IsMongoId({ each: true, message: "Each FacilityId must be a valid ObjectId" })
    facilityId: Types.ObjectId[];

    @ApiProperty({
        description: "First Name of the Staff.",
        example: "HKS",
        minLength: 2,
        maxLength: 50,
    })
    @Length(2, 50, { message: "FirstName must be between 2 and 50 characters long." })
    @IsNotEmpty({ message: "FirstName is required." })
    firstName: string;

    @ApiProperty({
        description: "Last Name of the Staff.",
        example: "Abhay",
        minLength: 2,
        maxLength: 50,
    })
    @IsOptional()
    lastName?: string;

    @ApiProperty({
        description: "The Staff email is required",
        example: "<EMAIL>",
        maxLength: 255,
        required: true,
    })
    @IsEmail({}, { message: "Email must be a valid email address." })
    @IsNotEmpty({ message: "Email is required." })
    @MaxLength(255, { message: "Email cannot exceed 255 characters." })
    email: string;

    @ApiProperty({
        description: "The staff mobile number is required.",
        example: "9876543210",
        // minLength: 10,
        // maxLength: 10,
        required: true,
    })
    @IsNotEmpty({ message: "Mobile number is required." })
    //@Length(10, 10, { message: "Mobile number must be exactly 10 digits." })
    mobile: string;

    @ApiProperty({ description: "Gender of the Staff", enum: Gender, example: Gender.MALE })
    @IsNotEmpty({ message: "Gender is required." })
    @IsEnum(Gender, { message: "Gender must be a valid Gender enum value" })
    gender: Gender;

    @ApiProperty({ description: "Role of the Staff", enum: ENUM_ROLE_TYPE, example: new Types.ObjectId() })
    @IsNotEmpty({ message: "Role is required." })
    @IsMongoId({ message: "Role must be valid" })
    role: string;

    @ApiProperty({ description: "Date to setup account", example: new Date().toISOString() })
    @IsNotEmpty({ message: "Account Setup Email Date is required" })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    setUpDate: Date;
}
