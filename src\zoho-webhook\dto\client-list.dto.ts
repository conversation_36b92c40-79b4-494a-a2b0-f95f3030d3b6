import { ApiProperty } from "@nestjs/swagger";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { IsMongoId, IsNotEmpty, IsOptional, IsString,IsBoolean } from "class-validator";

export class ClientLeadListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Knox",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search: string;

    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;
}
