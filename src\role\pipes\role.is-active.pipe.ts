import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { ENUM_ROLE_STATUS_CODE_ERROR } from 'src/role/enums/role.status-code.enum';
import { RoleDocument } from 'src/role/repository/entities/role.entity';

@Injectable()
export class RoleIs<PERSON>ctivePipe implements PipeTransform {
    private readonly isActive: boolean[];

    constructor(isActive: boolean[]) {
        this.isActive = isActive;
    }

    async transform(value: RoleDocument): Promise<RoleDocument> {
        if (!this.isActive.includes(value.isActive)) {
            throw new BadRequestException({
                statusCode: ENUM_ROLE_STATUS_CODE_ERROR.IS_ACTIVE,
                message: 'role.error.isActiveInvalid',
            });
        }

        return value;
    }
}
