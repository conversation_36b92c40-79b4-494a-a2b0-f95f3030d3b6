import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import {
    PromotionEntity,
    PromotionSchema,
} from 'src/promotions/repository/entities/promotion.entity';
import { PromotionRepository } from 'src/promotions/repository/repositories/promotion.repository';
import {
    PromotionItemEntity,
    PromotionItemSchema,
} from 'src/promotions/repository/entities/promotion-item.entity';
import { PromotionItemRepository } from 'src/promotions/repository/repositories/promotion-item.repository';

@Module({
    providers: [PromotionRepository, PromotionItemRepository],
    exports: [PromotionRepository, PromotionItemRepository],
    controllers: [],
    imports: [
        MongooseModule.forFeature(
            [
                {
                    name: PromotionEntity.name,
                    schema: PromotionSchema,
                },
                {
                    name: PromotionItemEntity.name,
                    schema: PromotionItemSchema,
                },
            ],
            DATABASE_PRIMARY_CONNECTION_NAME
        ),
    ],
})
export class PromotionRepositoryModule {}
