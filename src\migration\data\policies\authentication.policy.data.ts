import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const authenticationPolicies: IDefaultPolicies[] = [
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_CUSTOMER_ONBOARDING],
        description: 'Grant view-only access to customer onboarding',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_CUSTOMER_ONBOARDING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_CUSTOMER_ONBOARDING],
        description: 'Grant full access to customer onboarding',
        isActive: true,
        module: <PERSON><PERSON><PERSON>_POLICY_MODULE.AUTHENTICATION,
        type: <PERSON><PERSON>M_POLICY_TYPE.AUTHENTICATION_CUSTOMER_ONBOARDING,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_STAFF_ONBOARDING],
        description: 'Grant view-only access to staff onboarding',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_STAFF_ONBOARDING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_STAFF_ONBOARDING],
        description: 'Grant full access to staff onboarding',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_STAFF_ONBOARDING,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
    //
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_BRANCH_ONBOARDING],
        description: 'Grant view-only access to branch onboarding',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_BRANCH_ONBOARDING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_BRANCH_ONBOARDING],
        description: 'Grant full access to branch onboarding',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_BRANCH_ONBOARDING,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_PERMISSIONS],
        description: 'Grant view-only access to permissions',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_PERMISSIONS,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.AUTHENTICATION_PERMISSIONS],
        description: 'Grant full access to permissions',
        isActive: true,
        module: ENUM_POLICY_MODULE.AUTHENTICATION,
        type: ENUM_POLICY_TYPE.AUTHENTICATION_PERMISSIONS,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    }
];