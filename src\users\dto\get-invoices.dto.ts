import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsEnum, IsOptional, <PERSON>, <PERSON>, ValidateIf } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PaymentStatus } from "src/utils/enums/payment.enum";


export class GetInvoicesDto {
    @ApiProperty({
        description: "Search input for user search",
        type: String,
        example: "John",
    })
    @IsOptional()
    search?: string;

    @ApiProperty({
        description: "Filter by payment status",
        type: String,
        example: PaymentStatus.PENDING,
    })
    @IsOptional()
    @IsEnum(PaymentStatus)
    paymentStatus?: string;

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    facilityId?: string[] = [];

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @IsOptional()
    @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    classType?: ClassType;

    @ApiProperty({
        description: "Appointment Id any package is selected for the appointment",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    serviceCategory?: string[] = [];

    @ApiProperty({
        description: "Start date range",
        example: new Date(),
    })
    @ValidateIf((req) => !!req.endDate)
    @Type(() => Date)
    @Transform(({value}) => {
        if (value) return value;
        const now = new Date();
        return new Date(now.getFullYear(), now.getMonth(), 1); // First day of current month
    })
    startDate?: Date;

    @ApiProperty({
        description: "End date range", 
        example: new Date(),
    })
    @ValidateIf((req) => !!req.startDate)
    @Type(() => Date)
    @Transform(({value}) => value || new Date()) // Current date/time if not provided
    @ValidateIf((req) => {
        if (!req.startDate || !req.endDate) return true;
        const diffInMs = new Date(req.endDate).getTime() - new Date(req.startDate).getTime();
        const diffInYears = diffInMs / (1000 * 60 * 60 * 24 * 365);
        return diffInYears <= 1;
    })
    endDate?: Date;

    @ApiProperty({
        description: "Page number",
        type: Number,
        example:1
    })
    @IsOptional()
    @Transform(({value})=>value||1)
    @Type(()=>Number)
    @Min(1)
    page?: number = 1;

    @ApiProperty({
        description: "Page number",
        type: Number,
        example:1
    })
    @Transform(({value})=>value || 10)
    @Type(()=> Number)
    @Min(10)
    @Max(50)
    pageSize?: number = 10;


}