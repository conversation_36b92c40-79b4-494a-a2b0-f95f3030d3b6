{"list": "Users retrieved successfully", "get": "User details fetched successfully", "create": "New user created successfully", "update": "User updated successfully", "delete": "User deleted successfully", "inactive": "User marked as inactive", "active": "User marked as active", "error": {"inactive": "User is inactive", "exist": "A user with this email or mobile number already exists", "notFound": "Sorry, we couldn't find the requested user", "isSelf": "Cannot perform action on self"}}