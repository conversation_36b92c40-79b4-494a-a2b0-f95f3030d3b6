import { Body, Controller, FileTypeValidator, Get, MaxFileSizeValidator, Param, ParseFilePipe, Post, UploadedFile, UseGuards, UseInterceptors } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { FileInterceptor } from "@nestjs/platform-express";
import { GeneralService } from "../services/general.service";
import { UploadService } from "../services/upload.service";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { StatePaginationDTO } from "src/users/dto/state-pagination.dto";
import { AnyArn } from "aws-sdk/clients/groundstation";
import { GetCityNameDto } from "../dto/get-city.dto";

@ApiTags("General")
@ApiBearerAuth()
@Controller("general")
export class GeneralController {
    constructor(private generalService: GeneralService, private uploadService: UploadService) {}

    @Post("/upload-image")
    @UseGuards(AuthGuard())
    @UseInterceptors(FileInterceptor("image"))
    @ApiConsumes("multipart/form-data")
    @ApiOperation({ summary: "Upload Image" })
    @ApiBody({
        schema: {
            type: "object",
            properties: {
                image: {
                    type: "string",
                    format: "binary",
                },
            },
        },
    })
    async uploadProfilePicture(
        @UploadedFile(
            new ParseFilePipe({
                validators: [new FileTypeValidator({ fileType: ".(png|jpeg|jpg|webp|png)" }), new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 5 })],
            }),
        )
        image: Express.Multer.File,
    ): Promise<any> {
        try {
            return await this.uploadService.uploadImage(image);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Post("/states")
    @UseGuards(AuthGuard())
    @ApiOperation({ summary: "Get all states with pagination and search" })
    async getStates(@Body() paginationDTO: PaginationDTO) {
        try {
            return await this.generalService.getStates(paginationDTO);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Post("get/cityDetails")
    @UseGuards(AuthGuard())
    @ApiOperation({ summary: "Get cities details" })
    async getCitiesByStateId(@Body() paginationDTO: StatePaginationDTO,) {
        try {
            return await this.generalService.getCitiesByStateId(paginationDTO);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Get('/getCityName')
    @UseGuards(AuthGuard())
    @ApiOperation({ summary: 'Get city name by cityId' })
    async getCityNameById(@Body() req:GetCityNameDto): Promise<any> {
        try {
            const name = await this.generalService.getCityNameById(req);
            return { name };
        } catch (error) {
            throw new Error(error.message);
        }
    }
}
