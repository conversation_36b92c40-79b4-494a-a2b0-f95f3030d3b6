import { Injectable, NestMiddleware } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { JwtPayload } from "src/auth/interface/jwt-payload.interface";
import { Facility } from "src/facility/schemas/facility.schema";
import { User } from "src/users/schemas/user.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

@Injectable()
export class SocketAuthMiddlewareService implements NestMiddleware {
    constructor(
        private readonly jwtService: JwtService,
        private readonly configService: ConfigService,
        @InjectModel(User.name) private readonly userModel: Model<User>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
    ) {}

    // async use(socket: any, next: (err?: any) => void) {
    //     try {
    //         const token = socket.handshake.auth?.authorization;

    //         if (!token) {
    //             console.log("Missing token:", token);
    //             socket.emit("invalid_token", { message: "Invalid or missing token" });
    //             return next(new Error("Invalid token"));
    //         }

    //         const roles = Object.values(RoleType);
    //         const payload: JwtPayload = this.jwtService.verify(token, {
    //             secret: this.configService.get<string>("JWT_SECRET"),
    //         });

    //         if (!payload?.userId) {
    //             socket.emit("auth_error", { message: "Invalid token payload" });
    //             return next(new Error("Invalid token payload"));
    //         }

    //         const user = await this.userModel.findById(payload.userId);
    //         if (!user) {
    //             socket.emit("auth_error", { message: "User not found" });
    //             return next(new Error("User not found"));
    //         }

    //         socket.roleIncluded = roles.toString().includes(user["role"].toString());
    //         socket.user = user;
    //         return next(); // Proceed normally if everything is fine
    //     } catch (error) {
    //         console.error("Socket authentication failed:", error);
    //         socket.emit("auth_error", { message: "Authentication failed: Invalid token" });
    //         return next(new Error("Authentication failed"));
    //     }
    // }
    async use(socket: any, next: (err?: any) => void) {
        try {
            const facilityId = socket?.handshake?.query?.facilityId as string;

            if (!facilityId) {
                //console.log("Missing facilityId in connection request in auth.");
                socket.emit("invalid_facility", { message: "Facility ID is missing." });
                return next(new Error("Facility ID Missing"));
            }

            if (!Types.ObjectId.isValid(facilityId)) {
                //console.log("Invalid facilityId format in auth.");
                socket.emit("invalid_facility", { message: "Invalid facility ID format." });
                return next(new Error("Invalid Facility ID Format"));
            }

            const facility = await this.FacilityModel.findById(facilityId);
            if (!facility) {
                //console.log("Facility not found in auth.");
                socket.emit("facility_not_found", { message: "Facility not found." });
                return next(new Error("Facility Not Found"));
            }

            socket.facility = facility; // Attach valid facility data to socket
            return next(); // Proceed normally
        } catch (error) {
            console.error("Facility validation failed:", error);
            socket.emit("facility_error", { message: "Facility validation failed." });
            return next(new Error("Facility validation failed"));
        }
    }
}

