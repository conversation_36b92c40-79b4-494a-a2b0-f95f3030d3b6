import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
    <PERSON>,
    DocAuth,
    DocRequest,
    DocGuard,
    DocResponse,
    DocResponsePaging,
} from 'src/common/doc/decorators/doc.decorator';
import { ENUM_DOC_REQUEST_BODY_TYPE } from 'src/common/doc/enums/doc.enum';
import { DatabaseIdResponseDto } from 'src/common/database/dtos/response/database.id.response.dto';
// import { PromotionListResponseDto } from '../dto/response/promotion.list.response.dto';
// import { PromotionGetResponseDto } from '../dto/response/promotion.get.response.dto';
import { CreatePromotionDto } from '../dto/create-promotion.dto';
import { UpdatePromotionDto } from '../dto/update-promotion.dto';
import { ApplyPromotionDto, ApplyPromotionToItemsDto } from '../dto/apply-promotion.dto';
import { GetPromotionPricingDto } from '../dto/get-promotion.pricing.dto';

export function PromotionCreateDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Create a new promotion',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            bodyType: ENUM_DOC_REQUEST_BODY_TYPE.JSON,
            dto: CreatePromotionDto,
        }),
        DocGuard({ policy: true }),
        DocResponse<DatabaseIdResponseDto>('promotion.create', {
            httpStatus: HttpStatus.CREATED,
            dto: DatabaseIdResponseDto,
        })
    );
}

export function PromotionListDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Get all promotions',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            queries: [
                {
                    name: 'page',
                    allowEmptyValue: true,
                    required: false,
                    type: 'number',
                    example: 1,
                },
                {
                    name: 'limit',
                    allowEmptyValue: true,
                    required: false,
                    type: 'number',
                    example: 10,
                },
                {
                    name: 'pricingId',
                    allowEmptyValue: true,
                    required: false,
                    type: 'string',
                    example: '60d21b4667d0d8992e610c85',
                    description: 'Filter by pricing ID (returns promotions that include or exclude this pricing)'
                },
                {
                    name: 'includePricing',
                    allowEmptyValue: true,
                    required: false,
                    type: 'boolean',
                    example: 'true',
                    description: 'Whether to include or exclude the pricing ID (true = include, false = exclude)'
                },
                {
                    name: 'includedPricingIds',
                    allowEmptyValue: true,
                    required: false,
                    type: 'string',
                    example: '60d21b4667d0d8992e610c85,60d21b4667d0d8992e610c86',
                    description: 'Filter promotions that include these pricing IDs (comma-separated)'
                },
                {
                    name: 'excludedPricingIds',
                    allowEmptyValue: true,
                    required: false,
                    type: 'string',
                    example: '60d21b4667d0d8992e610c85,60d21b4667d0d8992e610c86',
                    description: 'Filter promotions that exclude these pricing IDs (comma-separated)'
                },
            ],
        }),
        DocGuard({ policy: true }),
        // DocResponsePaging<PromotionListResponseDto>('promotion.list', {
        //     dto: PromotionListResponseDto,
        // })
    );
}

export function PromotionGetDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Get promotion by ID',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            params: [
                {
                    name: 'id',
                    allowEmptyValue: false,
                    required: true,
                    type: 'string',
                    example: '6500a0d646234234234234',
                },
            ],
        }),
        DocGuard({ policy: true }),
        // DocResponse<PromotionGetResponseDto>('promotion.get', {
        //     dto: PromotionGetResponseDto,
        // })
    );
}

export function PromotionUpdateDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Update a promotion',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            params: [
                {
                    name: 'id',
                    allowEmptyValue: false,
                    required: true,
                    type: 'string',
                    example: '6500a0d646234234234234',
                },
            ],
            bodyType: ENUM_DOC_REQUEST_BODY_TYPE.JSON,
            dto: UpdatePromotionDto,
        }),
        DocGuard({ policy: true }),
        DocResponse<DatabaseIdResponseDto>('promotion.update', {
            dto: DatabaseIdResponseDto,
        })
    );
}

export function PromotionDeleteDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Delete a promotion',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            params: [
                {
                    name: 'id',
                    allowEmptyValue: false,
                    required: true,
                    type: 'string',
                    example: '6500a0d646234234234234',
                },
            ],
        }),
        DocGuard({ policy: true }),
        DocResponse('promotion.delete')
    );
}

export function PromotionApplyDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Apply promotion to items',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            params: [
                {
                    name: 'itemId',
                    allowEmptyValue: false,
                    required: true,
                    type: 'string',
                    example: '6500a0d646234234234234',
                },
            ],
            bodyType: ENUM_DOC_REQUEST_BODY_TYPE.JSON,
            dto: ApplyPromotionToItemsDto,
        }),
        DocGuard({ policy: true }),
        DocResponse('promotion.apply')
    );
}

export function PromotionGetByTypeDoc(): MethodDecorator {
    return applyDecorators(
        Doc({
            summary: 'Get promotions grouped by item type',
        }),
        DocAuth({
            jwtAccessToken: true,
        }),
        DocRequest({
            params: [
                GetPromotionPricingDto
            ],
        }),
        DocGuard({ policy: true }),
        DocResponse('promotion.getByType')
    );
}