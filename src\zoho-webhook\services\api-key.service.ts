import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { webHookAPIkey } from '../schema/api-key.schema';
import { CreateApiKeyDto } from '../dto/create_apiKey.dto';

@Injectable()
export class ApiKeyService {
  constructor(
    @InjectModel(webHookAPIkey.name)
    private readonly apiKeyModel: Model<webHookAPIkey>,
  ) { }

  generateRawKey(): string {
    return `${crypto.randomBytes(24)
      .toString('base64')
      .replace(/[^a-zA-Z0-9]/g, '')
      .slice(0, 32)}`;
  }

  hashKey(rawKey: string): string {
    return crypto.createHash('sha256').update(rawKey).digest('hex');
  }

  async generateAndStoreApiKey(createApiKeyDto?: CreateApiKeyDto): Promise<{ rawKey: string }> {
    try {
      const rawKey = this.generateRawKey();
      const hashedKey = this.hashKey(rawKey);

      const savedKey = await this.apiKeyModel.create({
        rawKey,
        hashedKey,
        status: 'active',
        facilityId: createApiKeyDto?.facilityId,
        organizationId: createApiKeyDto?.organizationId,
        platform: 'Zoho',
      });

      return {
        rawKey: rawKey,

      };
    }
    catch (error) {
      console.log(error)
    }
  }
}
