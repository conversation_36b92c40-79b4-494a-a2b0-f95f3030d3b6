// src/inventory/services/inventory-template-export.service.ts
import { Injectable, BadRequestException } from '@nestjs/common';
import * as XLSX from 'xlsx';

type FileType = 'csv' | 'xlsx';
type Row = Record<string, any>;

/**
 * Exports rows in the EXACT shape/order expected by the inventory CSV import.
 * Headers: productName, variantTitle, productSku, productType, variantSku, mrp, salePrice, quantity, expiryDate
 */
@Injectable()
export class InventoryTemplateExportService {
  async generateExportFile(
    rows: Row[],                // output of inventoryService.getProductsAsInventoryTemplate(...)
    fileType: FileType,
    timezone?: string           // reserved (dates are YYYY-MM-DD)
  ): Promise<Buffer> {
    const { headers, mapper } = this.getColumnConfig();
    const mapped = rows.map((r) => mapper(r));

    switch (fileType) {
      case 'csv':
        return this.makeCsvBuffer(headers, mapped);
      case 'xlsx':
        return this.makeXlsxBuffer(headers, mapped);
      default:
        throw new BadRequestException(`Unsupported fileType: ${fileType}`);
    }
  }

  getMimeType(fileType: FileType): string {
    return fileType === 'xlsx'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8';
  }

  getDefaultExtension(fileType: FileType): string {
    return fileType === 'xlsx' ? 'xlsx' : 'csv';
  }

  /**
   * Single source of truth for import template headers + normalization.
   * Keep this order in sync with your importer.
   */
  private getColumnConfig(): {
    headers: { key: string; label: string }[];
    mapper: (row: Row) => Row;
  } {
    const headers = [
      { key: 'productName',  label: 'productName' },
      { key: 'variantTitle', label: 'variantTitle' },
      { key: 'productSku',   label: 'productSku' },
      { key: 'productType',  label: 'productType' },   // simple | variable
      { key: 'variantSku',   label: 'variantSku' },
      { key: 'mrp',          label: 'mrp' },
      { key: 'quantity',     label: 'quantity' },
      { key: 'expiryDate',   label: 'expiryDate' },    // YYYY-MM-DD
    ];

    const toStr = (v: any) => (v === null || v === undefined ? '' : String(v));
    const toNumOrBlank = (v: any) => {
      const n = Number(v);
      return Number.isFinite(n) ? n : '';
    };
    const toDateYmd = (v: any) => {
      if (!v) return '';
      const d = new Date(v);
      return Number.isNaN(+d) ? '' : d.toISOString().slice(0, 10);
    };

    // Map only known keys; coerce null/undefined → ''
    const mapper = (row: Row) => ({
      productName:  toStr(row.productName),
      variantTitle: toStr(row.variantTitle),
      productSku:   toStr(row.productSku),
      productType:  toStr(row.productType).toLowerCase() === 'variable' ? 'variable' : 'simple',
      variantSku:   toStr(row.variantSku),
      mrp:          toNumOrBlank(row.mrp),
      salePrice:    toNumOrBlank(row.salePrice),
      quantity:     toNumOrBlank(row.quantity),
      expiryDate:   toDateYmd(row.expiryDate),
    });

    return { headers, mapper };
  }

  // -------- CSV (buffer) --------
  private makeCsvBuffer(headers: { key: string; label: string }[], rows: Row[]): Buffer {
    const esc = (v: any) => {
      if (v === null || v === undefined) return '';
      const s = typeof v === 'string' ? v : String(v);
      return /[",\n]/.test(s) ? `"${s.replace(/"/g, '""')}"` : s;
    };

    const head = headers.map((h) => esc(h.label)).join(',');
    const body = rows.map((r) => headers.map((h) => esc(r[h.key])).join(',')).join('\n');
    // Add UTF-8 BOM for Excel compatibility
    const csv = head + '\n' + body + (body ? '\n' : '');
    const BOM = '\uFEFF';
    return Buffer.from(BOM + csv, 'utf-8');
  }

  // -------- XLSX (buffer) --------
  private makeXlsxBuffer(headers: { key: string; label: string }[], rows: Row[]): Buffer {
    const aoa = [
      headers.map((h) => h.label),
      ...rows.map((r) => headers.map((h) => r[h.key])),
    ];
    const ws = XLSX.utils.aoa_to_sheet(aoa);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' }) as Buffer;
  }
}
