import { ApiProperty, PickType } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsNumber } from "class-validator";

export class SharePassDto {
    @ApiProperty({
        description: "Id of the associated client.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid Client Id" })
    shareFrom: string;

    @ApiProperty({
        description: "Id of the associated client.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid Client Id" })
    shareTo: string;

    @ApiProperty({
        description: "Id of the associated package.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid Package Id" })
    purchaseId: string;

    @ApiProperty({
        description: "Id of the organization.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid Organization Id" })
    organizationId: string;

    @ApiProperty({
        description: "Number of sessions for the appointment",
        type: Number,
        example: 3,
        required: true,
    })
    @IsNotEmpty()
    @IsNumber()
    noOfSessions: number;
}

export class ShareVoucherDto extends PickType(SharePassDto, ['shareFrom', 'shareTo', 'purchaseId'] as const) { }
