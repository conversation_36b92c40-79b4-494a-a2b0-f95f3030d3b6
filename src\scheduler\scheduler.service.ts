import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON> } from "@nestjs/schedule";
import { PurchaseService } from "src/users/services/purchase.service";

@Injectable()
export class SchedulerService {
	constructor(
		private readonly purchaseService: PurchaseService,
	) { }

	private async notifyExpiringMemberships() {
		try {
			await this.purchaseService.sendMailForExpiringMemberships();
			console.log("Notification emails sent for expiring memberships.");
		} catch (err) {
			console.error("Error sending expiring membership emails:", err);
		}
	}

	@Cron("0 11 * * *")
	// @Cron("* * * * *")
	async cronToNotifyExpiringMembership() {
		console.log("Cron running to notify expiring memberships...");
		await this.notifyExpiringMemberships();
	}
}
