import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";
import { DiscountType } from "src/utils/enums/discount.enum";
export type CustomPackageDocument = HydratedDocument<CustomPackage>;

export class Discount {
    @Prop({ enum: DiscountType, required: false })
    type?: string;

    @Prop({ required: false, type: Number })
    value?: number;
}

@Schema({ timestamps: true })
export class CustomPackage {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Organization" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: Number, required: true })
    unitPrice: Number;

    @Prop({ type: Number, required: true })
    total: Number;

    @Prop({ type: Number, required: true })
    quantity: Number;

    @Prop({ type: Number, required: true })
    tax: Number;

    @Prop({ type: String, required: false })
    hsnOrSacCode: string;

    @Prop({ type: Discount, required: false })
    discount?: Discount;

    @Prop({ type: Boolean, default: true })
    isActive: Boolean;

    @Prop({ type: Boolean, default: true })
    isTaxable: Boolean;
}

export const CustomPackageSchema = SchemaFactory.createForClass(CustomPackage);
