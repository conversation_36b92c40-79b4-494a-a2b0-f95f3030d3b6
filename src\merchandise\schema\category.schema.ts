import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document,Types } from 'mongoose';


export enum CategoryStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum CategoryLevel {
  FIRST = "first",
  SECOND = "second",
  THIRD = "third",
}
@Schema({ timestamps: true })
export class Category extends Document {
  @Prop({ required: true, type: String })
  name: String;
  @Prop({ type: String, enum: CategoryLevel, required: true, index: true })
  level: String;
  @Prop({ type: SchemaTypes.ObjectId, ref: "Category", index: true })
  parentId: String;
  @Prop({ required: false, default: false, type: Boolean })
  isFeaturedCategory: Boolean;
  @Prop({ required: false, default: false, type: Boolean })
  isBestSelling: Boolean;
  @Prop({ required: true, enum: CategoryStatus, default: CategoryStatus.ACTIVE })
  status: String;
  @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: "Category" }] })
  children: String[];
  populatedChildren: Category[];
  @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
  createdBy: string;
  @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
  organizationId: string;
}
export type CategoryDocument = Category & Document;
export const CategorySchema = SchemaFactory.createForClass(Category);

