// src/merchandise/bulk/bulk-upload.worker.ts
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Processor, WorkerHost, InjectQueue } from '@nestjs/bullmq';
import { Job, Queue } from 'bullmq';
import { rkeys } from './redis-keys';
import { ProductService } from '../services/product.service';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

const CHUNK = 200;
const BLOCK_MS = 2000;
const ROW_TTL_SEC = 7 * 24 * 3600;

@Injectable()
@Processor('product-upload-queue-redis', { concurrency: 2 })
export class BulkUploadWorker extends WorkerHost {
  private readonly logger = new Logger(BulkUploadWorker.name);

  constructor(
    private readonly productService: ProductService,
    @Inject('REDIS') private readonly redis: any,
    @InjectQueue('product-upload-queue-redis') private readonly queue: Queue,
  ) {
    super();
  }

  async process(job: Job<{ batchId: string }>): Promise<void> {
    if (job.name && job.name !== 'process-upload-redis') {
      this.logger.debug(`Skipping job '${job.name}'`);
      return;
    }

    const { batchId } = job.data;
    const k = rkeys(batchId);
    const consumer = `w:${process.pid}:${Math.random().toString(36).slice(2)}`;

    this.logger.log(`⏳ Start processing batch ${batchId}`);

    const batch: any = await this.productService.getUploadBatch(batchId);
    if (!batch) {
      this.logger.warn(`Batch ${batchId} not found; skipping`);
      return;
    }

    await this.productService.markBatchProcessing(batchId);

    try { await this.redis.xGroupCreate(k.stream, k.group, '0', { MKSTREAM: true }); } catch {}

    // keep three separate counters
    let consumedSum = Number(batch.processedCount || 0); // rows consumed
    let successSum  = Number(batch.successCount   || 0);
    let failedSum   = Number(batch.failedCount    || 0);

    const readChunk = async (id: '0' | '>') =>
      this.redis.xReadGroup(k.group, consumer, [{ key: k.stream, id }], { COUNT: CHUNK, BLOCK: BLOCK_MS });

    const getTotals = async () => {
      const stats = await this.redis.hGetAll(k.stats);
      return {
        total:     Number(stats.total || stats.enqueued || 0),
        processed: Number(stats.processed || 0), // will mean "consumed rows"
        failed:    Number(stats.failed || 0),
      };
    };

    for (;;) {
      const t0 = await getTotals();
      if (t0.total > 0 && t0.processed >= t0.total) break;

      let resp = await readChunk('0'); // drain pending
      if (!resp || !resp[0]?.messages?.length) resp = await readChunk('>'); // read new

      if (!resp || resp.length === 0) {
        const t1 = await getTotals();
        if (t1.total > 0 && t1.processed >= t1.total) break;
        else continue;
      }

      const messages = resp[0]?.messages ?? [];
      if (!messages.length) continue;

      const rows: any[] = [];
      const msgIds: string[] = [];
      const rowKeys: string[] = [];

      for (const m of messages) {
        const rowKey = m.message.rowKey as string;
        if (!rowKey) continue;
        rowKeys.push(rowKey);
        msgIds.push(m.id);

        const status = await this.redis.hGet(rowKey, 'status');
        if (status && status !== 'PENDING') continue;

        const obj = await this.redis.hGetAll(rowKey);
        if (obj && Object.keys(obj).length) {
          delete (obj as any).status;
          for (const key of Object.keys(obj)) obj[key] = obj[key] == null ? '' : String(obj[key]);
          rows.push(obj);
        }
      }

      if (!rows.length) {
        await this.redis.xAck(k.stream, k.group, ...msgIds);
        continue;
      }

      try {
        const orgIdStr = typeof batch.organizationId === 'string'
          ? batch.organizationId
          : String(batch.organizationId || '');
        const pseudoUser = { _id: orgIdStr, role: { type: ENUM_ROLE_TYPE.ORGANIZATION } };

        const result = await this.productService.bulkUploadProductsBatch(rows, pseudoUser, !!batch.isUpdate, batchId);

        const succ = Number(result.success || 0);
        const fail = Number(result.failed  || 0);
        const consumedNow = rows.length;

        // update local sums
        consumedSum += consumedNow;
        successSum  += succ;
        failedSum   += fail;

        // IMPORTANT: processed == rows consumed
        await this.redis.hIncrBy(k.stats, 'processed', consumedNow);
        // failed is separate (kept for diagnostics)
        await this.redis.hIncrBy(k.stats, 'failed', fail);

    // errors list (sanitize + spread)
if (Array.isArray(result.errors) && result.errors.length) {
  const payloads = result.errors
    .filter(Boolean)
    .map((e: any) => JSON.stringify({
      lineNumber: e?.lineNumber ?? e?.line ?? '',
      message: e?.message ?? String(e ?? ''),
      field: e?.field ?? '',
      sku: e?.sku ?? '',
      value: e?.value ?? '',
      at: new Date().toISOString(),
    }));

  if (payloads.length) {
    await this.redis.rPush(k.errors, ...payloads); // spread, not array as single item
    await this.redis.expire(k.errors, ROW_TTL_SEC);
  }
}



        // mark rows consumed
        const pipe = this.redis.multi();
        for (const rk of rowKeys) {
          pipe.hSet(rk, { status: 'CONSUMED' });
          pipe.expire(rk, ROW_TTL_SEC);
        }
        await pipe.exec();

        // persist to Mongo (store all three counters)
        await this.productService.updateBatchProgress(batchId, {
          processedCount: consumedSum,
          successCount:   successSum,
          failedCount:    failedSum,
          status: 'processing',
        } as any);

        await this.redis.xAck(k.stream, k.group, ...msgIds);

        const { total, processed } = await getTotals();
        const pct = total > 0 ? Math.round((processed / total) * 100) : 0;
        this.logger.log(`✅ Batch ${batchId}: +${succ} success, +${fail} failed, +${consumedNow} consumed (progress ${pct}%)`);
        await job.updateProgress(pct);
      } catch (err) {
        this.logger.error(`❌ Batch ${batchId} chunk failed`, err as any);
        await this.redis.xAck(k.stream, k.group, ...msgIds);
        await this.productService.failBatch(batchId, String((err as any)?.message || err));
        return;
      }
    }

    await this.ackAllPending(k.stream, k.group, consumer);

    const stats = await this.redis.hGetAll(k.stats);
    const finalProcessed = Number(stats?.processed || consumedSum); // consumed rows
    const finalFailed    = Number(stats?.failed    || failedSum);
    const totalRows      = Number(stats?.total     || stats?.enqueued || finalProcessed);

    // Always mark COMPLETED
    await this.productService.finalizeBatch(batchId, {
      totalRows,
      processedRows: finalProcessed,
      processedCount: finalProcessed,
      successCount: successSum,
      failedCount: finalFailed,
      status: 'completed',
    } as any);

    await this.flushBatchRedis(batchId);

    if (String(process.env.BULK_OBLITERATE_QUEUE || '').toLowerCase() === 'true') {
      await this.obliterateQueueIfEmpty();
    }

    await job.updateProgress(100);
    this.logger.log(`🏁 Finished batch ${batchId}: consumed=${finalProcessed}, success=${successSum}, failed=${finalFailed}`);
  }

  private async ackAllPending(stream: string, group: string, consumer: string, chunk = 500) {
    try {
      let start = '0-0';
      for (;;) {
        const res: any = await this.redis.xAutoClaim(stream, group, consumer, 0, start, { COUNT: chunk });
        const next = Array.isArray(res) ? res[0] : res?.nextStart || '0-0';
        const entries = Array.isArray(res) ? res[1] : res?.messages || [];
        if (!entries || entries.length === 0) break;
        const ids = entries.map((e: any) => e?.id ?? e?.[0] ?? e?.messageId).filter(Boolean);
        if (ids.length) await this.redis.xAck(stream, group, ...ids);
        start = next || '0-0';
        if (entries.length < chunk) break;
      }
      return;
    } catch { /* fallback below */ }

    try {
      for (;;) {
        const list: any[] = await (this.redis.xPendingRange
          ? this.redis.xPendingRange(stream, group, '-', '+', chunk)
          : this.redis.sendCommand(['XPENDING', stream, group, '-', '+', String(chunk)])
        );
        if (!list || list.length === 0) break;
        const ids = list.map((e: any) => (Array.isArray(e) ? e[0] : e?.id)).filter(Boolean);
        if (ids.length) await this.redis.xAck(stream, group, ...ids);
        if (ids.length < chunk) break;
      }
    } catch { /* ignore */ }
  }

  private async flushBatchRedis(batchId: string) {
    const k = rkeys(batchId);
    try { await this.redis.xGroupDestroy(k.stream, k.group); } catch {}
    try { await this.redis.del(k.stream); } catch {}
    try { await this.redis.del(k.stats); } catch {}
    // try { await this.redis.del(k.errors); } catch {}

    const pattern = `batch:${batchId}:row:*`;
    let cursor: any = '0';
    do {
      const res: any = await this.redis.scan(cursor, { MATCH: pattern, COUNT: 1000 });
      cursor = res?.cursor ?? res?.[0] ?? '0';
      const keys: string[] = res?.keys ?? res?.[1] ?? [];
      if (keys.length) {
        try { await this.redis.unlink(...keys); } catch { await this.redis.del(...keys); }
      }
    } while (cursor !== '0');
  }

  private async obliterateQueueIfEmpty() {
    try {
      const counts = await this.queue.getJobCounts('waiting','delayed','active','paused');
      const pendingTotal =
        Number(counts.waiting || 0) +
        Number(counts.delayed || 0) +
        Number(counts.active || 0) +
        Number(counts.paused || 0);

      const repeatables = await (this.queue.getRepeatableJobs?.() ?? Promise.resolve([]));
      if (pendingTotal === 0 && (repeatables?.length ?? 0) === 0) {
        await this.queue.obliterate({ force: true });
        this.logger.log(`🧹 BullMQ queue '${this.queue.name}' obliterated`);
      }
    } catch (e) {
      this.logger.warn(`Skipped obliterate for '${this.queue.name}': ${String(e)}`);
    }
  }
}
