import { BadRequestException, Body, Controller, Post, UseGuards, Query, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { PaymentService } from '../service/payment.service';
import { CreatePaymentCredentialDto } from '../dto/create-payment-credential.dto'
import { CapturePaymentDto } from "../dto/capture-payment.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";

@ApiTags("Payment-link")
@ApiBearerAuth()
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) { }

  @Post('generate-link')
  @ApiOperation({ summary: "Create a Payment Link" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async create(@Body('amount') amount: number) {
    return this.paymentService.createPayment(amount);
  }

  @Get('status')
  async status(@Query('linkId') linkId: string) {
    return this.paymentService.getStatus(linkId);
  }

  @ApiOperation({ summary: "save the credential for the payment gateway" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @Post("/save-credential")
  async savePaymentCredential(@Body() createPaymentCredentialDto: CreatePaymentCredentialDto, @GetUser() user: any) {
    const response = await this.paymentService.savePaymentCredential(createPaymentCredentialDto, user);
    return {
      message: "Payment credential saved successfully",
      data: response,
    }
  }
  @ApiOperation({ summary: "change the status of the payment from authorization to capture payment" })
  //  @PolicyAbilityRoleProtected()
  //  @AuthJwtAccessProtected()
  @Post('razorpay/capture')
  async capturePayment(@Body() capturePaymentDto: CapturePaymentDto) {
    const captureReponse = await this.paymentService.capturePayment(capturePaymentDto);
    return {
      message: "Payment captured successfully",
      data: captureReponse
    }
  }

  @ApiOperation({ summary: "Get the Api key of the payment gateway" })
  @AuthJwtAccessProtected()
  @Post('/get-razorpay-apikey')
  async razorPayApikey(
    @GetOrganizationId() organizationId: IDatabaseObjectId
  ) {
    const apiKeyResponse = await this.paymentService.fetchRazorPayApiKey(organizationId);
    return {
      message: "Razorpay API Key fetched successfully",
      data: apiKeyResponse
    }
  }
}
