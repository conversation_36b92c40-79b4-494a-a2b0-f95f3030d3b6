import { ApiProperty, ApiHideProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsEnum, IsMongoId, IsNumber, IsOptional, IsString, ValidateIf, ValidateNested } from "class-validator";
import { PaymentDetailsDTO } from "src/users/dto/packages-purchasing.dto";
import { PaymentStatus } from "src/utils/enums/payment.enum";

export class UpdatePaymentStatusDto {
    @ApiProperty({
        description: "InvoiceId must be a string",
        example: true,
    })
    @IsString()
    @IsMongoId({ message: "InvoiceId is not valid" })
    invoiceId: string;

    @ApiProperty({
        description: "Status must be a string",
        example: true,
    })
    @IsString({ message: "Status must be a string" })
    @IsEnum(PaymentStatus)
    status: PaymentStatus;

    @ApiProperty({
        description: "Total amount paid",
        example: 1040,
        required: false,
    })
    @IsNumber()
    amountPaid: number;

    @ApiProperty({ description: "The payment is splitted or not.", example: true })
    @IsBoolean()
    @IsOptional()
    @Type(() => Boolean)
    isSplittedPayment?: boolean;

    @ApiHideProperty()
    @ApiProperty({
        description: "Status must be a string",
        example: true,
    })
    @IsOptional()
    @IsString({ message: "Status must be a string" })
    method?: string;

    @ApiProperty({
        description: "Payment details for the purchase",
        required: false,
        type: [PaymentDetailsDTO],
    })
    @ValidateNested()
    @Type(() => PaymentDetailsDTO)
    @ValidateIf((o) => o.status === PaymentStatus.COMPLETED)
    paymentDetails: PaymentDetailsDTO[];
}
