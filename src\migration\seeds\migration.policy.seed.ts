import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { PolicyService } from 'src/policy/services/policy.service';
import { authenticationPolicies } from '../data/policies/authentication.policy.data';
import { schedulingPolicies } from '../data/policies/scheduling.policy.data';
import { posPolicies } from '../data/policies/pos.policy.data';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';
import { servicesPolicies } from '../data/policies/services.policy';
import { orderPolicies } from '../data/policies/order.policy';
import { settingPolicies } from '../data/policies/settings.policy.data';
import { reportPolicies } from '../data/policies/report.policy.data';
import { TransactionService } from 'src/utils/services/transaction.service';
import { purchasePolicies } from 'src/migration/data/policies/purchase.policy';


@Injectable()
export class MigrationPolicySeed {
    constructor(
        private readonly policyService: PolicyService,
        private readonly startTransaction: TransactionService,
        // private readonly permissionService: PermissionService,
    ) {}

    private readonly defaultPolicies = [
        ...authenticationPolicies,
        ...posPolicies,
        ...servicesPolicies,
        ...schedulingPolicies,
        ...orderPolicies,
        ...settingPolicies,
        ...reportPolicies,
        ...purchasePolicies,
    ];

    async createPolicyDoc(policy: IDefaultPolicies, session?: any) {
        
        const updateData = {
            ...policy,
            isActive: true
        };

        const existingPolicy = await this.policyService.findOne(
            {
                type: policy.type,
                action: policy.action
            }
        );

        if (existingPolicy) {
            return this.policyService.update(
                existingPolicy,
                updateData,
                { session }
            );
        } else {
            return this.policyService.create(
                updateData,
                { session }
            );
        }

    }

    @Command({
        command: 'seed:policy',
        describe: 'Seed policies',
    })
    async seeds(): Promise<void> {
        try {
            // const session = await this.startTransaction.startTransaction();
            // try {
            //     for (const policy of this.defaultPolicies) {
            //         await this.createPolicyDoc(policy, session);
            //     }
                await Promise.all(
                    this.defaultPolicies.map(async (policy) => {
                        return await this.createPolicyDoc(policy);
                    })
                );
            //     await this.startTransaction.commitTransaction(session);
            // } catch (error) {
            //     await this.startTransaction.abortTransaction(session);
            //     throw error;
            // } finally {
            //     session.endSession();
            // }
        } catch (err: any) {
            throw new Error(err);
        }
    }

    @Command({
        command: 'remove:policy',
        describe: 'Remove policies',
    })
    async remove(): Promise<void> {
        try {
            await this.policyService.deleteMany({});
        } catch (err: any) {
            throw new Error(err);
        }
    }
}
