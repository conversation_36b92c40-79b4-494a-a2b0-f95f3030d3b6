import { Body, Controller, Get, Param, Post, UseGuards } from "@nestjs/common";
import { CourseService } from "../services/course.service";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CourseListPublicDto } from "src/courses/dto/courses.dto";
import { CreateSchedulingDataDto, SchedulingListDto, UpdateSchedulingDataDto } from "src/courses/dto/scheduling.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";

@ApiTags("module.scheduling.course")
@ApiBearerAuth()
@Controller("public/course")
export class CoursePublicController {
    constructor(private courseService: CourseService) { }

    @Post("/list")
    @ApiOperation({ summary: "Course list" })
    @AuthJwtAccessProtected()
    async getCourseList(
        @GetUser() user: any, @Body()
        courseListDto: CourseListPublicDto
    ): Promise<any> {
        const organizationId = await this.courseService.getOrganizationId(user);
        const output = await this.courseService.getCourseList(organizationId, courseListDto);
        return output;
    }

    @Get("/details/:courseId")
    @ApiOperation({ summary: "Course Details" })
    async getCourseDetail(
        @Param("courseId") courseId: string): Promise<any> {
        let output = await this.courseService.getCourseDetail(courseId);
        return output;
    }
}
