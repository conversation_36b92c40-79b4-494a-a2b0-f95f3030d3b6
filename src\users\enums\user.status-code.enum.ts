export enum ENUM_USER_STATUS_CODE_ERROR {
    NOT_FOUND = 5150,
    NOT_SELF = 5151,
    EMAIL_EXIST = 5152,
    USERNAME_EXIST = 5153,
    MOBILE_NUMBER_EXIST = 5154,
    STATUS_INVALID = 5155,
    BLOCKED_INVALID = 5156,
    INACTIVE_FORBIDDEN = 5157,
    DELETED_FORBIDDEN = 5158,
    BLOCKED_FORBIDDEN = 5159,
    PASSWORD_NOT_MATCH = 5160,
    PASSWORD_MUST_NEW = 5161,
    PASSWORD_EXPIRED = 5162,
    PASSWORD_ATTEMPT_MAX = 5163,
    MOBILE_NUMBER_INVALID = 5164,
    USERNAME_NOT_ALLOWED = 5165,
    USERNAME_CONTAIN_BAD_WORD = 5166,
    USER_IS_SELF = 5001,
}
