import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { faker } from '@faker-js/faker';
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { RolePermissionDto } from 'src/role/dtos/role.permission.dto';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';

export class PermissionUpdateRequestDto {
    @ApiProperty({
        description: 'Description of role',
        example: faker.lorem.sentence(),
        required: false,
    })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({
        description: 'Representative for role type',
        example: ENUM_PERMISSION_TYPE,
        required: true,
        enum: ENUM_PERMISSION_TYPE,
    })
    @IsEnum(ENUM_PERMISSION_TYPE)
    @IsNotEmpty()
    type: ENUM_PERMISSION_TYPE;

    @ApiProperty({
        description: "Representative for role type",
        example: false,
        required: true,
    })
    @Type(() => Boolean)
    @IsOptional()
    isDelegated?: boolean;
}
