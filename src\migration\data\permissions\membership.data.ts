import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const membershipPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Membership Write",
        type: ENUM_PERMISSION_TYPE.MEMBERSHIP_WRITE,
        description: 'Grant membership write access',
        isDelegated: true,
    },
    {
        name: "Membership Read",
        type: ENUM_PERMISSION_TYPE.MEMBERSHIP_READ,
        description: 'Grant membership read access',
        isDelegated: true,
    },
    {
        name: "Membership Update",
        type: ENUM_PERMISSION_TYPE.MEMBERSHIP_UPDATE,
        description: 'Grant membership update access',
        isDelegated: true,
    },
    {
        name: "Membership Delete",
        type: ENUM_PERMISSION_TYPE.MEMBERSHIP_DELETE,
        description: 'Grant membership delete access',
        isDelegated: true,
    },
];