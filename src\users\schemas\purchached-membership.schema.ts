import mongoose from "mongoose";
import { ClientDocument } from "./clients.schema";
import { Prop } from "@nestjs/mongoose";
import { membership } from "src/membership/schema/membership.schema";
import { UserDocument } from "./user.schema";
 
export class PurchachedMembershipSchema extends Document {


    @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'membership' })
    membership: membership;

    @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
    userId: UserDocument; 

    @Prop({ type: Date, required: true })
    startDate: Date;



    @Prop({ type: String, required: true })
    notes: string;

 }