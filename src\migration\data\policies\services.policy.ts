import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';

export const servicesPolicies: IDefaultPolicies[] = [

    // Pricing
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_PRICING],
        description: 'Grants view-only access to pricing',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_PRICING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_PRICING],
        description: 'Grants create access to pricing',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_PRICING,
        action: ENUM_POLICY_ACTION.CREATE,

    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_PRICING],
        description: 'Grants update access to pricing',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_PRICING,
        action: ENUM_POLICY_ACTION.UPDATE,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_PRICING],
        description: 'Grants full access to pricing',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_PRICING,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },

    // Promotions
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_DISCOUNTS],
        description: 'Grants view-only access to promotions',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_DISCOUNTS,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_DISCOUNTS],
        description: 'Grants full access to promotions',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_DISCOUNTS,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },

    // Service SETUP
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_SERVICE_SETUP],
        description: 'Grants view-only access to service setup',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_SERVICE_SETUP,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SERVICES_SERVICE_SETUP],
        description: 'Grants full access to service setup',
        isActive: true,
        module: ENUM_POLICY_MODULE.SERVICES,
        type: ENUM_POLICY_TYPE.SERVICES_SERVICE_SETUP,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];