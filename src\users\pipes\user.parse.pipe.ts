import { Injectable, NotFoundException, PipeTransform } from '@nestjs/common';
import { ENUM_USER_STATUS_CODE_ERROR } from 'src/users/enums/user.status-code.enum';
import { UserService } from 'src/users/services/user.service';
import { Types } from 'mongoose';
import { UserDocument } from '../schemas/user.schema';

@Injectable()
export class UserParsePipe implements PipeTransform {
    constructor(private readonly userService: UserService) {}

    async transform(value: string): Promise<UserDocument> {
        const user: UserDocument = await this.userService.findOneById(new Types.ObjectId(value));
        if (!user) {
            throw new NotFoundException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'user.error.notFound',
            });
        }

        return user;
    }
}
