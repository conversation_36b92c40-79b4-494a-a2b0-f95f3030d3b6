import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AmenitiesListDto } from "src/attributes/dto/amenities-list.dto";
import { AmenitiesDto } from "src/attributes/dto/amenities.dto";
import { AmenitiesService } from "src/attributes/services/amenities.service";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

@ApiTags("Admin-Amenities")
@ApiBearerAuth()
@Controller("admin/amenities")
export class AmenitiesController {
    constructor(private amenitiesService: AmenitiesService) { }

    @Post("/create")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities create" })
    async createAmenities(@GetUser() user, @Body() createAmenitiesDto: AmenitiesDto): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.createAmenities(createAmenitiesDto, user);
        return {
            message: "Amenities created",
            data: output,
        };
    }

    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities Listing" })
    async amenitiesList(@GetUser() user, @Body() amenitiesListDto: AmenitiesListDto): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.amenitiesListing(amenitiesListDto, user);
        return {
            message: "Amenities listing",
            data: output,
        };
    }

    @Patch("/update/:amenityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities Update" })
    async amenityUpdate(@Param("amenityId") amenityId: string, @GetUser() user, @Body() updateAmenityDto: AmenitiesDto): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.amenityUpdate(updateAmenityDto, amenityId, user);
        return {
            message: "Amenities update",
            data: output,
        };
    }

    @Get("/groupList")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities detail group wise" })
    async amenityGroupDetails(@GetUser() user): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.amenityGroupDetails(user);
        return {
            message: "Amenities detail group wise",
            data: output,
        };
    }

    @Get("/details/:amenityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities detail" })
    async amenityDetails(@Param("amenityId") amenityId: string, @GetUser() user): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.amenityDetails(amenityId, user);
        return {
            message: "Amenities details",
            data: output,
        };
    }

    @Delete("/delete/:amenityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Amenities deleted" })
    async attributeDelete(@GetUser() user, @Param("amenityId") amenityId: string): Promise<{ message: String; data: any }> {
        let output = await this.amenitiesService.amenityDelete(amenityId, user);
        return {
            message: "Amenities deleted",
            data: "",
        };
    }
}
