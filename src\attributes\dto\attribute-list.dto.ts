import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class AttributeListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Yoga",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    @IsNotEmpty({ message: "Search must not be empty" })
    search: string;
}
