import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsBoolean, IsNotEmpty, IsOptional, IsEnum } from 'class-validator';
import { SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';

export class CreateSubSettingOptionsDto {
  @ApiProperty({
    description: 'Unique key for the sub setting option',
    example: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_MEASUREMENT
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(SubSettingOptionsKeyEnums)
  key: string;

  @ApiProperty({
    description: 'Unique key for the parent setting option',
    example: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_ASSESSMENT
  })
  @IsString()
  @IsOptional()
  @IsEnum(SubSettingOptionsKeyEnums)
  groupKey: string;

  @ApiProperty({
    description: 'Display name of the sub setting option',
    example: 'Measurement'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Detailed description of the sub setting option',
    example: 'Enable/disable assessment'
  })
  @IsString()
  @IsNotEmpty()
  @Type(()=>String)
  description: string;

  @ApiProperty({
    description: 'Default value for the sub setting option',
    example: false,
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  default?: boolean = false;
}
