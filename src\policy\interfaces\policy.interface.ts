import { ENUM_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_TYPE, ENUM_POLICY_SUBJECT_NAME } from '../enums/policy.enum';
import { PermissionDocument } from '../repository/entities/permission.entity';
import { PolicyDocument } from '../repository/entities/policy.entity';



export interface IPolicyDocument extends Omit<PolicyDocument, 'permissions' | 'restrictedPermissions'> {
    permissions: PermissionDocument[];
}

export interface IDefaultPolicies {
    subject: typeof ENUM_POLICY_SUBJECT_NAME[keyof typeof ENUM_POLICY_SUBJECT_NAME],
    description: string,
    isActive: boolean,
    module: ENUM_POLICY_MODULE,
    type: ENUM_POLICY_TYPE,
    action: ENUM_POLICY_ACTION
}

export interface ISelfOrHasPermission {
    self: boolean,
    hasPermissions: boolean
}