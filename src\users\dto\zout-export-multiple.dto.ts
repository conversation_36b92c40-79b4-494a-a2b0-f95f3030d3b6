import { ApiProperty } from "@nestjs/swagger";
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    ArrayNotEmpty,
} from "class-validator";
import { Types } from "mongoose";

export class ExportMultipleZOutReportDto {

    @ApiProperty({
        description: "Array of Zout IDs for which the sales reports are to be generated",
        example: ["66ceffc7a13db5380edb06b1", "66ceffc7a13db5380edb06b2"],
        required: true,
        type: [String]
    })
    @IsNotEmpty({ message: "Zout IDs are required." })
    @IsArray({ message: "Zout IDs must be an array." })
    @ArrayNotEmpty({ message: "Zout IDs array must not be empty." })
    zOutIds: string[];

    @ApiProperty({
        description: "Facility IDs for which the sales report is to be generated",
        example: ["66ceffc7a13db5380edb06b1"],
        required: true,
    })
    @IsNotEmpty({ message: "Facility IDs are required." })
    @IsArray({ message: "Facility IDs must be an array." })
    @ArrayNotEmpty({ message: "Facility IDs array must not be empty." })
    facilityIds: Types.ObjectId[];

    @ApiProperty({
        description: "Response type for export",
        enum: ['pdf', 'zip'],
        default: 'pdf',
    })
    @IsNotEmpty({ message: "Response type is required." })
    @IsEnum(['pdf', 'zip'], {
        message: "Response type must be either 'pdf' or 'zip'.",
    })
    responseType: 'pdf' | 'zip';
}