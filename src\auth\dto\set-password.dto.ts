import { Is<PERSON>trongPassword, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsMongo<PERSON>d, IsEmail, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";

export class SetPasswordDto {
    @ApiProperty({
        description: "The 6-digit OTP sent to the user. Must be a number between 100000 and 999999.",
        example: 123456,
        minimum: 111111,
        maximum: 999999,
    })
    @IsNumber()
    @Min(100000, { message: "Invalid unique id passed" })
    @Max(999999, { message: "Invalid unique id passed" })
    uid: Number;

    @ApiProperty({
        description: "id from the param",
        example: "507f191e810c19729de860ea",
    })
    @IsMongoId({ message: "Invalid user id" })
    id: string;

    @ApiProperty({
        description: "The user email.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @Transform((param) => param.value.toLowerCase())
    @IsEmail()
    @MaxLength(255)
    email: string;

    @ApiProperty({
        description: "The user password.",
        example: "Demo@123",
    })
    @IsStrongPassword()
    password: string;
}
