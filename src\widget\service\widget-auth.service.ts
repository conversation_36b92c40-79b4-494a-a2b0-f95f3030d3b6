import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Response } from "express";
import { BadRequestException, ForbiddenException, InternalServerErrorException, NotFoundException } from "@nestjs/common/exceptions";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import * as bcrypt from "bcrypt";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { JwtService } from "@nestjs/jwt";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { TransactionService } from "src/utils/services/transaction.service";
import { MailService } from "src/mail/services/mail.service";
import * as path from "path";
import * as jwt from "jsonwebtoken";
import { plainToInstance } from 'class-transformer';
import { GeneralService } from "src/utils/services/general.service";
import { Clients } from "src/users/schemas/clients.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { HelperEncryptionService } from "src/common/helper/services/helper.encryption.service";
import { ConfigService } from "@nestjs/config";
import { RoleService } from "src/role/services/role.service";
import { UserService } from "src/users/services/user.service";
import { log } from "console";
import { SessionService } from "src/session/services/session.service";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { RELATION_ENUM } from "src/utils/enums/relation.enum";
import { Gender } from "src/utils/enums/gender.enum";
import { Organizations } from "src/organization/schemas/organization.schema";
import { WidgetRegisterUserDto } from '../dto/widget-Register-dto';
import { AuthJwtAccessPayloadDto } from 'src/auth/dto/jwt/auth.jwt.access-payload.dto';
import { LoginDto } from 'src/auth/dto/login.dto';
@Injectable()
export class WidgetAuthService {
    // jwt
    private readonly jwtAccessTokenSecretKey: string;
    private readonly jwtAccessTokenExpirationTime: number;
    private readonly jwtPrefix: string;
    private readonly jwtAudience: string;
    private readonly jwtIssuer: string;

    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
         @InjectModel(Organizations.name) private organizationModel: Model<Organizations>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        private readonly configService: ConfigService,
        private readonly helperDateService: HelperDateService,
        private readonly helperEncryptionService: HelperEncryptionService,
        private readonly userService: UserService,
        private roleService: RoleService,
    ) {
        this.jwtAccessTokenSecretKey = this.configService.get<string>(
            'auth.jwt.accessToken.secretKey'
        );
        this.jwtAccessTokenExpirationTime = this.configService.get<number>(
            'auth.jwt.accessToken.expirationTime'
        );
        this.jwtPrefix = this.configService.get<string>('auth.jwt.prefix');
        this.jwtAudience = this.configService.get<string>('auth.jwt.audience');
        this.jwtIssuer = this.configService.get<string>('auth.jwt.issuer');

    }
    async registerUser(registerUserDto: WidgetRegisterUserDto, organizationId: string): Promise<{ user: IUserDocument; loginDate: Date; organizationId: string | null }> {
        try {
            const user = await this.UserModel.findOne({ [registerUserDto.type]: registerUserDto[registerUserDto.type], organizationId }).exec();

            if (user) throw new BadRequestException(`${[registerUserDto.type]} already exists`);
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.USER);
            if (!role) throw new BadRequestException("Invalid role");
            let userDetails = {
                name: [registerUserDto.firstName, registerUserDto.lastName].join(" ").trim(),
                firstName: registerUserDto.firstName ? registerUserDto.firstName : "",
                lastName: registerUserDto.lastName ? registerUserDto.lastName : "",
                role: role._id,
                isActive: true,
                organizationId,
                isUserAcceptTerms: registerUserDto.isUserAcceptTerms ? registerUserDto.isUserAcceptTerms : false,
                email: registerUserDto.email ? registerUserDto.email : undefined,
            };
            const emailRequired = registerUserDto.type === AuthTypes.EMAIL;
            const mobileRequired = registerUserDto.type === AuthTypes.MOBILE;

            if (emailRequired) {
                if (!registerUserDto[AuthTypes.EMAIL]) {
                    throw new Error("Email is required");
                }
                userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];

                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required for registration");
                }
                let checkMobileNoExist = await this.UserModel.findOne({ mobile: registerUserDto[AuthTypes.MOBILE], organizationId }).select({ mobile: 1 }).exec();
                if (checkMobileNoExist) {
                    throw new BadRequestException(`${registerUserDto[AuthTypes.MOBILE]} already exists`);
                }
                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";
            } else if (mobileRequired) {
                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required");
                }
                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";

                // if (!registerUserDto[AuthTypes.EMAIL]) {
                //     throw new Error("Email is required for registration");
                // }

                if (AuthTypes.EMAIL === registerUserDto.type) {
                    let checkEmailExist = await this.UserModel.findOne({ email: registerUserDto[AuthTypes.EMAIL], organizationId }).select({ email: 1 }).exec();
                    if (checkEmailExist) {
                        throw new BadRequestException(`${registerUserDto[AuthTypes.MOBILE]} already exists`);
                    }
                    userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];
                }
            } else {
                throw new Error("Invalid registration type specified");
            }
            const createdUser = new this.UserModel(userDetails);

            await createdUser.save();

            const facilitiesDetailArray = await this.FacilityModel.find({ organizationId: registerUserDto.organizationId, });
            const clientDetails = {
                userId: createdUser["_id"],
                facilityId: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0]._id : undefined,
                organizationId: registerUserDto.organizationId,
                address: {
                    state: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0].address.state : undefined
                },
                createdBy:createdUser["_id"],
            };
            if (registerUserDto.facilityId) {
                let facilityValid = await this.FacilityModel.findOne({ organizationId: registerUserDto.organizationId, _id: registerUserDto.facilityId });
                if (!facilityValid) throw new BadRequestException("Facility not found");
            }

            let saveClient = new this.ClientModel(clientDetails);
            await saveClient.save();
            createdUser.password = createdUser.salt = undefined;

            const newUser = await this.userService.findOneWithRoleAndPermissions({ _id: createdUser._id });
            const tokenData = await this.createToken(newUser, registerUserDto.organizationId);
            // return { user: createdUser, accessToken: accessToken };
            return { user: newUser, loginDate: tokenData.loginDate, organizationId: null }
        } catch (error) {
            console.log(error)
            throw new Error(error.message);
        }
    }
    async createToken(
        user: IUserDocument,
        organization?: Types.ObjectId | string
    ): Promise<any> {
        const loginDate = this.helperDateService.create();
        const roleType = user.role.type;

        const payloadAccessToken: AuthJwtAccessPayloadDto =
            await this.createPayloadAccessToken(
                user,
                loginDate,
                organization.toString()
            );
        const accessToken: string = await this.createAccessToken(
            user._id.toString(),
            payloadAccessToken
        );

        return {
            tokenType: this.jwtPrefix,
            roleType,
            loginDate,
            expiresIn: this.jwtAccessTokenExpirationTime,
            accessToken,
        };
    }
    async createPayloadAccessToken(
        data: IUserDocument,
        loginDate: Date,
        organization: Types.ObjectId | string
    ): Promise<AuthJwtAccessPayloadDto> {
        return plainToInstance(AuthJwtAccessPayloadDto, {
            user: data._id.toString(),
            type: data.role.type,
            role: data.role._id.toString(),
            roleIsActive: data.role.isActive,
            email: data.email?.toString(),
            loginDate: loginDate,
            organization: organization
        } as Partial<AuthJwtAccessPayloadDto>);
    }
    async createAccessToken(
        subject: string,
        payload: AuthJwtAccessPayloadDto
    ): Promise<string> {
        return this.helperEncryptionService.jwtEncrypt(
            { ...payload },
            {
                secretKey: this.jwtAccessTokenSecretKey,
                expiredIn: this.jwtAccessTokenExpirationTime,
                audience: this.jwtAudience,
                issuer: this.jwtIssuer,
                subject,
            }
        );
        
    }
    async findUserByIdentifier(
        key: 'email' | 'mobile',
        value: string | undefined,
        organizationId?: string
    ) {
        if (!value) return null;
        const parentless = { $or: [{ parent: { $exists: false } }, { parent: null }] } as const;

        // 1) within org
        let user = await this.userService.findOneWithRoleAndPermissions({
            [key]: value,
            ...parentless,
            organizationId,
        });

        // 2) global (fallback)
        if (!user) {
            user = await this.userService.findOneWithRoleAndPermissions({
                [key]: value,
                ...parentless,
            });
        }
        return user;
    }

    async validateUserFlexible(loginDto: LoginDto, organizationId: string): Promise<IUserDocument> {
        const { type, email, mobile, password } = loginDto;

        // basic input checks
        if (type === 'email' && !email) throw new BadRequestException('Email is required');
        if (type === 'mobile' && !mobile) throw new BadRequestException('Mobile is required');
        if (!password) throw new BadRequestException('Password is required');

        let user: IUserDocument | null = null;

        if (type === 'email') {
            user = await this.findUserByIdentifier('email', email, organizationId);
        } else if (type === 'mobile') {
            user = await this.findUserByIdentifier('mobile', mobile, organizationId);
        } else {
            // type === 'both' => email first, then mobile
            user =
                (await this.findUserByIdentifier('email', email, organizationId)) ||
                (await this.findUserByIdentifier('mobile', mobile, organizationId));
        }

        if (!user) throw new BadRequestException('Invalid username or password');

        // ---- same checks you already had ----
        const superAdminRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.SUPER_ADMIN);
        const isSuperAdmin = user.role?._id?.toString() === superAdminRole._id?.toString();
        const belongsToOrg =
            user._id?.toString() === organizationId ||
            user.organizationId?.toString() === organizationId;

        if (!isSuperAdmin && !belongsToOrg) {
            // keep message generic because we accept either identifier
            throw new BadRequestException('Account does not exist for this organization.');
        }

        if (!user?.salt && !user?.password) {
            throw new BadRequestException("You haven't set a password yet.");
        }
        if (user.isActive === false) {
            throw new ForbiddenException('User Account is disabled');
        }

        const ok = await user.validatePassword(password);
        if (!ok) throw new BadRequestException('Invalid username or password');

        user.password = user.salt = undefined as any;
        return user;
    }
    async login(loginDto: LoginDto, allowedRoles: ENUM_ROLE_TYPE[], organizationId: string): Promise<any> {
        try {
            let profiles = [];
            const user = await this.validateUserFlexible(loginDto, organizationId);
            if (!user) throw new BadRequestException("Invalid login details");

            if (allowedRoles && !allowedRoles.includes(user.role.type)) {
                throw new BadRequestException("You are not allowed to get access");
            }

            switch (user.role.type) {
                case ENUM_ROLE_TYPE.USER:
                    const clientDetails = await this.ClientModel.findOne(
                        { userId: user._id },
                        { photo: 1, organizationId: 1 }
                    );
                    if (!clientDetails) {
                        throw new BadRequestException("Client profile not found");
                    }
                    user["_doc"]["photo"] = clientDetails.photo || "";
                    // User profiles will set later
                    break;

                case ENUM_ROLE_TYPE.ORGANIZATION:
                    // Organization doesn't need additional checks
                    const organization = await this.organizationModel.findOne({ userId: user._id });
                    if (!organization) {
                        throw new BadRequestException("Organization profile not found");
                    }
                    user["_doc"]["photo"] = organization.logo || "";
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: organization.logo || "",
                            gender: Gender.OTHER,
                            relation: RELATION_ENUM.OTHER,
                        },
                    ];
                    break;

                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.TRAINER:
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user._id },
                        { organizationId: 1, photo: 1 }
                    );
                    if (!staffDetails) {
                        throw new BadRequestException("Staff profile not found");
                    }
                    let facilityIds = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1, organizationId: 1 });
                    let facilityArray = facilityIds && facilityIds.facilityId ? facilityIds.facilityId.map((facility) => facility) : [];
                    let facilities = await this.FacilityModel.findOne(
                        {
                            _id: { $in: facilityArray },
                            // isActive: true,
                        },
                        {
                            organizationId: 1,
                        },
                    );
                    if (!facilities) throw new BadRequestException("Facility not found or Deactivated");
                    user["_doc"]["photo"] = staffDetails.profilePicture || "";
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: staffDetails.profilePicture || "",
                            gender: staffDetails.gender || "",
                            relation: RELATION_ENUM.PARENT
                        },
                    ]
                    break;

                case ENUM_ROLE_TYPE.SUPER_ADMIN:
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: "",
                            //gender: staffDetails.gender || "",
                            relation: RELATION_ENUM.PARENT
                        },
                    ]
                    break;

                default:
                    throw new BadRequestException("Invalid role type");
            }

            if (!organizationId && user.role.type !== ENUM_ROLE_TYPE.SUPER_ADMIN) {
                throw new BadRequestException("Unable to determine organization");
            }

            // const accessToken = this.JwtService.sign({ userId: user._id });
            const tokenData = await this.createToken(user, new Types.ObjectId(organizationId));
            // let allowedRoles = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            // let allowedRoles = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            // let organizationId = null;
            // if (allowedRoles.includes(user["role"].type)) {
            //     let facilityIds = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1,organizationId:1 });
            //     let facilityArray = facilityIds && facilityIds.facilityId ? facilityIds.facilityId.map((facility) => facility) : [];
            //     let facilities = await this.FacilityModel.findOne(
            //         {
            //             _id: { $in: facilityArray },
            //            // isActive: true,
            //         },
            //         {
            //             organizationId: 1,
            //         },
            //     );
            //     //if(!facilities) throw new BadRequestException("Facility not found or Deactivated");
            //     organizationId = facilities ? facilities["organizationId"] : null;
            //     // if(organizationId) {
            //     //     let organization = await this.UserModel.findOne({ _id: organizationId, isActive: true });
            //     //     if(!organization) throw new BadRequestException("Can't login.Organization is currently Deactivated");
            //     // }
            // }
            // if(user["role"] === ENUM_ROLE_TYPE.USER){
            //     let organization = await this.UserModel.findOne({ _id: organizationId, isActive: true });
            //     if(!organization) throw new BadRequestException("Can't login.Organization is currently Deactivated");

            if (user["role"].type === ENUM_ROLE_TYPE.USER && !organizationId) throw new BadRequestException("Can't login. Organization is currently Deactivated");

            if (user["role"].type === ENUM_ROLE_TYPE.USER && !user.parent) {
                const minor = await this.UserModel.find({ parent: user._id, isActive: true }, { password: 0, salt: 0 }, { hydrate: true });
                const clientDetails = await this.ClientModel.find({ userId: { $in: [user._id, ...minor.map(doc => doc._id)] } }, { photo: 1, userId: 1, gender: 1, relation: 1 });
                profiles = [
                    {
                        _id: user._id,
                        firstName: user.firstName || user.name,
                        lastName: user.lastName,
                        photo: clientDetails.find(client => client.userId.toString() === user._id.toString())?.photo || "",
                        gender: clientDetails.find(client => client.userId.toString() === user._id.toString())?.gender || "",
                        relation: RELATION_ENUM.PARENT
                    },
                    ...minor.map(doc => ({
                        _id: doc._id,
                        firstName: doc.firstName || doc.name,
                        lastName: doc.lastName,
                        photo: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.photo || "",
                        gender: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.gender || "",
                        relation: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.relation || ""
                    }))
                ]
            }

            return {
                user,
                profiles,
                roleType: tokenData.roleType,
                accessToken: tokenData.accessToken,
                tokenType: tokenData.tokenType,
                loginDate: tokenData.loginDate,
                organizationId: organizationId
            }
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw new BadRequestException(error.message);
            }
            if (error instanceof NotFoundException) {
                throw new NotFoundException(error.message);
            }
            if (error instanceof ForbiddenException) {
                throw new ForbiddenException(error.message);
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}