import { Schema, Types } from 'mongoose';
import slugify from 'slugify';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import {
    DatabaseEntity,
    DatabaseProp,
    DatabaseSchema,
} from 'src/common/database/decorators/database.decorator';
import { IDatabaseDocument } from 'src/common/database/interfaces/database.interface';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { ENUM_PROMOTION_TARGET } from 'src/promotions/enums/promotion-target.enum';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';

export const PromotionTableName = 'promotions';

@DatabaseEntity({ collection: PromotionTableName })
export class TimeWindow {

    @DatabaseProp({
        required: true,
        type: String,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    startTime: string;

    @DatabaseProp({
        required: true,
        type: String,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    endTime: string;
}

@DatabaseEntity({ collection: PromotionTableName })
export class PromotionEntity extends DatabaseEntityBase {
    @DatabaseProp({
        required: true,
        type: Schema.Types.ObjectId,
        ref: 'User',
        index: true,
    })
    organizationId: Types.ObjectId;

    @DatabaseProp({
        required: false,
        type: [Schema.Types.ObjectId],
        ref: 'Facility',
        default: [],
    })
    facilityIds?: Types.ObjectId[];

    @DatabaseProp({
        required: true,
        type: String,
        index: true,
        trim: true,
    })
    name: string;

    @DatabaseProp({
        required: false,
        type: String,
        trim: true,
        uppercase: true,
    })
    couponCode?: string;

    @DatabaseProp({
        required: false,
        type: String,
        trim: true,
        default: "",
    })
    description?: string;

    @DatabaseProp({
        required: true,
        enum: DiscountType, // percentage or flat
    })
    type: DiscountType;

    @DatabaseProp({
        required: true,
        type: Number,
    })
    value: number;

    @DatabaseProp({
        required: true,
        enum: ENUM_PROMOTION_TARGET,
        default: ENUM_PROMOTION_TARGET.ALL_USERS, // all users or members only
    })
    target: ENUM_PROMOTION_TARGET;

    @DatabaseProp({
        required: true,
        type: String,
        enum: ENUM_PRODUCT_ITEM_TYPE,
    })
    itemType: ENUM_PRODUCT_ITEM_TYPE;

    @DatabaseProp({
        required: true,
        default: true,
        index: true,
        type: Boolean,
    })
    isActive: boolean;

    @DatabaseProp({
        required: false,
        type: Date,
        default: null,
    })
    startDate?: Date;

    @DatabaseProp({
        required: false,
        type: Date,
        default: null,
    })
    endDate?: Date;

    @DatabaseProp({
        required: false,
        type: TimeWindow,
    })
    timeWindow?: TimeWindow;

    @DatabaseProp({
        required: false,
        type: String,
        index: false,
        trim: false,
    })
    promotionLabel?: string;

    @DatabaseProp({
        required: false,
        type: Boolean,
        default: false,
    })
    autoApplyAll: boolean;
}

export const PromotionSchema = DatabaseSchema(PromotionEntity);
export type PromotionDocument = IDatabaseDocument<PromotionEntity>;


PromotionSchema.index({ organizationId: 1, couponCode: 1 }, { unique: true });

PromotionSchema.pre('save', function (next) {
    if (this.isNew && !this.couponCode) {
        const nameSlug = slugify(this.name, {
            replacement: '-',    // replace spaces with replacement character
            remove: /[*+~.()'"!:@]/g, // regex to remove characters
            lower: false,        // convert to lower case
            strict: true,       // strip special characters except replacement
            locale: 'en',       // language code of the locale to use
            trim: true         // trim leading and trailing replacement chars
        });
        this.couponCode = `${nameSlug}`.toUpperCase();
    }
    next();
});
