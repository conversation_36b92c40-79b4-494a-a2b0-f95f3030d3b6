import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsMongoId, IsOptional } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetPricingDto {
    @ApiProperty({
        description: "Type of service",
        example: "bookings",
        required: false,
    })
    @IsOptional()
    @IsEnum(ClassType)
    classType?: string;

    @ApiProperty({
        description: "Is fetching for share pass",
        example: false,
        required: false,
    })
    @IsOptional()
    isForSharePass?: boolean;

    @ApiProperty({
        description: "User Id",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Client of package" })
    userId?: string;

    @ApiProperty({
        description: "Staff Id",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Staff of package" })
    staffId?: string;
}
