import { Injectable } from '@nestjs/common';
// Safe way with types
import * as XLSX from 'xlsx';

// import * as PDFDocument from 'pdfkit';
// import * as StreamBuffers from 'stream-buffers';

@Injectable()
export class ExportService {
  async generateExportFile(
    products: any[],
    type: 'csv' | 'xlsx' | 'pdf',
  ): Promise<Buffer> {
    const formattedRows = this.flattenProducts(products);

    if (type === 'csv' || type === 'xlsx') {
      const worksheet = XLSX.utils.json_to_sheet(formattedRows);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');

      return XLSX.write(workbook, {
        bookType: type,
        type: 'buffer',
      });
    }

    // if (type === 'pdf') {
    //   return this.generatePdf(formattedRows);
    // }

    throw new Error('Unsupported file type');
  }

  private flattenProducts(products: any[]): Record<string, any>[] {
    const rows: Record<string, any>[] = [];
  
    for (const product of products) {
      const baseFields: Record<string, any> = {
        master_sku: product.sku,
        type: product.type.toLowerCase(),
        name: product.name,
      };
  
      const productAttrs = (product.parentAttributes || []).reduce((acc, attr) => {
        acc[attr.attribute] = attr.value;
        return acc;
      }, {} as Record<string, any>);
  
      const brand = productAttrs['brand'] || '';
      delete productAttrs['brand'];
  
      const categoryInfo = {
        Category: product.firstCategoryDetails?.[0]?.name || '',
        'Sub-Category': product.secondCategoryDetails?.[0]?.name || '',
        hsn: product.hsn,
        gst: product.gst,
      };
  
      if (product.type === 'simple') {
        rows.push({
          ...baseFields,
          variantTitle: '',
          variantSku: '',
          Brand: brand,
          ...categoryInfo,
          ...{} // no dynamic variant attributes
        });
      } else if (product.type === 'variable' && Array.isArray(product.productVariants)) {
        for (const variant of product.productVariants) {
          const variantAttrs = (variant.variantAttributes || []).reduce((acc, attr) => {
            acc[attr.attribute] = attr.value;
            return acc;
          }, {} as Record<string, any>);
  
          const variantBrand = variantAttrs['brand'];
          delete variantAttrs['brand'];
  
          rows.push({
            ...baseFields,
            variantTitle: variant.title,
            variantSku: variant.sku,
            Brand: variantBrand || brand,
            ...categoryInfo,
            ...variantAttrs,
          });
        }
      }
    }
  
    return rows;
  }
  
  
  
  

//   private async generatePdf(data: Record<string, any>[]): Promise<Buffer> {
//     const doc = new PDFDocument({ margin: 30 });
//     const stream = new StreamBuffers.WritableStreamBuffer();

//     doc.pipe(stream);
//     doc.fontSize(18).text('Product Export Report', { align: 'center' });
//     doc.moveDown();

//     data.forEach((row, index) => {
//       doc.fontSize(12).text(`Row ${index + 1}`, { underline: true });
//       Object.entries(row).forEach(([key, value]) => {
//         doc.fontSize(10).text(`${key}: ${value}`);
//       });
//       doc.moveDown();
//     });

//     doc.end();

//     return new Promise((resolve) => {
//       stream.on('finish', () => resolve(stream.getContents()));
//     });
//   }

  getMimeType(type: string): string {
    switch (type) {
      case 'csv': return 'text/csv';
      case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'pdf': return 'application/pdf';
      default: return 'application/octet-stream';
    }
  }
}
