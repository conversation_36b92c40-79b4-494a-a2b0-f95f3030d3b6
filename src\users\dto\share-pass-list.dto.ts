import { IsMongoId } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ApiProperty } from "@nestjs/swagger";
export class SharePassListDto extends PaginationDto {
    @ApiProperty({
        description: "User whose pin want to update",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid user details" })
    userId: string;
}
