import { AuthGuard } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ENUM_AUTH_STATUS_CODE_ERROR } from 'src/auth/enums/auth.status-code.enum';
import { AuthJwtAccessPayloadDto } from 'src/auth/dto/jwt/auth.jwt.access-payload.dto';
import { Types } from 'mongoose';

@Injectable()
// export class AuthJwtAccessGuard extends AuthGuard('jwt') {
export class AuthJwtAccessGuard extends AuthGuard('jwt') {
    handleRequest<T = AuthJwtAccessPayloadDto>(
        err: Error,
        user: T,
        info: Error
    ): T {
        if (err || !user) {
            throw new UnauthorizedException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
                _error: err ? err.message : info.message,
            });
        }

        const { sub } = user as AuthJwtAccessPayloadDto;
        if (!sub) {
            throw new UnauthorizedException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        } else if (!Types.ObjectId.isValid(sub)) {
            throw new UnauthorizedException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }

        return user;
    }
}

@Injectable()
export class AuthJwtAccessOptionalGuard extends AuthGuard('jwt') {
    handleRequest<T = AuthJwtAccessPayloadDto>(
        err: Error,
        user: T,
        info: Error
    ): T | null {
        if (!user) {
            return null; // Silently allow unauthenticated access
        }

        const { sub } = user as AuthJwtAccessPayloadDto;
        if (!sub || !Types.ObjectId.isValid(sub)) {
            return null; // Silently fail, no error thrown
        }

        return user;
    }
}