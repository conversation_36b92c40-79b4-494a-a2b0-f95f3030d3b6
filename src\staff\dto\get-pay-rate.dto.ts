import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsMongoId, IsEnum } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetPayRatesDto extends PaginationDTO {
    @ApiPropertyOptional({
        description: "The ID of the staff to filter Pay Rates by",
        example: "659d268dee4b6081dacd41fd",
    })
    @IsOptional()
    @IsMongoId({ message: "Staff ID must be a valid MongoDB ObjectId" })
    staffId?: string;

    @ApiProperty({
        description: "Type of the Service",
        example: ClassType.PERSONAL_APPOINTMENT,
        enum: ClassType,
        required: false,
    })
    @IsEnum(ClassType, { message: "Service Type must be a valid enum value" })
    @IsOptional({ message: "Service Type is required" })
    serviceType?: ClassType;

    @ApiProperty({
        description: "The ID of the Service category",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "Service Category ID must be a valid ObjectId" })
    @IsOptional({ message: "Service Type is required" })    
    serviceCategory?: string;

    @ApiProperty({
        description: "The ID of the Appointment Type",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "Appointment Type ID must be a valid ObjectId" })
    @IsOptional({ message: "Service Type is required" })    
    appointmentType?: string;
}
