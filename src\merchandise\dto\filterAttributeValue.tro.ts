import { IsEnum, IsOptional, IsString } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ApiProperty } from '@nestjs/swagger';
import { Attributes } from 'src/utils/enums/attribute.enum'
export class FilterAttributeValueDto extends PaginationDto {
    @ApiProperty({
        description: 'The content to search',
        type: String,
        required: false,
        example: 'brand ',
    })
    @IsOptional()
    @IsString()
    search: string;

    @ApiProperty({   
        description: 'The attribute to search',
        type: String,
        required: false,
        example: 'brand ',
    })
    @IsOptional()
   
    attribute: string;
}
