import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { AttributeValue, AttributeDocument } from "src/merchandise/schema/attribute.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { CreateAttributeValueDto } from "../dto/createAttributeValue.dto";
import { FilterAttributeValueDto } from "../dto/filterAttributeValue.tro";
import { Types } from "mongoose";
import { Product, ProductSchema, ProductType } from "src/merchandise/schema/product.schema";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { ProductVariantAttribute } from "src/merchandise/schema/product-attribute-list.schema";
import { ProductCreateAttributeDto } from "../dto/productAttributeValue.dto";
import { AttributeType } from "../schema/attribute.schema";
import { InputFieldType } from 'src/utils/enums/input-field-type.enum';
import { UpdateAttributeDto, UpdateAttributeStatusDto } from "../dto/productattributeUpdate.dto";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IUserDocument } from "src/users/interfaces/user.interface";


type AttributeDefinition = {
    name: string;
    type: AttributeType;
    inputType: InputFieldType;
    multiple: boolean;
    isActive?: boolean

};
const DEFAULT_ATTRIBUTES: AttributeListMap = {
    brand: {
        name: "Brand",
        type: AttributeType.Mandatory,
        inputType: InputFieldType.Dropdown,
        multiple: false,
    },
    shade: {
        name: "Shade",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
    }
};



type AttributeListMap = Record<string, AttributeDefinition>;
@Injectable()
export class ProductAttributeService {
    constructor(
        @InjectModel(AttributeValue.name) private attributeValueModel: Model<AttributeDocument>,
        @InjectModel(Product.name) private productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private readonly productVariantModel: Model<ProductVariant>,
        @InjectModel(ProductVariantAttribute.name) private readonly productVariantAttributeModel: Model<ProductVariantAttribute>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        private readonly transactionService: TransactionService,
    ) { }


    async getOrganizationId(user: IUserDocument){
        if (!user._id) {
          throw new BadRequestException("User not found");
        }
        if (!user.role) {
          throw new BadRequestException("User not found");
        }
        if (user.role.type === ENUM_ROLE_TYPE.WEB_MASTER || user.role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || user.role.type === ENUM_ROLE_TYPE.TRAINER) {
          const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
          if (!staffDetails) throw new BadRequestException("Staff not found");
          return staffDetails.organizationId;
        }
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
          return user._id;
        }

    }

    async createProductAttribute(createProductAttributeDto: ProductCreateAttributeDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const organizationId = await this.getOrganizationId(user)
        try {
            const key = this.generateKeyFromName(createProductAttributeDto.name);
            const data = {
                ...createProductAttributeDto,
                organizationId: organizationId,
                slug: key
            };

            const isAttributeExist = await this.productVariantAttributeModel.findOne({
                organizationId: data.organizationId,
                slug: data.slug
            });

            if (isAttributeExist) {
                throw new BadRequestException('Attribute already exists');
            }

            const newProductAttribute = new this.productVariantAttributeModel(data);
            const createdAttribute = await newProductAttribute.save({ session });

            await this.transactionService.commitTransaction(session);
            return createdAttribute;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    private generateKeyFromName(name: string): string {
        return name
            .replace(/[^\w\s]/gi, '')
            .split(' ')
            .map((word, index) =>
                index === 0
                    ? word.toLowerCase()
                    : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            )
            .join('');
    }
    async getAttributeListMap(user: any): Promise<AttributeListMap> {
        const organizationId = await this.getOrganizationId(user)
        const attributes = await this.productVariantAttributeModel
            .find({ organizationId })
            .sort({ createdAt: -1 })
            .lean();

        const result: AttributeListMap = { ...DEFAULT_ATTRIBUTES };

        for (const attr of attributes) {
            result[attr.slug] = {
                name: attr.name,
                type: attr.type,
                inputType: attr.inputType,
                multiple: attr.multiple,
                isActive: attr.isActive
            };
        }

        return result;
    }
    async deleteAttribute(slug: string, user: any): Promise<{ message: string }> {
        const organizationId = await this.getOrganizationId(user)
        if (['brand', 'shade'].includes(slug)) {
            throw new BadRequestException(`Cannot delete default attribute: ${slug}`);
        }

        const session = await this.transactionService.startTransaction();
        try {
            const attribute = await this.productVariantAttributeModel.findOne({ slug, organizationId }).lean();
            if (!attribute) {
                throw new NotFoundException("Attribute not found");
            }

            const isUsedInVariants = await this.productVariantModel.exists({
                'attributes.key': slug,
                organizationId: organizationId
            });

            if (isUsedInVariants) {
                throw new BadRequestException(`Cannot delete — attribute '${slug}' is used in product variants`);
            }

            const isUsedInAttributeValues = await this.attributeValueModel.exists({
                attribute: slug,
                organizationId: organizationId
            });

            if (isUsedInAttributeValues) {
                throw new BadRequestException(`Cannot delete — attribute '${slug}' has subattribute values`);
            }

            await this.productVariantAttributeModel.deleteOne({ _id: attribute._id }, { session });

            await this.transactionService.commitTransaction(session);
            return { message: `Attribute '${slug}' deleted successfully` };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getAttributeBySlug(slug: string, user: any): Promise<AttributeDefinition> {
        const organizationId = await this.getOrganizationId(user)
        if (DEFAULT_ATTRIBUTES[slug]) {
            return DEFAULT_ATTRIBUTES[slug];
        }
        const attribute = await this.productVariantAttributeModel.findOne({
            slug,
            organizationId,
        }).lean();
        if (!attribute) {
            throw new NotFoundException(`Attribute '${slug}' not found`);
        }
        return {
            name: attribute.name,
            type: attribute.type,
            inputType: attribute.inputType,
            multiple: attribute.multiple,

        };
    }

    async updateAttribute(
        slug: string,
        dto: UpdateAttributeDto,
        user: any
    ): Promise<{ message: string }> {
        const organizationId = await this.getOrganizationId(user)
        if (['brand', 'shade'].includes(slug)) {
            throw new BadRequestException(`Cannot update default attribute: ${slug}`);
        }

        const session = await this.transactionService.startTransaction();
        try {
            const attribute = await this.productVariantAttributeModel.findOne({ slug, organizationId }).session(session);
            if (!attribute) {
                throw new NotFoundException(`Attribute '${slug}' not found`);
            }

            const newSlug = this.generateKeyFromName(dto.name);

            const isSlugTaken = await this.productVariantAttributeModel.findOne({
                slug: newSlug,
                organizationId,
                _id: { $ne: attribute._id },
            });
            if (isSlugTaken) {
                throw new BadRequestException(`Attribute with name '${dto.name}' already exists`);
            }

            attribute.name = dto.name;
            attribute.slug = newSlug;
            await attribute.save({ session });

            await this.attributeValueModel.updateMany(
                { attribute: slug, organizationId },
                { $set: { attribute: newSlug } },
                { session }
            );

            await this.productVariantModel.updateMany(
                {
                    'attributes.key': slug,
                    organizationId
                },
                {
                    $set: { 'attributes.$[elem].key': newSlug }
                },
                {
                    session,
                    arrayFilters: [{ 'elem.key': slug }]
                }
            );

            await this.transactionService.commitTransaction(session);
            return { message: `Attribute '${slug}' updated successfully to '${newSlug}'` };
        } catch (err) {
            await this.transactionService.abortTransaction(session);
            throw err;
        } finally {
            session.endSession();
        }
    }
    async updateAttributeStatus(
        slug: string,
        updateAttributeStatus: UpdateAttributeStatusDto,
        user: any
    ): Promise<{ message: string }> {
        const organizationId = await this.getOrganizationId(user)
        if (['brand', 'shade'].includes(slug)) {
            throw new BadRequestException(`Cannot update status for default attribute: ${slug}`);
        }

        const attribute = await this.productVariantAttributeModel.findOneAndUpdate(
            { slug, organizationId },
            { $set: { isActive: updateAttributeStatus.isActive } },
            { new: true }
        );

        if (!attribute) {
            throw new NotFoundException(`Attribute '${slug}' not found`);
        }

        return {
            message: `Attribute '${slug}' status updated to ${updateAttributeStatus.isActive ? 'active' : 'inactive'}`,
        };
    }
    async getActiveAttributes(user: any): Promise<AttributeListMap> {
        const organizationId = await this.getOrganizationId(user)
        const attributes = await this.productVariantAttributeModel
          .find({ organizationId, isActive: true })
          .sort({ createdAt: -1 })
          .lean();
      
        
        const result: AttributeListMap = { ...DEFAULT_ATTRIBUTES };
      
        for (const attr of attributes) {
          result[attr.slug] = {
            name: attr.name,
            type: attr.type,
            inputType: attr.inputType,
            multiple: attr.multiple,
           
          };
        }
      
        return result;
      }
      
}