import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';

export class PolicyAssignPermissionRequestDto {
    @ApiProperty({
        description: 'Array of permission IDs to assign to the policy',
        example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
        required: true,
        type: [String]
    })
    @IsArray()
    @IsNotEmpty()
    permissions: string[];
}
