import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ENUM_POLICY_STATUS_CODE_ERROR } from 'src/policy/enums/policy.status-code.enum';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_AUTH_STATUS_CODE_ERROR } from 'src/auth/enums/auth.status-code.enum';
import { ROLE_META_KEY } from '../constants/policy.constant';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
@Injectable()
export class PolicyRoleGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<IRequestApp>();
        const { user } = request
        request.__permissionSet = new Set<ENUM_PERMISSION_TYPE | ENUM_ROLE_TYPE>();

        const role =
            this.reflector.get<ENUM_ROLE_TYPE[]>(
                ROLE_META_KEY,
                context.getHandler()
            ) || [];

        if (!user) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }

        if (!user) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }

        const { type } = user;

        if (type === ENUM_ROLE_TYPE.SUPER_ADMIN) {
            return true;
        }

        if (!role.length) {
            // No role required
            return true;
        }

        if (!role.includes(type)) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ROLE_FORBIDDEN,
                message: 'policy.error.roleForbidden',
            });
        }

        role.map(r => request.__permissionSet.add(r))

        return true;
    }
}
