import { Injectable } from '@nestjs/common';
import { Model, PopulateOptions } from 'mongoose';
import { DatabaseRepositoryBase } from 'src/common/database/bases/database.repository';
import { InjectDatabaseModel } from 'src/common/database/decorators/database.decorator';
import { PolicyEntity } from 'src/policy/repository/entities/policy.entity';
import {
    RoleDocument,
    RoleEntity,
} from 'src/role/repository/entities/role.entity';

@Injectable()
export class RoleRepository extends DatabaseRepositoryBase<
    RoleEntity,
    RoleDocument
> {

    readonly _joinActive: PopulateOptions[] = [
        {
            path: 'policies',
            localField: 'policies',
            foreignField: '_id',
            model: PolicyEntity.name,
            // justOne: true,
            match: {
                isActive: true,
            },
        },
        {
            path: 'delegatedPolicies',
            localField: 'delegatedPolicies',
            foreignField: '_id',
            model: PolicyEntity.name,
            // justOne: true,
            match: {
                isActive: true,
            },
        },
        
    ];


    constructor(
        @InjectDatabaseModel(RoleEntity.name)
        private readonly roleModel: Model<RoleEntity>
    ) {
        super(roleModel, [
            {
                path: 'policies',
                localField: 'policies',
                foreignField: '_id',
                model: RoleEntity.name,
                // justOne: true,
                match: {
                    isActive: true,
                },
            },
            {
                path: 'delegatedPolicies',
                localField: 'delegatedPolicies',
                foreignField: '_id',
                model: RoleEntity.name,
                // justOne: true,
                match: {
                    isActive: true,
                },
            }
        ]);
    }
}
