import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { RedisClientType } from '@keyv/redis';
import {
    RedisClient,
    RedisPipelineCommand,
    CacheOptions,
    HashOperationOptions,
    CacheSetResult,
    CacheGetResult,
    HashOperationResult
} from '../interfaces/caching.interface';
import { delay } from 'rxjs';


@Injectable()
export class CachingService implements OnModuleInit, OnModuleDestroy {
    private logger = new Logger(CachingService.name);
    public redisClient: RedisClientType;
    private isConnected = false;
    private maxRetries = 5;
    private retryDelay = 2000; // 2 seconds
    private reconnectTimer: NodeJS.Timeout | null = null;

    constructor(
        @Inject(CACHE_MANAGER) private cacheManager: Cache,

    ) {
        this.initializeRedisClient();

        return new Proxy(this, {
            get: (target, prop: keyof CachingService) => {
                const originalMethod = target[prop];
                if (typeof originalMethod === 'function') {
                    return async (...args: any[]) => {
                        await target.ensureConnected();
                        return originalMethod.apply(target, args);
                    };
                }
                return originalMethod;
            },
        });
    }

    private async initializeRedisClient(): Promise<void> {
        const keyvStore = (this.cacheManager as any).store || (this.cacheManager as any).stores?.[0];

        if (keyvStore) {
            this.redisClient = keyvStore.opts?.store?.client || keyvStore.client;

            if (!this.redisClient) {
                this.logger.error('Redis client not found in Keyv store');
                return;
            }

            // Set up error handler
            this.redisClient.on('error', (error) => {
                this.logger.error('Redis client error:', error);
                this.isConnected = false;
                this.scheduleReconnect();
            });

            // Set up connect handler
            this.redisClient.on('connect', () => {
                this.logger.log('✅ Redis connected successfully');
                this.isConnected = true;
                if (this.reconnectTimer) {
                    clearTimeout(this.reconnectTimer);
                    this.reconnectTimer = null;
                }
            });

            await this.ensureConnected();
        }
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) return;

        this.reconnectTimer = setTimeout(async () => {
            try {
                await this.ensureConnected();
            } catch (error) {
                this.logger.error('Redis reconnection attempt failed:', error);
                this.scheduleReconnect();
            }
        }, this.retryDelay);
    }

    private async ensureConnected(retries = this.maxRetries): Promise<void> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                if (!this.redisClient) {
                    throw new Error('Redis client is undefined');
                }

                if (this.redisClient.isOpen) {
                    this.isConnected = true;
                    return;
                }

                await this.redisClient.connect();
                this.isConnected = true;
                this.logger.log('✅ Redis successfully connected');
                return;
            } catch (error) {
                this.logger.error(`❌ Redis connection failed (attempt ${attempt}/${retries}):`, error.message);
                this.isConnected = false;

                if (attempt < retries) {
                    const delay = Math.min(this.retryDelay * Math.pow(2, attempt - 1), 30000); // Cap at 30 seconds
                    this.logger.log(`🔄 Retrying in ${delay / 1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    this.logger.error('🚨 Max retry attempts reached. Redis connection failed.');
                    this.scheduleReconnect();
                }
            }
        }
    }

    async onModuleInit() {
        await this.ensureConnected();
    }

    async onModuleDestroy() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        if (this.redisClient) {
            await this.redisClient.quit();
        }
    }

    async getClient(): Promise<RedisClientType> {
        await this.ensureConnected();
        return this.redisClient;
    }

    async get<T>(key: string): Promise<T | undefined> {
        return await this.cacheManager.get<T>(key);
    }

    async set(key: string, value: any, ttl?: number): Promise<void> {
        await this.cacheManager.set(key, value, ttl);
    }

    async del(key: string): Promise<void> {
        await this.cacheManager.del(key);
    }

    async wrap<T>(
        key: string,
        fn: () => Promise<T>,
        ttl?: number
    ): Promise<T> {
        return await this.cacheManager.wrap(key, fn, ttl);
    }

    async getOrSet<T>(
        key: string,
        fn: () => Promise<T>,
        ttl?: number
    ): Promise<T> {
        const cachedValue = await this.get<T>(key);
        if (cachedValue) {
            return cachedValue;
        }

        const value = await fn();
        await this.set(key, value, ttl);
        return value;
    }

    async mget<T>(keys: string[]): Promise<(T | undefined)[]> {
        return await Promise.all(keys.map(key => this.get<T>(key)));
    }

    async mset(keyValuePairs: [string, any][], ttl?: number): Promise<void> {
        await Promise.all(
            keyValuePairs.map(([key, value]) => this.set(key, value, ttl))
        );
    }

    async mdel(keys: string[]): Promise<void> {
        await Promise.all(keys.map(key => this.del(key)));
    }

    // Hash operations
    async hset(key: string, field: string, value: any): Promise<number> {
        await this.redisClient.hSet(key, field, JSON.stringify(value));
        return
    }

    async hget<T>(key: string, field: string): Promise<T | null> {
        const value = await this.redisClient.hGet(key, field);
        return value ? JSON.parse(value) : null;
    }

    async hmset(key: string, hash: Record<string, any>): Promise<number> {
        const stringifiedHash = Object.entries(hash).reduce((acc, [field, value]) => {
            acc[field] = JSON.stringify(value);
            return acc;
        }, {});
        return await this.redisClient.hSet(key, stringifiedHash);
    }

    async hmget<T>(key: string, fields: string[]): Promise<(T | null)[]> {
        const values = await this.redisClient.hmGet(key, fields);
        return values.map(value => value ? JSON.parse(value) : null);
    }

    async hgetall<T>(key: string): Promise<Record<string, T> | null> {
        const hash = await this.redisClient.hGetAll(key);
        if (Object.keys(hash).length === 0) {
            return null;
        }
        return Object.entries(hash).reduce((acc, [field, value]) => {
            acc[field] = JSON.parse(value as string);
            return acc;
        }, {});
    }

    async hdel(key: string, fields: string | string[]): Promise<number> {
        return await this.redisClient.hDel(key, Array.isArray(fields) ? fields : [fields]);
    }

    async hexists(key: string, field: string): Promise<boolean> {
        return await this.redisClient.hExists(key, field);
    }

    async hkeys(key: string): Promise<string[]> {
        return await this.redisClient.hKeys(key);
    }

    async hvals<T>(key: string): Promise<T[]> {
        const values = await this.redisClient.hVals(key);
        return values.map(value => JSON.parse(value));
    }

    async hlen(key: string): Promise<number> {
        return await this.redisClient.hLen(key);
    }

    // Hash with TTL
    async hsetWithTtl(key: string, field: string, value: any, ttl: number): Promise<void> {
        await this.hset(key, field, value);
        await this.redisClient.expire(key, ttl);
    }

    // Batch hash operations with TTL
    async hmsetWithTtl(key: string, hash: Record<string, any>, ttl: number): Promise<void> {
        await this.hmset(key, hash);
        await this.redisClient.expire(key, ttl);
    }

    async pipeline(): Promise<ReturnType<typeof this.redisClient.multi>> {
        const pipeline = this.redisClient.multi();
        return pipeline;
    }

    async pipelineCommander(commands: RedisPipelineCommand[]): Promise<HashOperationResult<any>[]> {
        const pipeline = await this.pipeline();

        for (const [command, ...args] of commands) {
            pipeline[command.toLowerCase()](...args);
        }

        try {
            const results = await pipeline.exec();
            return results.map(result => ({ data: result }));
        } catch (error) {
            return [{ data: null, error: error as Error }];
        }
    }

    /**
     * Custom methods should be below
     */

}
