import { Types } from 'mongoose';
import { Schema } from 'mongoose';
import { DatabaseProp } from 'src/common/database/decorators/database.decorator';

export class DatabaseEntityBase {
    @DatabaseProp({
        required: false,
        // index: true,
        type: Schema.Types.ObjectId,
        default: () => new Types.ObjectId(),
    })
    _id?: Types.ObjectId;

    @DatabaseProp({
        required: false,
        index: 'asc',
        type: Date,
        default: new Date(),
    })
    createdAt?: Date;

    @DatabaseProp({
        required: false,
        index: 'asc',
        type: Date,
        default: new Date(),
    })
    updatedAt?: Date;

    @DatabaseProp({
        required: false,
        index: true,
        type: Date,
    })
    deletedAt?: Date;
}
