import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const posPolicies: IDefaultPolicies[] = [
    // POS
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.POS_POS],
        description: 'Grant full access to POS',
        isActive: true,
        module: ENUM_POLICY_MODULE.POS,
        type: ENUM_POLICY_TYPE.POS_POS,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },

    // Z-Out
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.POS_Z_OUT],
        description: 'Grant full access to Z-Out',
        isActive: true,
        module: ENUM_POLICY_MODULE.POS,
        type: E<PERSON>M_POLICY_TYPE.POS_Z_OUT,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },

    // Discount
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.POS_DISCOUNT],
        description: 'Grant full access to apply discount',
        isActive: true,
        module: ENUM_POLICY_MODULE.POS,
        type: ENUM_POLICY_TYPE.POS_DISCOUNT,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];