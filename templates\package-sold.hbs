<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Sold</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f9f9f9;
        }

        .invoice-container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            overflow: hidden;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }

        .success-message {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin: 20px 0;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }

        .info-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .table th {
            background: #8143D1;
            color: #fff;
        }

        .totals {
            text-align: right;
            margin-top: 20px;
        }

        .totals p {
            margin: 5px 0;
            font-size: 16px;
        }

        .totals .grand-total {
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="invoice-container">


        <div class="success-message">
         {{clientDetails.name}} has successfully purchased the following packages with the total amount:{{grandTotal}}
        </div>
    </div>
</body>

</html>