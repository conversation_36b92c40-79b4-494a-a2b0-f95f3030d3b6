import { Injectable, NotFoundException, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { ContactUs } from '../schema/contact-us.schema';
import { MailService } from "src/mail/services/mail.service";
import { ContactUsDemoDto } from "../dto/contact-us.dto";
import { ConfigService } from "@nestjs/config";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class ContactUsService {
    private readonly contactEmail: string;

    constructor(
        @InjectModel(ContactUs.name, DATABASE_PRIMARY_CONNECTION_NAME) private readonly ContactUsDemo: Model<ContactUs>,
        private readonly mailService: MailService,
        private readonly configService: ConfigService
    ) {
        this.contactEmail = this.configService.getOrThrow<string>("CONTACT_US_MAIL");
    }

    async createContactUs(contactusDemo: ContactUsDemoDto) {
        try {
            if (!contactusDemo || Object.keys(contactusDemo).length === 0) {
                throw new BadRequestException('Invalid request data.');
            }

            const contactus = new this.ContactUsDemo(contactusDemo);
            await contactus.save();
            await this.mailService.sendMail({
                to: this.contactEmail,
                subject: `New Demo Booking Received`,
                template: "contactUs",
                context: contactusDemo,
            });

            return contactus;
        } catch (error) {
            if (error.name === 'ValidationError') {
                throw new BadRequestException(error.message);
            }
            console.error('Error creating demo request:', error);
            throw new InternalServerErrorException('Could not process your request. Please try again later.');
        }
    }
}
