import { ApiProperty } from '@nestjs/swagger';
import { IsObject } from 'class-validator';

export class CreateLeadDto {
  @ApiProperty({
    description: 'Lead form data payload',
    type: Object,
    example: {
      name: '<PERSON>',
      email: '<EMAIL>',
      mobile: '9876543210',
      subject: 'Service Inquiry',
      message: 'Looking for a service quote',
      website: 'https://example.com/contact-us',
      form_name: 'form-contact-1'
    }
  })
  @IsObject()
  data: Record<string, any>;
}
