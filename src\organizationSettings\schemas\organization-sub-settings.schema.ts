import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'aws-sdk/clients/acm';
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Document, SchemaTypes } from 'mongoose';

@Schema({ timestamps: true })
export class OrganizationSubSettings extends Document{

    // Order number
    @Prop({ required: false, index: true, type: Number, default: 0 })
    @IsNumber()
    @IsNotEmpty()
    order: number;

    @Prop({ required: true, index: true, type: SchemaTypes.ObjectId, ref: 'users' })
    organizationId: string;

    // Parent setting id
    @Prop({ required: true, index: true, type: SchemaTypes.ObjectId, ref: 'OrganizationSettings' })
    settingId: string;

    // Other parent sub-setting id
    @Prop({ required: false, default: null, index: true, type: SchemaTypes.ObjectId, ref: OrganizationSubSettings.name })
    parentId: string;

    @Prop({ required: true, index: true, type: String })
    @IsString()
    @IsNotEmpty()
    key: string;

    @Prop({ required: true, index: true, type: Boolean })
    @IsBoolean()
    @IsNotEmpty()
    isEnabled: boolean;

    @Prop({ required: false, index: true, type: String, default: true })
    @IsString()
    @IsNotEmpty()
    groupKey: string;

    @Prop({ required: false, index: true, type: Boolean, default: true })
    @IsBoolean()
    @IsNotEmpty()
    isGroup: boolean;
  
    @Prop({ required: true, type: String })
    @IsString()
    @IsNotEmpty()
    name: string;
  
    @Prop({ type: String, default: null })
    @IsString()
    @IsOptional()
    description?: string;
  
    @Prop({ type: Boolean, default: false })
    @IsBoolean()
    isActive: boolean; // Set default value for organization

    @Prop({ type: String, default: "", required: false })
    href?: "";
  
    @Prop({type:Date,  index: true, default: undefined, required: false })
    @IsDate()
    deletedAt: Date; // remove for future and cant be enabled
  
}

export const OrganizationSubSettingsSchema = SchemaFactory.createForClass(OrganizationSubSettings);
export type OrganizationSubSettingsDocument = OrganizationSubSettings & Document;

OrganizationSubSettingsSchema.pre('save', function(next) {
    if(this.groupKey){
        this.isGroup = true;
    }
    next();
});
