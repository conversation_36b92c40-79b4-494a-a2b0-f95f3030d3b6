import { OnGatewayConnection, OnGatewayDisconnect, SubscribeMessage, WebSocketGateway, WebSocketServer } from "@nestjs/websockets";
import { Server, Socket } from "socket.io";
import { AuthService } from "src/auth/services/auth.service";
import { SocketAuthMiddlewareService } from "../middleware/socket-auth-middleware.service";
import { RoomService } from "src/room/service/room.service";
import { forwardRef, Inject } from "@nestjs/common";

@WebSocketGateway({
    cors: { origin: "*" },
    transports: ["websocket"]
})
export class WaitTimeGatewayService implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer() server: Server;

    constructor(
        private authService: AuthService,
        @Inject(forwardRef(() => RoomService))
        private roomService: RoomService,
        private readonly socketAuthMiddleware: SocketAuthMiddlewareService,
    ) { }

    afterInit(server: Server) {
        server.use(this.socketAuthMiddleware.use.bind(this.socketAuthMiddleware));
    }

    async handleConnection(socket: Socket): Promise<void> {
        try {
            const facilityId = socket?.handshake?.query?.facilityId as string;
            //console.log("Branch ID:", facilityId);

            if (!facilityId) {
                //console.log("Missing facilityId in connection request in handleConnection");
                socket.emit("error", { message: "Missing facilityId" });
                socket.disconnect();
                return;
            }
            socket.join(facilityId);
            let data = await this.roomService.roomWaitTime(facilityId)
            socket.emit("connected", { message: `Connected to branch ${facilityId}` });
            //console.log("data in handleConnection",data)

            this.server.to(facilityId).emit("updateWaitingTime", { data });

        } catch (err) {
            console.error("Connection error:", err);
            socket.disconnect();
        }
    }

    async handleDisconnect(socket: Socket): Promise<void> {
        //console.log(`Client disconnected: ${socket.id}`);
    }

    @SubscribeMessage("refresh-wait-time")
    async handleRefreshWaitTime(socket: Socket, request: any): Promise<void> {
        let facilityId = request?.facilityId
        if (!facilityId) {
            //console.log("Missing facilityId in connection request in .refresh-wait-time");
            socket.emit("error", { message: "Missing facilityId in refresh-wait-time method" });
            return;
        }
        //console.log("facilityId",facilityId)
        let data = await this.roomService.roomWaitTime(facilityId)
        //console.log("data",data)
        this.server.to(facilityId).emit("updateWaitingTime", { data });
    }

    sendWaitingTimeUpdate(facilityId: string): void {
       // console.log("facilityId",facilityId.toString())
        this.roomService.roomWaitTime(facilityId).then((data) => {
            // console.log(JSON.stringify(data))
            this.server.to(facilityId?.toString()).emit("updateWaitingTime", { data });
            // console.log(`Sent updated wait time to facility ${facilityId}`);
            // console.log(`Sent updated wait time to facility ${data}`);

        }).catch((error) => {
            console.error("Error updating wait time via socket:", error);
        });
    }
}
