// decorators/to-boolean.decorator.ts
import { Transform } from 'class-transformer';

/**
 * Transform various string/number representations to boolean.
 * - 'true', '1', 1, true => true
 * - 'false', '0', 0, false => false
 * - '' or undefined => undefined (keeps optional behavior)
 * Leaves other values untouched.
 *
 * Use with class-validator decorators (e.g. @IsBoolean()) and
 * ValidationPipe({ transform: true }).
 */
export function TransformToBoolean() {
    return Transform(({ value }) => {
        if (value === undefined || value === null || value === '') return undefined;

        // Allow already-boolean values
        if (typeof value === 'boolean') return value;

        // String handling
        if (typeof value === 'string') {
            const v = value.toLowerCase().trim();
            if (v === 'true' || v === '1') return true;
            if (v === 'false' || v === '0') return false;
        }

        // If it was an array like ['false'] (query params), try to coerce the first item
        if (Array.isArray(value) && value.length === 1) {
            const first = value[0];
            if (typeof first === 'string') {
                const v = first.toLowerCase().trim();
                if (v === 'true' || v === '1') return true;
                if (v === 'false' || v === '0') return false;
            }
        }

        // fallback: return the original value so validation can catch it
        return value;
    }, { toClassOnly: true });
}
