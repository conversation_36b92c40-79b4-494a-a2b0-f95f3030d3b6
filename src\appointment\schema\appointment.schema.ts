import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document } from 'mongoose';
import { DateRange } from "src/utils/enums/date-range-enum";

@Schema({ timestamps: true })
export class Appointment extends Document {
    @Prop({ type: SchemaTypes.ObjectId, required: true })
    bookedBy: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    trainerId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    clientId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: false })
    packageId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true })
    appointmentId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: false })
    serviceId: ObjectId;

    @Prop({ type: Boolean, required: false, default: false })
    sendConfirmation: boolean;

    @Prop({ type: String, enum: DateRange, required: true, default: DateRange.SINGLE })
    dateRange: string;

    @Prop({ type: String, required: true })
    duration: string;

    @Prop({ type: Date, required: true })
    date: Date;

    @Prop({
        type: String,
        required: false,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    from?: string;

    @Prop({
        type: String,
        required: false,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    to?: string;
    @Prop({type:String,required:false})
    notes:string
}

export const AppointmentSchema = SchemaFactory.createForClass(Appointment);

