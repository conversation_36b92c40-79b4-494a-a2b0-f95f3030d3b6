import { Controller, Get, Query } from '@nestjs/common';
import { MasterLeadService } from '../services/master-lead.service';

@Controller('api/leads')
export class MasterLeadController {
  constructor(private readonly masterLeadService: MasterLeadService) {}

  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string
  ) {
    return this.masterLeadService.findAll({ page: Number(page), limit: Number(limit), search, fromDate, toDate });
 }
}
