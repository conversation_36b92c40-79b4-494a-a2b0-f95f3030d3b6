// src/users/controllers/bulk-purchase.controller.ts
import { Body, Post, Controller } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { Response } from "src/common/response/decorators/response.decorator";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";

import { BulkCreateOrdersDto } from 'src/users/dto/order-bulk-upload.dto';
import { BulkPurchaseService } from '../services/bulk-purchase.service';
import { IDatabaseObjectId } from"src/common/database/interfaces/database.objectid.interface";

@Controller('orders')
export class BulkPurchaseController {
  constructor(
    private readonly bulkPurchaseService: BulkPurchaseService,
  ) {}

  @ApiOperation({ summary: 'Bulk purchase (multiple clients × multiple items)' })
  @Response('purchase.bulk.success')
  @Post("/bulk/v1")
  @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PURCHASE_WRITE)
  @AuthJwtAccessProtected()
  async createBulkPurchase(
    @Body() dto: BulkCreateOrdersDto,
    @GetUser() user: any,
    @GetOrganizationId() _organizationId: IDatabaseObjectId, // not needed (org comes from Clients), kept for parity
  ): Promise<any> {
    const data = await this.bulkPurchaseService.processBulk(dto, user);
    return { data };
  }
}
