import { BadRequestException, Body, Controller, Get, Headers, HttpCode, HttpException, HttpStatus, Param, Patch, Post, Req, Res, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { WidgetRegisterUserDto } from "../dto/widget-Register-dto";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { WidgetAuthService } from "../service/widget-auth.service";
import { AuthService } from "src/auth/services/auth.service";
import { Response } from "express";
import { AuthLoginResponseDto } from "src/auth/dto/response/auth.login.response.dto";
import { LoginDto } from "src/auth/dto/login.dto";
@ApiTags("widget-authentication")
@ApiBearerAuth()
@Controller("widget-auth")
export class widgetAuthenticationController {
    constructor(private widgetAuthService: WidgetAuthService,
        private authService: AuthService,
    ) { }
    @Post("/register")
    @HttpCode(201)
    @ApiOperation({ summary: "Register a new user through widget" })
    async registerUser(@Body() registerUserDto: WidgetRegisterUserDto, @Req() request: IRequestApp, @Res({ passthrough: true }) response: Response, @Headers("x-organization") organizationId: string): Promise<object> {
        console.log(registerUserDto, "reee")
        const orgId = registerUserDto?.organizationId
        const data = await this.widgetAuthService.registerUser(registerUserDto, orgId);
        //  const sessionId = await this.authService.startNewSession(request, data.user);
        //  await this.authService.setCookieData(response, data.user, sessionId.toString(), data.accessToken, data.loginDate, data.organizationId);
        return {
            message: "User registered",
            data: data,
        };
    }
    @Post('/login')
        @HttpCode(200)
        @ApiOperation({ summary: 'Login' })
        @ApiResponse({
            status: 200,
            description: 'User Login successful',
            type: AuthLoginResponseDto,
        })
        async login(
            @Body() loginDto: LoginDto,
            @Req() request: IRequestApp,
            @Res() response: Response, // Remove passthrough to handle response directly
            @Headers("x-organization") organizationId: string,
        ) {
            const data = await this.widgetAuthService.login(loginDto, null, organizationId);

            // Validate and create/update session
            const session = await this.authService.startNewSession(request, data.user);

            // Set cookies in the response
            await this.authService.setCookieData(
                response,
                data.user,
                session._id.toString(),
                data,
                data.loginDate,
                data?.organizationId?.toString()
            );

            data.user.role.policies = undefined
            data.user.assignedPolicies = undefined
            data.user.restrictedPolicies = undefined

            // Prepare response data
            const responseData = {
                message: 'User Login successful',
                data: {
                    user: {
                        ...data.user.toJSON(),
                    },
                    profiles: data.profiles,
                    roleType: data.roleType,
                    accessToken: data.accessToken,
                    tokenType: data.tokenType,
                    loginDate: data.loginDate,
                    organizationId: data.organizationId,
                    session: session._id
                } as AuthLoginResponseDto
            };

            // Send the response manually
            return response.status(200).json(responseData);
        }
}