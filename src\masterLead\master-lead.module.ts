import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MasterLeadController } from './controllers/master-lead.controller';
import { MasterLeadService } from './services/master-lead.service';
import { MasterLead, MasterLeadSchema } from './schemas/master-lead.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: MasterLead.name, schema: MasterLeadSchema }]),
  ],
  controllers: [MasterLeadController],
  providers: [MasterLeadService],
  exports: [MasterLeadService],
})
export class MasterLeadModule {}
