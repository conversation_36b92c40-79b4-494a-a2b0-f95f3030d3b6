import { ApiProperty, PickType } from "@nestjs/swagger";
import { CreatePricingDto } from "./create-pricing.dto";
import { VoucherCreateDto } from "./create-voucher.dto";
import { IsBoolean, IsMongoId, IsOptional } from "class-validator";

export class VoucherUpdateDto extends VoucherCreateDto {
    @ApiProperty({
        description: "The id of the voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Voucher id must be a valid Mongo Id" })
    voucherId: string;
}

export class VoucherStatusUpdateDto {
    @ApiProperty({
        description: "Updated status of the voucher",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Required valid isActive" })
    isActive: boolean;
}




