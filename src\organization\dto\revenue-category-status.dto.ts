import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty } from "class-validator";

export class RevenueCategoryStatusDto {
    @ApiProperty({
        description: "The active status of the revenue category",
        example: true,
        required: true,
    })
    @IsNotEmpty({ message: "isActive is required" })
    @IsBoolean({ message: "isActive must be a boolean value" })
    isActive: boolean;
}
