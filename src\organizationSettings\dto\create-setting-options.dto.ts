import { IsString, IsBoolean, Is<PERSON>otEmpty, <PERSON><PERSON>ptional, IsEnum, ValidateNested } from 'class-validator';
import { CreateSubSettingOptionsDto } from './create-sub-setting-options.dto';
import { Type } from 'class-transformer';
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { SettingOptionsKeyEnums, SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';

export class CreateSettingOptionsDto extends OmitType(CreateSubSettingOptionsDto, ['groupKey']) {
  @ApiProperty({
    description: 'Unique key for the setting option',
    example: SettingOptionsKeyEnums.CLIENT_ONBOARDING
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(SettingOptionsKeyEnums)
  key: string;

  @ApiProperty({
    type: [CreateSubSettingOptionsDto],
    required: false,
    example: [{
      key: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_WIGHT,
      groupKey: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_ASSESSMENT,
      name: 'Measurement',
      description: 'Enable/disable measurement',
      default: false
    }],
    // examples: [CreateSubSettingOptionsDto],
    description: 'Array of sub settings options',
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateSubSettingOptionsDto)
  subSettings?: CreateSubSettingOptionsDto[] = []

}
