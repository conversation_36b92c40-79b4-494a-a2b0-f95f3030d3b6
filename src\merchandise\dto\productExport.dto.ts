import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ExportProductsDto {
  @ApiProperty({ enum: ['csv', 'xlsx', 'pdf'] })
  @IsEnum(['csv', 'xlsx', 'pdf'])
  fileType: 'csv' | 'xlsx' | 'pdf';

  @ApiProperty({ enum: ['simple', 'variable', 'both'] })
  @IsEnum(['simple', 'variable', 'both'])
  productType: 'simple' | 'variable' | 'both';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  endDate?: string;

  @ApiProperty({ enum: ['stream', 'json'], required: false, default: 'stream' })
  @IsOptional()
  @IsEnum(['stream', 'json'])
  responseType?: 'stream' | 'json';
}
