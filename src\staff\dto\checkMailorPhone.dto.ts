import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class CheckMailorPhoneDto {
    @ApiProperty({
        description: "The ID of the user",
        example: "66cecb432351713ae4447a6b",
        required:false
    })
    @IsOptional()
    @IsMongoId({ message: "User ID must be a string" })
    id?: string;

    @ApiProperty({
        description: "Value should be mail or mobile",
        example: "<EMAIL>",
        required: true
    })
    @IsNotEmpty({ message: "Value is required" })
    @IsString({ message: "Value must be a string" })
    value: string;
     @ApiProperty({
        description: "The ID of the organization",
        example: "66cecb432351713ae4447a6b",
        required:false
    })
    @IsOptional()
    @IsMongoId({ message: "organizationId must be a string" })
    organizationId?: string;
}
