import {
    Body,
    ConflictException,
    Controller,
    Get,
    Param,
    Patch,
    Post,
    Put,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
    PaginationQuery,
    PaginationQueryFilterInBoolean,
    PaginationQueryFilterInEnum,
} from 'src/common/pagination/decorators/pagination.decorator';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { RequestRequiredPipe } from 'src/common/request/pipes/request.required.pipe';
import {
    Response,
    ResponsePaging,
} from 'src/common/response/decorators/response.decorator';
import {
    IResponse,
    IResponsePaging,
} from 'src/common/response/interfaces/response.interface';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import {
    PolicySuperAdminProtect,
} from 'src/policy/decorators/policy.decorator';
import {
    ROLE_DEFAULT_AVAILABLE_SEARCH
} from 'src/role/constants/role.list.constant';
import { ENUM_ROLE_STATUS_CODE_ERROR } from 'src/role/enums/role.status-code.enum';
import {
    RoleAdminActiveDoc,
    RoleAdminCreateDoc,
    RoleAdminGetDoc,
    RoleAdminInactiveDoc,
    RoleAdminListDoc,
    RoleAdminUpdateDoc,
    RoleAdminGetPoliciesDoc,
    RoleAdminGetSettingPoliciesDoc
} from 'src/role/docs/role.admin.doc';
import { RoleCreateRequestDto } from 'src/role/dtos/request/role.create.request.dto';
import { RoleUpdateRequestDto } from 'src/role/dtos/request/role.update.request.dto';
import { RoleGetResponseDto } from 'src/role/dtos/response/role.get.response.dto';
import { RoleListResponseDto } from 'src/role/dtos/response/role.list.response.dto';
import { RoleIsActivePipe } from 'src/role/pipes/role.is-active.pipe';
import { RoleParsePipe } from 'src/role/pipes/role.parse.pipe';
import { RoleDocument } from 'src/role/repository/entities/role.entity';
import { RoleService } from 'src/role/services/role.service';
import { DatabaseIdResponseDto } from 'src/common/database/dtos/response/database.id.response.dto';
import { AuthFullProtected, AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { RoleAddPolicyRequestDto } from '../dtos/request/role.add-policy.request.dto';

@ApiTags('modules.admin.role')
@ApiBearerAuth('jwt')
@Controller({
    version: '1',
    path: 'admin/role',
})
export class RoleAdminController {
    constructor(
        private readonly paginationService: PaginationService,
        private readonly roleService: RoleService
    ) {}

    @RoleAdminListDoc()
    @ResponsePaging('role.list')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/list')
    async list(
        @PaginationQuery({ availableSearch: ROLE_DEFAULT_AVAILABLE_SEARCH })
        { _search, _limit, _offset, _order }: PaginationListDto,
        // @PaginationQueryFilterInBoolean('isActive', ROLE_DEFAULT_IS_ACTIVE)
        // isActive: Record<string, any>,
        // @PaginationQueryFilterInEnum(
        //     'type',
        //     ROLE_DEFAULT_POLICY_ROLE_TYPE,
        //     ENUM_ROLE_TYPE
        // )
        // type: Record<string, any>
    ): Promise<IResponsePaging<RoleListResponseDto>> {
        const find: Record<string, any> = {
            ..._search,
            type: { $nin: [ENUM_ROLE_TYPE.SUPER_ADMIN]},
            // ...isActive,
            // ...type,
        };

        const roles: RoleDocument[] = await this.roleService.findAll(find, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });

        const total: number = await this.roleService.getTotal(find);
        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        // const mapRoles: RoleListResponseDto[] = this.roleService.mapList(roles);

        return {
            _pagination: { total, totalPage },
            data: roles as unknown as  RoleListResponseDto[],
        };
    }

    @RoleAdminGetDoc()
    @Response('role.get')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/:role/get')
    async get(
        @Param('role', RequestRequiredPipe, RoleParsePipe) role: RoleDocument
    ): Promise<IResponse<RoleGetResponseDto>> {
        const mapRole: RoleGetResponseDto = this.roleService.mapGet(role);

        return { data: role as unknown as RoleGetResponseDto };
    }

    @RoleAdminUpdateDoc()
    @Response('role.update')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Put('/:role/update')
    async update(
        @Param('role', RequestRequiredPipe, RoleParsePipe) role: RoleDocument,
        @Body()
        { description, policies: permissions, type }: RoleUpdateRequestDto
    ): Promise<IResponse<DatabaseIdResponseDto>> {
        await this.roleService.update(role, { description, policies: permissions, type });

        return {
            data: { _id: role._id },
        };
    }

    @RoleAdminInactiveDoc()
    @Response('role.inactive')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Patch('/update/:role/inactive')
    async inactive(
        @Param(
            'role',
            RequestRequiredPipe,
            RoleParsePipe,
            new RoleIsActivePipe([true])
        )
        role: RoleDocument
    ): Promise<void> {
        await this.roleService.inactive(role);

        return;
    }

    @RoleAdminActiveDoc()
    @Response('role.active')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Patch('/update/:role/active')
    async active(
        @Param(
            'role',
            RequestRequiredPipe,
            RoleParsePipe,
            new RoleIsActivePipe([false])
        )
        role: RoleDocument
    ): Promise<void> {
        await this.roleService.active(role);

        return;
    }

    @RoleAdminGetPoliciesDoc()
    @Response('role.policies')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/:role/policies')
    async getRolePolicies(
        @Param('role', RequestRequiredPipe, RoleParsePipe)
        role: RoleDocument
    ): Promise<any> {
        const data = await this.roleService.getRolePolicies(role._id);
        return {
            data: {
                policies: data.policies
            }
        };
    }

    @RoleAdminGetSettingPoliciesDoc()
    @ResponsePaging('role.setting.policies')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/:role/setting-policies')
    async getRoleSettingPolicies(
        @Param('role', RequestRequiredPipe, RoleParsePipe)
        role: RoleDocument,
        @PaginationQuery()
        { _limit, _offset }: PaginationListDto,
    ): Promise<any> {
        const { data, total } = await this.roleService.getRoleSettingPolicies(
            role._id,
            {
                paging: {
                    limit: _limit,
                    offset: _offset
                }
            }
        );

        const totalPage: number = this.paginationService.totalPage(total, _limit);

        return {
            _pagination: { total, totalPage },
            data: data //this.roleService.mapList(data)
        };
    }

    @Response('role.addPolicies')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Put('/:role/add-policies')
    async addPolicies(
        @Param('role', RequestRequiredPipe, RoleParsePipe)
        role: RoleDocument,
        @Body() { policies }: RoleAddPolicyRequestDto
    ): Promise<IResponse<DatabaseIdResponseDto>> {
        await this.roleService.addPolicies(role, policies);

        return {
            data: { _id: role._id },
        };
    }

}
