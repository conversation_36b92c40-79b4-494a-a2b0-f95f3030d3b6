import { Is<PERSON>mail, <PERSON>NotEmpty, <PERSON><PERSON>hone<PERSON><PERSON>ber, IsString, Length } from 'class-validator';
import { ApiProperty } from "@nestjs/swagger";

export class BookDemoDto {
    @ApiProperty({
        description: "Name of the Person",
        example: "<PERSON><PERSON>",
    })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({
        description: "Email of the Person who want to book the Demo",
        example: "<EMAIL>",
    })
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: "Phone No of the Person who want to book the Demo",
        example: "+91111111111",
    })
    @IsNotEmpty()
    phoneNo: string;
}

