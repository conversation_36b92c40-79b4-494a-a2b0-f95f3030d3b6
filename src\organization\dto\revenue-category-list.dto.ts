import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsMongoId, IsOptional, IsString } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";

export class RevenueCategoryListDto {
    @ApiProperty({
        description: "Search term for revenue category name",
        example: "membership",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search term must be a string" })
    search?: string;

    @ApiProperty({
        description: "Filter by active status",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean({ message: "isActive must be a boolean value" })
    isActive?: boolean;

    @ApiHideProperty()
    @IsOptional()
    organizationId?: string;

}
