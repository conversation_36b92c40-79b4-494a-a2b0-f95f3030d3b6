import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PolicyAbilityGuard } from 'src/policy/guards/policy.ability.guard';
import { PolicyRoleGuard } from 'src/policy/guards/policy.role.guard';
import { PERMISSION_META_VALIDATE_DELEGATE_USER_KEY, PERMISSIONS_META_KEY, ROLE_META_KEY } from '../constants/policy.constant';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
import { UseDelegatedUserGuard } from '../guards/policy.delegateUser.guard';


/**
 * Decorator to mark required permissions for service methods
 * @param permissions List of permissions required to execute the method
 * @returns A method decorator.
 */
export function PolicyAbilityProtected(
    ...handlers: ENUM_PERMISSION_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyAbilityGuard),
        SetMetadata(PERMISSIONS_META_KEY, handlers)
    );
}

/**
 * @deprecated Use `PolicyAbilityProtected` instead with `AuthSessionProtected(true or false)`.
 * Decorator to mark required permissions for service methods
 * @param permissions List of permissions required to execute the method
 * @returns A method decorator.
 * This will get removed in future versions.
 */
export function PolicyAbilityDelegateProtected(
    ...handlers: ENUM_PERMISSION_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyAbilityGuard),
        SetMetadata(PERMISSIONS_META_KEY, handlers),
        SetMetadata(PERMISSION_META_VALIDATE_DELEGATE_USER_KEY, true),
    );
}

/**
 * Decorator to mark required roles for service methods
 * @param roles List of roles required to execute the method
 * @returns A method decorator.
 */
export function PolicyAbilityRoleProtected(
    ...roles: ENUM_ROLE_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyRoleGuard),
        SetMetadata(ROLE_META_KEY, roles)
    );
}

/**
 * Decorator to mark required roles for service methods
 * @param roles List of roles required to execute the method
 */
export function PolicySuperAdminProtect(
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyRoleGuard),
    );
}
